#!/bin/bash

# Store the root directory in a variable
root_dir="./fingermark"
ls $root_dir > /dev/null
inroot=$?
if [ "$inroot" -gt "0" ]; then
  echo "Please move to the root directory to format all files "
  echo ""
else
	# Find all subdirectories containing files with the .tf extension
	subdirs=$(find $root_dir -type d \( -name ".terraform" -o -name "modules" \) -prune -o -type f -name "*.tf" | xargs -I{} dirname {}  | sort -u)
	# Format .tf files using terraform fmt
	for subdir in $subdirs; do
		# echo $subdir
		terraform -chdir=$subdir fmt
	done
	echo "Format has been applied, please commit any changed files "
fi