import os
import json
import requests
import boto3


def get_secret(secret_name):
    sm = boto3.client("secretsmanager")
    response = sm.get_secret_value(SecretId=secret_name)
    secret = response["SecretString"]
    return json.loads(secret)

def lambda_handler(event, context):
    # SNS event from CloudWatch alarm contains alarm details in the "Message" field
    print(f"Received event: {json.dumps(event)}")
    message = event["Records"][0]["Sns"]["Message"]
    alarm_data = json.loads(message)

    # Extract alarm name and state
    alarm_name = alarm_data["AlarmName"]
    alarm_state = alarm_data["NewStateValue"]  # "ALARM", "OK", or "INSUFFICIENT_DATA"

    # Load component map from environment variable
    component_map = json.loads(os.environ["COMPONENT_MAP"])
    component_id = component_map.get(alarm_name)
    if not component_id:
        print(f"No component_id found for alarm '{alarm_name}'. Check COMPONENT_MAP configuration.")
        return {"statusCode": 404, "body": f"No component mapped to alarm: {alarm_name}"}

    # Retrieve the Statuspage API key from Secrets Manager
    secret_data = get_secret(os.environ["STATUSPAGE_SECRET_NAME"])
    statuspage_api_key = secret_data["STATUSPAGE_API_KEY"]
    page_id = os.environ["PAGE_ID"]

    # Determine Statuspage component status based on alarm state
    if alarm_state == "ALARM":
        status = "major_outage"
    else:
        # For "OK" or "INSUFFICIENT_DATA", default to "operational"
        status = "operational"

    # Update the component on Statuspage
    url = f"https://api.statuspage.io/v1/pages/{page_id}/components/{component_id}"
    headers = {
        "Authorization": f"OAuth {statuspage_api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "component": {
            "status": status
        }
    }
    response = requests.patch(url, headers=headers, json=data)
    print(f"Updated Statuspage: {response.status_code}, {response.text}")
    return {"statusCode": response.status_code, "body": response.text}
