# Store Statuspage API key in Secrets Manager
resource "aws_secretsmanager_secret" "statuspage_api" {
  name        = var.statuspage_secret_name
  description = "Secret containing Statuspage API key"
  tags        = var.tags
}

resource "aws_secretsmanager_secret_version" "statuspage_api_version" {
  secret_id = aws_secretsmanager_secret.statuspage_api.id
  secret_string = jsonencode({
    STATUSPAGE_API_KEY = var.statuspage_api_key
  })
}

# IAM Role for Lambda
resource "aws_iam_role" "lambda_role" {
  name = "${var.project_name}-lambda-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = { Service = "lambda.amazonaws.com" }
      }
    ]
  })
  tags = var.tags
}

# IAM Policy Document for Lambda Permissions
data "aws_iam_policy_document" "lambda_policy" {
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]
    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      aws_secretsmanager_secret.statuspage_api.arn
    ]
  }
}

resource "aws_iam_policy" "lambda_policy" {
  name        = "${var.project_name}-lambda-policy"
  description = "Policy for Lambda to access logs and secrets"
  policy      = data.aws_iam_policy_document.lambda_policy.json
}

resource "aws_iam_role_policy_attachment" "lambda_policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

# CloudWatch Log Group for Lambda function logs
resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name              = "/aws/lambda/${var.project_name}-lambda-log-group"
  retention_in_days = 7
  tags              = var.tags

  lifecycle { # allow overwrite by cw_log_retention module
    ignore_changes = [retention_in_days]
  }
}

# SNS Topic for alarm notifications
resource "aws_sns_topic" "lambda_topic" {
  name = "${var.project_name}-alarm-topic"
  tags = var.tags
}

# Lambda function package and environment
resource "aws_lambda_function" "update_statuspage" {
  function_name = "${var.project_name}-lambda-function"
  role          = aws_iam_role.lambda_role.arn
  handler       = "lambda_function.lambda_handler"
  runtime       = "python3.11"

  filename         = "${path.module}/lambda/function.zip"
  source_code_hash = filebase64sha256("${path.module}/lambda/function.zip")

  environment {
    variables = {
      STATUSPAGE_SECRET_NAME = var.statuspage_secret_name
      PAGE_ID                = var.page_id
      COMPONENT_MAP          = jsonencode(local.component_map)
    }
  }
  tags       = var.tags
  depends_on = [aws_iam_role_policy_attachment.lambda_policy_attachment]
}

# Allow SNS to invoke the Lambda function
resource "aws_lambda_permission" "allow_sns" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.update_statuspage.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.lambda_topic.arn
}

# Subscribe Lambda function to SNS topic
resource "aws_sns_topic_subscription" "lambda_subscription" {
  topic_arn  = aws_sns_topic.lambda_topic.arn
  protocol   = "lambda"
  endpoint   = aws_lambda_function.update_statuspage.arn
  depends_on = [aws_lambda_permission.allow_sns]
}

# Create Route53 Health Checks dynamically based on var.checks
resource "aws_route53_health_check" "checks" {
  for_each = var.checks

  fqdn              = each.value.endpoint
  port              = 443
  type              = "HTTPS"
  resource_path     = "/"
  request_interval  = 30
  failure_threshold = 3
  tags              = var.tags
}

# CloudWatch Alarms for each health check
resource "aws_cloudwatch_metric_alarm" "checks" {
  for_each            = var.checks
  alarm_name          = each.key
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "HealthCheckStatus"
  namespace           = "AWS/Route53"
  period              = 60
  statistic           = "Minimum"
  threshold           = 1
  dimensions = {
    HealthCheckId = aws_route53_health_check.checks[each.key].id
  }
  alarm_description = "Alarm when ${each.key} fails"
  ok_actions        = [aws_sns_topic.lambda_topic.arn]
  alarm_actions     = [aws_sns_topic.lambda_topic.arn]
  tags              = var.tags
}
