variable "project_name" {
  type    = string
  default = "statuspage-updater"
}

variable "page_id" {
  type    = string
  default = "fmcvb5fd17fx"
}

variable "statuspage_api_key" {
  type      = string
  sensitive = true
  default   = "44bc09fc004743748f71cd5e34b86b46"
}

variable "statuspage_secret_name" {
  type    = string
  default = "statuspage-api-secret"
}

variable "checks" {
  type = map(object({
    endpoint     = string
    component_id = string
  }))
  default = {
    "eyecue-dashboard-health-check" = {
      endpoint     = "www.eyecuedashboard.com"
      component_id = "xcs8vsps920w"
    },
    "supernova-dashboard-health-check" = {
      endpoint     = "cms.eyecuedashboard.com"
      component_id = "h50w7lr7n9sj"
    }
  }
}

variable "tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "True"
    Squad       = "Platform"
    Environment = "Production"
    Product     = "Eyecue"
  }
}

variable "statuspage_custom_domain" {
  description = "Custom domain to be used for the Atlassian status page"
  type        = string
  default     = "status.example.com" // Change this to your chosen domain/subdomain.
}

variable "atlassian_statuspage_target" {
  description = "The Atlassian-provided target hostname for the custom domain (e.g. custom.atlassianstatuspage.io)"
  type        = string
  default     = "custom.atlassianstatuspage.io" // Replace with the actual value from Atlassian.
}
