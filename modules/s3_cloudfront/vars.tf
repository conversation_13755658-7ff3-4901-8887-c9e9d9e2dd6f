variable "s3_bucket_name" {
  type        = string
  description = "Name of the S3 Bucket"
}

variable "s3_website" {
  type        = map(any)
  description = "S3 Bucket - Website configuration"
  default     = {}
}

variable "error_response_cachettl" {
  type        = string
  description = "Cloudfront - Error Page - Cache TTL"
  default     = ""
}

variable "block_public_acls" {
  type        = string
  description = "S3 Block public policy"
  default     = false
}

variable "block_public_policy" {
  type        = string
  description = "S3 Block public policy"
  default     = false
}
variable "error_response_errorcode" {
  type        = string
  description = "Cloudfront - Error Page - Error Code"
  default     = ""
}
variable "origin_path" {
  type        = string
  description = "Cloudfront - Origin path"
  default     = ""
}
variable "error_response_responsecode" {
  type        = string
  description = "Cloudfront - Error Page - Response COde"
  default     = ""
}
variable "error_response_pagepath" {
  type        = string
  description = "Cloudfront - Error Page - Page Path"
  default     = ""
}

variable "cloudfront_logging_config" {
  type        = map(any)
  description = "Cloudfront - Logging Config"
  default     = {}
}

variable "cloudfront_aliases" {
  type        = list(any)
  description = "Cloudfront - Aliases list"
  default     = []
}

variable "domain_name" {
  type        = string
  description = "S3 Bucket - Website configuration"
}

variable "cloudfront_priceclass" {
  type        = string
  description = "Cloudfront - Price class"
  default     = "PriceClass_200"
}

variable "cloudfront_acm_arn" {
  type        = string
  description = "Cloudfront - ACM ARN"
}

variable "cloudfront_ssl_supportmethod" {
  type        = string
  description = "Cloudfront - SSL SUpport Method"
  default     = "sni-only"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "cloudfront_root_object" {
  type        = string
  description = "Cloudfront - Root Object"
  default     = "index.html"
}

variable "cloudfront_restriction_type" {
  type        = string
  description = "Cloudfront - Restriction Type"
  default     = "none"
}

variable "cloudfront_restriction_locations" {
  type        = list(any)
  description = "Cloudfront - Restriction Location"
  default     = []
}

variable "ignore_public_acls" {
  type        = string
  description = "Cloudfront ignore public ACLs"
  default     = true
}

variable "restrict_public_buckets" {
  type        = string
  description = "Cloudfront restrict public access"
  default     = true
}
