resource "aws_s3_bucket" "vpc_flow_logs" {
  bucket = var.bucket_name
  tags   = var.tags
}

resource "aws_s3_bucket_lifecycle_configuration" "vpc_flow_logs" {
  bucket = aws_s3_bucket.vpc_flow_logs.id

  depends_on = [
    aws_s3_bucket.vpc_flow_logs,
  ]

  rule {
    id     = "transition-to-glacier"
    status = "Enabled"

    filter {
      prefix = "" # Apply to all objects
    }

    transition {
      days          = 30
      storage_class = "GLACIER_IR"
    }
  }

  rule {
    id     = "expire-old-logs"
    status = "Enabled"

    filter {
      prefix = "" # Apply to all objects
    }

    expiration {
      days = 365
    }
  }
}

resource "aws_s3_bucket_versioning" "vpc_flow_logs" {
  bucket = aws_s3_bucket.vpc_flow_logs.id

  depends_on = [
    aws_s3_bucket.vpc_flow_logs,
  ]

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "vpc_flow_logs" {
  bucket = aws_s3_bucket.vpc_flow_logs.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "vpc_flow_logs" {
  depends_on = [
    aws_s3_bucket.vpc_flow_logs,
    aws_s3_bucket_ownership_controls.vpc_flow_logs,
  ]

  bucket = aws_s3_bucket.vpc_flow_logs.id
  acl    = "private"
}

resource "aws_s3_bucket_policy" "vpc_flow_logs_policy" {
  bucket = aws_s3_bucket.vpc_flow_logs.id

  depends_on = [
    aws_s3_bucket.vpc_flow_logs,
    aws_s3_bucket_acl.vpc_flow_logs,
  ]

  policy = data.aws_iam_policy_document.vpc_flow_logs_policy.json
}
