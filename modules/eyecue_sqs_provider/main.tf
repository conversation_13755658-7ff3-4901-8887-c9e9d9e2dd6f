module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

module "s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source                   = "terraform-aws-modules/s3-bucket/aws"
  version                  = "3.14.0"
  bucket                   = "eyecue-${var.client_name}-${var.country}-${var.service_name}"
  acl                      = "private"
  block_public_policy      = true
  block_public_acls        = true
  restrict_public_buckets  = true
  ignore_public_acls       = true
  control_object_ownership = true
  object_ownership         = "BucketOwnerPreferred"
  versioning = {
    enabled = var.enable_versioning
  }
  lifecycle_rule = [
    {
      id      = "Delete older than 7 days"
      enabled = true
      filter = {
        prefix = ""
      }
      abort_incomplete_multipart_upload_days = 7
      expiration = {
        days = 7
      }
      noncurrent_version_expiration = {
        days = 7
      }
    }
  ]
  tags = merge(var.tags, { "Name" = "eyecue-${var.client_name}-${var.country}-${var.service_name}" })
}

data "aws_iam_policy_document" "eyecue_policy_document" {
  statement {
    sid = "Eyecue${var.service_name}PolicyDocument"
    actions = [
      "s3:PutObject",
      "s3:ListBucket",
      "s3:GetObject",
    ]
    resources = [
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-${var.service_name}",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-${var.service_name}/*",
    ]
  }
  statement {
    sid = "Eyecue${var.service_name}PolicyDocumentSQS"
    actions = [
      "sqs:CreateQueue",
      "sqs:DeleteMessage",
      "sqs:GetQueueUrl",
      "sqs:ReceiveMessage",
      "sqs:SendMessage",
      "sqs:ListQueues",
      "sqs:SetQueueAttributes",
      "sqs:TagQueue"
    ]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.aws_account_id}:fm-*-${var.service_name}"
    ]
  }
  statement {
    sid = "Eyecue${var.service_name}PolicyDocumentSNS"
    actions = [
      "sns:CreateTopic",
      "sns:GetTopicAttributes",
      "sns:SetTopicAttributes",
      "sns:Subscribe",
      "sns:Unsubscribe",
      "sns:ListSubscriptionsByTopic",
      "sns:Publish",
      "sns:DeleteTopic",
      "sns:TagResource"
    ]
    resources = [
      "arn:aws:sns:${var.aws_region}:${var.aws_account_id}:fm-*-${var.service_name}"
    ]
  }
  statement {
    sid = "Eyecue${var.service_name}PolicyDocumentCloudWatch"
    actions = [
      "cloudwatch:PutMetricAlarm",
      "cloudwatch:DescribeAlarms",
      "cloudwatch:DeleteAlarms",
      "cloudwatch:GetMetricData",
      "cloudwatch:GetMetricStatistics",
      "cloudwatch:ListMetrics",
      "cloudwatch:TagResource"
    ]
    resources = [
      "arn:aws:cloudwatch:${var.aws_region}:${var.aws_account_id}:alarm:fm-*-${var.service_name}"
    ]
  }
}

resource "aws_iam_policy" "eyecue_policy" {
  name        = "Eyecue${var.service_name}Policy"
  depends_on  = [module.iam_user]
  path        = "/"
  description = ""

  policy = data.aws_iam_policy_document.eyecue_policy_document.json
}

resource "aws_iam_user_policy_attachment" "policy_attachment" {
  user       = var.aws_iam_user
  policy_arn = aws_iam_policy.eyecue_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
