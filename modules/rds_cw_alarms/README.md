# Module: RDS CloudWatch Alarms

Deploys CloudWatch alarms for RDS provided CloudWatch metrics. The RDS instance must exist for the alarms to be created. CloudWatch Alarms notifications can be optionally sent to emails using a SNS topic.

For available RDS instance CloudWatch metrics see:
https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/rds-metrics.html

For available RDS Aurora instance CloudWatch metrics see:
https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Aurora.AuroraMonitoring.Metrics.html

## Example usage

```hcl
module "rds_cw_alarms" {
  source         = "./modules/rds_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]

  # RDS All
  cw_alarm_config_rds_cpu_util = { "my-rds-instance-1" = { identifier = "my-rds-instance-1" } }
  cw_alarm_config_rds_mem_free = {
    "my-rds-instance-1" = { identifier = "my-rds-instance-1" }
    "my-rds-instance-2" = { identifier = "my-rds-instance-2" }
  }
  cw_alarm_config_rds_disk_queue_depth = {
    "my-rds-instance-with-custom-thresholds" = {
      identifier         = "my-rds-instance-with-custom-thresholds"
      evaluation_periods = 3
      period_seconds     = 120
      statistic          = "Average"
      threshold          = 20
      alarm_description  = "Custom description"

    }
  }
  cw_alarm_config_rds_write_iops = { "DIFFERENT-CW-ALARM-NAME-HERE" = { identifier = "my-rds-instance-1" } }
  cw_alarm_config_rds_read_iops = {
    "alarm-notification-disabled" = {
      identifier          = "my-rds-instance-1"
      enable_notification = false
    }
  }

  # RDS Only (not Aurora)
  cw_alarm_config_rds_free_storage_space = {}

  # RDS Aurora Only
  cw_alarm_config_rds_aurora_volume_write_iops = { "my-rds-aurora-instance-1" = { identifier = "my-rds-aurora-instance-1" } }
  cw_alarm_config_rds_aurora_volume_read_iops  = {}

  # RDS NVMe SSD instance Only
  cw_alarm_config_rds_free_local_storage = { "my-fast-local-disk-instance-1" = { identifier = "my-fast-local-disk-instance-1" } }

  # RDS Aurora MySQL Only
  cw_alarm_config_rds_aurora_mysql_aurora_volume_bytes_left_total = {}
}
```