locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# RDS: Read ensuring existing RDS instances
# =====
locals {
  db_identifers = {
    rds_cpu_util                 = [for config in values(var.cw_alarm_config_rds_cpu_util) : config.identifier]
    rds_mem_free                 = [for config in values(var.cw_alarm_config_rds_mem_free) : config.identifier]
    rds_disk_queue_depth         = [for config in values(var.cw_alarm_config_rds_disk_queue_depth) : config.identifier]
    rds_write_iops               = [for config in values(var.cw_alarm_config_rds_write_iops) : config.identifier]
    rds_read_iops                = [for config in values(var.cw_alarm_config_rds_read_iops) : config.identifier]
    rds_aurora_volume_write_iops = [for config in values(var.cw_alarm_config_rds_aurora_volume_write_iops) : config.identifier]
    rds_aurora_volume_read_iops  = [for config in values(var.cw_alarm_config_rds_aurora_volume_read_iops) : config.identifier]
  }
  all_distinct_db_identifiers_list = distinct(flatten([
    local.db_identifers.rds_cpu_util,
    local.db_identifers.rds_mem_free,
    local.db_identifers.rds_disk_queue_depth,
    local.db_identifers.rds_write_iops,
    local.db_identifers.rds_read_iops,
    local.db_identifers.rds_aurora_volume_write_iops,
    local.db_identifers.rds_aurora_volume_read_iops,
  ]))
  all_distinct_identifiers_set = toset(local.all_distinct_db_identifiers_list)
}

data "aws_db_instance" "this" {
  for_each = local.all_distinct_identifiers_set

  db_instance_identifier = each.value
}

# =====
# CloudWatch Alarms: RDS CPU
# =====
locals {
  rds_cpu_util_alarm = {
    name_prefix = "rds-cpu-utilization"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_cpu_util_alarm" {
  for_each = var.cw_alarm_config_rds_cpu_util

  alarm_name = "${local.rds_cpu_util_alarm.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "CPU utilization threshold exceeded for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_cpu_util_alarm.name_prefix}-${each.key}"
    }
  )
}

# =====
# CloudWatch Alarms: RDS Memory
# =====
locals {
  rds_mem_free_alarm = {
    name_prefix = "rds-memory-free"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_mem_free_alarm" {
  for_each = var.cw_alarm_config_rds_mem_free

  alarm_name = "${local.rds_mem_free_alarm.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Memory free has dropped below threshold for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "FreeableMemory"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_mem_free_alarm.name_prefix}-${each.key}"
    }
  )
}

# =====
# CloudWatch Alarms: RDS IO
# =====
locals {
  rds_disk_queue_depth = {
    name_prefix = "rds-disk-queue-depth"
  }
  rds_write_iops = {
    name_prefix = "rds-write-iops"
  }
  rds_read_iops = {
    name_prefix = "rds-read-iops"
  }
}


resource "aws_cloudwatch_metric_alarm" "rds_disk_queue_depth" {
  for_each = var.cw_alarm_config_rds_disk_queue_depth

  alarm_name = "${local.rds_disk_queue_depth.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Disk queue depth threshold exceeded for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "DiskQueueDepth"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_disk_queue_depth.name_prefix}-${each.key}"
    }
  )
}


resource "aws_cloudwatch_metric_alarm" "rds_write_iops" {
  for_each = var.cw_alarm_config_rds_write_iops

  alarm_name = "${local.rds_write_iops.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Write IOPS threshold exceeded for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "WriteIOPS"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_write_iops.name_prefix}-${each.key}"
    }
  )
}


resource "aws_cloudwatch_metric_alarm" "rds_read_iops" {
  for_each = var.cw_alarm_config_rds_read_iops

  alarm_name = "${local.rds_read_iops.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Read IOPS threshold exceeded for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "ReadIOPS"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_read_iops.name_prefix}-${each.key}"
    }
  )
}

# =====
# CloudWatch Alarms: RDS Aurora IO
# =====
locals {
  rds_aurora_volume_write_iops = {
    name_prefix = "rds-aurora-volume-write-iops"
  }
  rds_aurora_volume_read_iops = {
    name_prefix = "rds-aurora-volume-read-iops"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_aurora_volume_write_iops" {
  for_each = var.cw_alarm_config_rds_aurora_volume_write_iops

  alarm_name = "${local.rds_aurora_volume_write_iops.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Write IOPS threshold exceeded for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "VolumeWriteIOPs"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_aurora_volume_write_iops.name_prefix}-${each.key}"
    }
  )
}

resource "aws_cloudwatch_metric_alarm" "rds_aurora_volume_read_iops" {
  for_each = var.cw_alarm_config_rds_aurora_volume_read_iops

  alarm_name = "${local.rds_aurora_volume_read_iops.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Read IOPS threshold exceeded for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "VolumeReadIOPs"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_aurora_volume_read_iops.name_prefix}-${each.key}"
    }
  )
}

# =====
# CloudWatch Alarms: RDS Storage
# =====
locals {
  rds_free_storage_space = {
    name_prefix = "rds-free-storage-space"
  }
  rds_free_local_storage = {
    name_prefix = "rds-free-local-storage"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_free_storage_space" {
  for_each = var.cw_alarm_config_rds_free_storage_space

  alarm_name = "${local.rds_free_storage_space.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Free storage space has dropped below threshold for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "FreeStorageSpace"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_free_storage_space.name_prefix}-${each.key}"
    }
  )
}

resource "aws_cloudwatch_metric_alarm" "rds_free_local_storage" {
  for_each = var.cw_alarm_config_rds_free_local_storage

  alarm_name = "${local.rds_free_local_storage.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Free local storage has dropped below threshold for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "FreeLocalStorage"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_free_local_storage.name_prefix}-${each.key}"
    }
  )
}

# =====
# CloudWatch Alarms: RDS Aurora MySQL Storage
# =====
locals {
  rds_aurora_mysql_aurora_volume_bytes_left_total = {
    name_prefix = "rds-aurora-mysql-aurora-volume-bytes-left-total"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_aurora_mysql_aurora_volume_bytes_left_total" {
  for_each = var.cw_alarm_config_rds_aurora_mysql_aurora_volume_bytes_left_total

  alarm_name = "${local.rds_aurora_mysql_aurora_volume_bytes_left_total.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Aurora volume bytes left total has dropped below threshold for RDS instance ${each.value.identifier}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "AuroraVolumeBytesLeftTotal"
  namespace           = "AWS/RDS"
  period              = each.value.period_seconds
  statistic           = each.value.statistic
  threshold           = each.value.threshold
  alarm_actions       = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions          = each.value.enable_notification ? var.sns_topic_arns : null

  dimensions = {
    DBInstanceIdentifier = data.aws_db_instance.this[each.value.identifier].id
  }

  tags = merge(
    local.tags,
    {
      Name = "${local.rds_aurora_mysql_aurora_volume_bytes_left_total.name_prefix}-${each.key}"
    }
  )
}
