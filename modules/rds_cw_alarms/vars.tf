# =====
# SNS Topic: CloudWatch Alarms for EC2 instance
# =====
variable "sns_topic_arns" {
  description = <<-DOC
    [Optional] List of existing SNS topic ARNs to use for sending CloudWatch Alarm notifications
    for EC2 instances.

    It is recommended to create a centralised SNS topic for multiple CloudWatch Alarm notifications
    using the module `cw_alarm_notifications_sns_topic`:
    https://bitbucket.org/fingermarkltd/fingermark-terraform/src/master/modules/cw_alarm_notifications_sns_topic/
  DOC
  type        = list(string)
  default     = []
}

# =====
# CloudWatch Alarms: RDS CPU
# =====
variable "cw_alarm_config_rds_cpu_util" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS instances CPU utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: CPUUtilization
    Comparison Operator: GreaterThanThreshold
    Applies to: All
    
    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Instance identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 120) # 2 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 80) # 80%
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: RDS Memory
# =====
variable "cw_alarm_config_rds_mem_free" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS instances memory utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: FreeableMemory
    Comparison Operator: LessThanThreshold
    Applies to: All

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Instance identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 120) # 2 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 100000000) # 100MB
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: RDS IO
# =====
variable "cw_alarm_config_rds_disk_queue_depth" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS instances IO utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: DiskQueueDepth
    Comparison Operator: GreaterThanThreshold
    Applies to: All

    A general guideline is to target a queue length of 1 for every 1000 IOPS available to your
    instance.

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Instance identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 2)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 10) # Adjust based on your IOPS
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_rds_write_iops" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS instances IO utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: WriteIOPS
    Comparison Operator: GreaterThanThreshold
    Applies to: All

    A general guideline is to target 80% of your IOPS limit. GP3 has a minimum of 3000 IOPS.

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Instance identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 2400) # Adjust based on your IOPS
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_rds_read_iops" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS instances IO utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: ReadIOPS
    Comparison Operator: GreaterThanThreshold
    Applies to: All

    A general guideline is to target 80% of your IOPS limit. GP3 has a minimum of 3000 IOPS.

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Instance identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 2400) # Adjust based on your IOPS
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: RDS Aurora IO
# =====
variable "cw_alarm_config_rds_aurora_volume_write_iops" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS Aurora instances IO utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: VolumeWriteIOPs
    Comparison Operator: GreaterThanThreshold
    Applies to: Aurora

    A general guideline is to target 80% of your IOPS limit. Aurora can handle up to 200,000 write
    IOPS per cluster volume.

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Cluster identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 160000) # Adjust based on your IOPS
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_rds_aurora_volume_read_iops" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS Aurora instances IO utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: VolumeReadIOPs
    Comparison Operator: GreaterThanThreshold
    Applies to: Aurora

    A general guideline is to target 80% of your IOPS limit. Aurora can handle up to 200,000 read
    IOPS per cluster volume.

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Cluster identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 160000) # Adjust based on your IOPS
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: RDS Storage
# =====
variable "cw_alarm_config_rds_free_storage_space" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS instances storage utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: FreeStorageSpace
    Comparison Operator: LessThanThreshold
    Applies to: RDS only (not Aurora)

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Instance identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 500000000) # 500 MB
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_rds_free_local_storage" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS Aurora instances storage utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: FreeLocalStorage
    Comparison Operator: LessThanThreshold
    Applies to: RDS NVMe SSD instance

    The FreeLocalStorage metric is specific to RDS instance classes that have NVMe SSD instance
    store volumes (like db.m6gd and db.r6gd instance classes).

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Cluster identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 100000000) # 100 MB
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: RDS Aurora MySQL Storage
# =====
variable "cw_alarm_config_rds_aurora_mysql_aurora_volume_bytes_left_total" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for RDS Aurora instances storage utlization.
    CloudWatch Alarm Namespace: AWS/RDS
    CloudWatch Alarm Metric: AuroraVolumeBytesLeftTotal
    Comparison Operator: LessThanThreshold
    Applies to: Aurora MySQL

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `identifier` - [Required] RDS DB Cluster identifier.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    identifier          = string
    evaluation_periods  = optional(number, 3)
    period_seconds      = optional(number, 300) # 5 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 1000000000) # 1 GB
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
