# Eyecue RDS Module

Terraform module for creating RDS PostgreSQL instances with configurable security group access control.

## Features

- PostgreSQL RDS instance with optional read replica
- Configurable security group access (open access or AWS IP prefix list)
- CloudFlare DNS records for master and replica endpoints
- IAM authentication support

## Security Group Management

This module provides two security modes controlled by the `enable_prefix_list` variable:

### Default Mode (enable_prefix_list = false)
- Uses 0.0.0.0/0 for RDS access (temporary solution)
- Recommended for most deployments to avoid hitting AWS SG rule limits
- Simple and doesn't require quota increases

### Prefix List Mode (enable_prefix_list = true)
- Fetches AWS IP ranges dynamically from `https://ip-ranges.amazonaws.com/ip-ranges.json`
- Creates managed prefix list with EC2 IPs from current region + us-east-1
- Includes corporate/office IPs in the prefix list
- **Only enable for customers with increased SG rule limits**

## Usage

### Basic Usage (Default - Open Access)

```hcl
module "eyecue_rds" {
  source = "../../../modules/eyecue_rds"

  # Basic Configuration
  aws_region     = "ap-southeast-2"
  aws_account_id = data.aws_caller_identity.current.account_id
  vpc_id         = module.network.vpc_id
  subnet_ids     = module.network.public_subnet_ids

  # RDS Configuration
  rds_instance_identifier   = "eyecue-postgres"
  rds_engine_version        = "16.8"
  rds_master_instance_class = "db.t3.medium"
  rds_storage_encrypted     = true  # Required for SOC2 compliance
  
  # Security defaults to 0.0.0.0/0 (enable_prefix_list = false by default)
}
```

### Advanced Usage (With Prefix List - For Customers with Increased Limits)

```hcl
module "eyecue_rds" {
  source = "../../../modules/eyecue_rds"

  # Basic Configuration
  aws_region     = "ap-southeast-2"
  aws_account_id = data.aws_caller_identity.current.account_id
  vpc_id         = module.network.vpc_id
  subnet_ids     = module.network.public_subnet_ids

  # RDS Configuration
  rds_instance_identifier   = "eyecue-postgres"
  rds_engine_version        = "16.8"
  rds_master_instance_class = "db.t3.medium"
  rds_storage_encrypted     = true

  # Enable prefix list mode (only for customers with increased SG limits)
  enable_prefix_list = true
  
  # Optional: Add specific additional CIDR blocks
  additional_cidr_blocks = ["10.0.0.0/8"]
}
```

## Variables

### Required

- `aws_region` - AWS region for the RDS instance
- `aws_account_id` - AWS account ID
- `vpc_id` - VPC ID for the RDS instance
- `subnet_ids` - List of subnet IDs for RDS subnet group

### Security Configuration

- `enable_prefix_list` - Enable AWS prefix list for security group rules (default: false - uses 0.0.0.0/0)
- `additional_cidr_blocks` - Additional CIDR blocks to allow access to RDS (default: [])

### RDS Configuration

- `rds_instance_identifier` - RDS instance identifier (default: "master-postgres")
- `rds_engine_version` - PostgreSQL version (default: "12.17")
- `rds_master_instance_class` - Instance class for master (default: "db.t3.medium")
- `rds_storage_encrypted` - Enable storage encryption (default: true)
- `create_replica` - Create read replica (default: false)

## Outputs

### Security Group Information

```hcl
aws_ip_configuration = {
  prefix_list_enabled = false
  region             = "ap-southeast-2"
  security_group_id  = "sg-xxx"
  security_mode      = "open_0.0.0.0/0"  # or "prefix_list" when enabled
  prefix_list_id     = null  # populated when prefix_list is enabled
}
```

### RDS Endpoints

- `master_db_instance_address` - Master RDS endpoint
- `master_db_instance_id` - Master RDS instance ID
- `replica_db_instance_address` - Replica RDS endpoint (if enabled)

## Migration Guide

### Migrating from Prefix List to Open Access (Default)

If you're experiencing SG rule limit issues:

1. Set `enable_prefix_list = false` (or remove it, as false is the default)
2. Run `terraform plan` to see the changes
3. Apply the changes - this will switch to 0.0.0.0/0 access

### Migrating from Open Access to Prefix List

Only after AWS quota increase:

1. Request AWS quota increase for security group rules
2. Set `enable_prefix_list = true` in your module configuration
3. Run `terraform plan` to see the changes
4. Apply the changes - this will create the prefix list and restrict access

## Troubleshooting

### RulesPerSecurityGroupLimitExceeded Error

If you encounter this error with prefix list enabled:

1. **Immediate fix**: Set `enable_prefix_list = false` to use 0.0.0.0/0
2. **Long-term fix**: Request AWS quota increase, then enable prefix list

### Security Considerations

- **Default mode (0.0.0.0/0)**: Temporary solution, document as exception for SOC2
- **Prefix list mode**: More secure but requires AWS limit increases
- Always ensure RDS encryption is enabled for compliance
