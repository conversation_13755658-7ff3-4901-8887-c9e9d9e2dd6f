data "aws_caller_identity" "current" {}

# Dynamic IP restriction for SOC2
# Note: This is fairly open but we're about to decommission these
data "aws_ip_ranges" "aws_ips" {
  regions  = [var.aws_region]
  services = var.aws_ip_ranges_services
}

# Generate random password for master instance
resource "random_string" "master_password" {
  count   = var.create_random_password ? 1 : 0
  length  = 32
  special = var.special_password
}

module "master" {
  # https://github.com/terraform-aws-modules/terraform-aws-rds
  source  = "terraform-aws-modules/rds/aws"
  version = "5.2.0"

  identifier     = var.rds_instance_identifier
  engine         = var.rds_engine
  engine_version = var.rds_engine_version

  instance_class        = var.rds_master_instance_class
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_type          = var.rds_storage_type
  storage_encrypted     = var.rds_storage_encrypted
  iops                  = var.rds_master_iops

  db_name                = var.rds_name
  username               = var.rds_username
  password               = var.create_random_password ? random_string.master_password[0].result : null
  port                   = var.rds_port
  create_random_password = var.rds_create_random_password

  publicly_accessible    = var.db_publicly_accessible
  vpc_security_group_ids = concat(var.vpc_security_group_ids, [aws_security_group.main_sec_group.id])
  subnet_ids             = var.subnet_ids

  maintenance_window      = var.rds_maintenance_window
  backup_window           = var.rds_backup_window
  backup_retention_period = var.rds_backup_retention_period
  skip_final_snapshot     = var.rds_skip_final_snapshot
  deletion_protection     = var.deletion_protection
  apply_immediately       = var.rds_apply_changes_immediately

  create_db_option_group          = false
  create_db_subnet_group          = var.rds_create_db_subnet_group
  db_subnet_group_name            = var.db_subnet_group_name
  db_subnet_group_use_name_prefix = var.db_subnet_group_use_name_prefix
  timeouts                        = var.rds_timeout

  create_db_parameter_group       = var.create_db_parameter_group
  parameter_group_description     = var.parameter_group_description
  parameter_group_name            = var.parameter_group_name
  parameter_group_use_name_prefix = var.parameter_group_use_name_prefix
  family                          = var.parameter_group_family
  parameters                      = var.enable_logical_replication ? concat(var.parameter_group_parameters, [
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "wal_sender_timeout"
      value        = "30000"
      apply_method = "immediate"
    }
  ]) : var.parameter_group_parameters

  performance_insights_enabled        = var.rds_performance_insights_enabled
  allow_major_version_upgrade         = var.allow_major_version_upgrade
  iam_database_authentication_enabled = true

  ca_cert_identifier = var.rds_ca_cert_identifier
  tags               = var.rds_master_tags
}

module "replica" {
  create_db_instance = var.create_replica
  source             = "terraform-aws-modules/rds/aws"
  version            = "5.2.0"
  identifier         = "${var.rds_instance_identifier}-replica"

  ### Source database. For cross-region use this_db_instance_arn
  replicate_source_db = "master-postgres"
  # replicate_source_db = module.master.db_instance_id

  engine                = var.rds_engine
  engine_version        = var.rds_engine_version
  instance_class        = var.rds_replica_instance_class
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_encrypted     = true
  publicly_accessible   = var.db_publicly_accessible
  storage_type          = var.rds_storage_type
  iops                  = var.rds_replica_iops
  skip_final_snapshot   = true
  ### Username and password must not be set for replicas
  username = ""
  password = ""
  port     = var.rds_port

  iam_database_authentication_enabled = true

  vpc_security_group_ids = concat(var.vpc_security_group_ids, [aws_security_group.main_sec_group.id])
  maintenance_window     = "Tue:00:00-Tue:03:00"
  backup_window          = "03:00-06:00"
  apply_immediately      = var.rds_apply_changes_immediately
  ### disable backups to create DB faster

  backup_retention_period = 0
  ### Not allowed to specify a subnet group for replicas in the same region
  create_db_subnet_group    = false
  create_db_option_group    = false
  create_db_parameter_group = false

  ca_cert_identifier = var.rds_ca_cert_identifier
  tags               = var.rds_replica_tags
}

resource "aws_iam_role" "eyecue_admin_role" {
  name = "EyecueRdsAdmin"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Condition = {
        StringLike = {
          "api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc:sub" = "*"
        }
      },
      Action = "sts:AssumeRoleWithWebIdentity",
      Principal = {
        Federated = "arn:aws:iam::${var.aws_account_id}:oidc-provider/api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc"
      }
    }]
  })
  tags = var.tags
}

data "aws_iam_policy_document" "eyecue_admin_policy" {
  statement {
    actions = [
      "rds-db:connect",
    ]
    resources = ["arn:aws:rds-db:${var.aws_region}:${var.aws_account_id}:dbuser:*/eyecue_admin"]
  }
}

resource "aws_iam_policy" "eyecue_admin_policy" {
  name        = "EyecueRdsAdminPermission"
  description = "Custom policy for EyecueAdmin to manage RDS instances"
  policy      = data.aws_iam_policy_document.eyecue_admin_policy.json
  tags        = var.tags
}

resource "aws_iam_role_policy_attachment" "eyecue_admin_policy_attachment" {
  role       = aws_iam_role.eyecue_admin_role.name
  policy_arn = aws_iam_policy.eyecue_admin_policy.arn
}

### Create IAM Policy and Role for IAM authentication

module "rds_access_role" {
  roles_allowed_to_assume = var.eyecue_rds_roles_allowed_to_read
  source                  = "../rds_access_role"
  aws_account_id          = var.aws_account_id
  aws_region              = var.aws_region
  instance_id             = var.create_replica ? module.replica.db_instance_resource_id : module.master.db_instance_resource_id
}


# Security Groups
resource "aws_security_group" "main_sec_group" {
  vpc_id      = var.vpc_id
  name        = var.vpc_security_group_name
  description = var.vpc_security_group_description
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Conditional ingress rules based on enable_prefix_list flag
  # When prefix list is enabled: Use AWS IP ranges via prefix list
  dynamic "ingress" {
    for_each = var.enable_prefix_list ? [1] : []
    content {
      from_port   = var.rds_port
      to_port     = var.rds_port
      protocol    = "tcp"
      cidr_blocks = data.aws_ip_ranges.aws_ips.cidr_blocks
      description = "Allow access from AWS IP ranges for ec2 services in ${var.aws_region}"
    }
  }

  # When prefix list is disabled: Use 0.0.0.0/0 (temporary until SG limits are increased)
  dynamic "ingress" {
    for_each = var.enable_prefix_list ? [] : [1]
    content {
      from_port   = var.rds_port
      to_port     = var.rds_port
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
      description = "Allow all traffic (temporary - prefix list disabled due to SG rule limits)"
    }
  }

  # Optional: Add specific additional CIDRs if needed
  dynamic "ingress" {
    for_each = var.additional_cidr_blocks
    content {
      from_port   = var.rds_port
      to_port     = var.rds_port
      protocol    = "tcp"
      cidr_blocks = [ingress.value]
      description = "Additional CIDR: ${ingress.value}"
    }
  }

  tags = var.tags
}

# Cloudflare DNS Records - DISABLED
# These modules created public DNS records for RDS instances which:
# 1. Expose infrastructure details publicly
# 2. Are not referenced anywhere in the codebase
# 3. Create potential security risks
# 
# To re-enable (not recommended), set create_cloudflare_dns_records = true
# and uncomment the modules below

# module "master_dns_record" {
#   count                   = var.create_cloudflare_dns_records ? 1 : 0
#   source                  = "../cloudflare"
#   cloudflare_zone_id      = "13bbaa28a85416bdd354f6014cdac2e3"
#   cloudflare_record_name  = "${var.eyecue_rds_stage_name}.master.rds.${var.aws_region}.${var.eyecue_rds_customer_id}.${var.product}"
#   cloudflare_record_value = module.master.db_instance_address
#   cloudflare_api_key      = var.eyecue_rds_cloudflare_api_key
#   cloudflare_record_type  = "CNAME"
# }

# module "replica_dns_record" {
#   count                   = var.create_cloudflare_dns_records ? 1 : 0
#   source                  = "../cloudflare"
#   cloudflare_zone_id      = "13bbaa28a85416bdd354f6014cdac2e3"
#   cloudflare_record_name  = "${var.eyecue_rds_stage_name}.replica.rds.${var.aws_region}.${var.eyecue_rds_customer_id}.${var.product}"
#   cloudflare_record_value = module.replica.db_instance_address != "" ? module.replica.db_instance_address : var.eyecue_rds_null_replica_address
#   cloudflare_api_key      = var.eyecue_rds_cloudflare_api_key
#   cloudflare_record_type  = "CNAME"
# }
