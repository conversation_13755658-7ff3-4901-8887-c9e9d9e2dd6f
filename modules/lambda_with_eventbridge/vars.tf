variable "tags" {
  description = "A map of tags to tag resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "enable_logging" {
  description = "Whether to enable logging for lambda"
  type        = string
  default     = "true"
}

variable "app_name" {
  description = "application name"
  type        = string
}

variable "ecr_image" {
  description = "Initial ECR Image with tag for provisioning the lambda"
  type        = string
}

variable "event_bus_name" {
  description = "Name of the event bus"
  type        = string
}

variable "lambda_timeout" {
  description = "Timeout for the lambda"
  default     = 30
}

variable "event_rule_name" {
  description = "Name of the event rule"
  type        = string
}

variable "event_rule_arn" {
  description = "ARN of the event rule"
  type        = string
}

variable "aws_account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "aws_region" {
  description = "AWS Region"
  type        = string
}

variable "environment_variables_for_lambda" {
  description = "Environment variables for the lambda"
  type        = map(any)
}
