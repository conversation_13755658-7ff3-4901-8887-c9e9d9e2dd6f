variable "minimum_password_length" {
  description = "Minimum length for the password."
  type        = number
  default     = 12
}

variable "require_lowercase_characters" {
  description = "Require lowercase characters in the password."
  type        = bool
  default     = true
}

variable "require_numbers" {
  description = "Require numbers in the password."
  type        = bool
  default     = true
}

variable "require_uppercase_characters" {
  description = "Require uppercase characters in the password."
  type        = bool
  default     = true
}

variable "require_symbols" {
  description = "Require symbols in the password."
  type        = bool
  default     = true
}

variable "allow_users_to_change_password" {
  description = "Allow users to change their password."
  type        = bool
  default     = true
}

variable "password_reuse_prevention" {
  description = "Number of previous passwords that are disallowed for reuse."
  type        = number
  default     = 3
}

variable "max_password_age" {
  description = "Maximum age (in days) for a password."
  type        = number
  default     = 90
}

variable "hard_expiry" {
  description = "If set to true, users are forced to reset their password when it expires."
  type        = bool
  default     = false
}
