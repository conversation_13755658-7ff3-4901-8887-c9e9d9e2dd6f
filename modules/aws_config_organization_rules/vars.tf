# =====
# AWS Config: Organization managed rule
# =====
variable "canned_rule_set" {
  type        = string
  description = "[Optional] The canned rule sets containing a collection of pre-configured AWS Config organization rules to use."
  default     = "baseline"
}

variable "additional_rules" {
  description = "[Optional] Collection of additional individual AWS Config rule configurations to use."
  type        = map(any)
  default     = {}
}

variable "global_excluded_accounts" {
  description = "[Optional] List of AWS accounts to exclude from all applied AWS Config rules."
  type        = list(string)
  default     = []
}

# =====
# SNS
# =====
variable "sns_topic_name" {
  description = "[Optional] Name for SNS topic used to send AWS Config rule notifications."
  type        = string
  default     = "aws-config-organizational-rule-notifications"
}

variable "notify_emails" {
  description = "[Optional] List of email addresses to notify for AWS Config rule notifications."
  type        = list(string)
  default     = []
}

variable "sns_kms_key_alias_name" {
  description = "[Optional] KMS key alias name for encrypting the SNS topic. This value should start with 'alias'. Default is the AWS managed KMS key 'alias/aws/sns"
  type        = string
  default     = "alias/aws/sns"
}

# =====
# EventBridge
# =====
variable "eventbridge_rule_name_prefix" {
  description = "[Optional] Name prefix for EventBridge rules for detecting AWS Config rule events."
  type        = string
  default     = "aws-config-organizational-rule"
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
