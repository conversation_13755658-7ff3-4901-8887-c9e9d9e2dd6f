locals {
  canned_rule_sets = {
    baseline = {
      # Baseline canned rule set
      # 
      # Format::
      #  <NAME> = {
      #    id                          = "<RULE_IDENTIFIER>"
      #    description                 = "<DESCRIPTION>"  # Max 256 chars
      #    excluded_accounts           = ["ACCOUNT_ID"]
      #    input_parameters            = jsonencode({<PARAMETERS>})
      #    maximum_execution_frequency = "One_Hour" | "Three_Hours" | "Six_Hours" | "Twelve_Hours" | "TwentyFour_Hours"
      #    resource_id_scope           = "<RESOURCE_ID>"
      #    resource_types_scope        = ["<RESOURCE_TYPE>"]
      #    tag_key_scope               = "<TAG_KEY>"
      #    tag_value_scope             = "<TAG_VALUE>"
      #    alerts = {
      #      enable_alert_non_compliant = true | false
      #    }
      #  }
      access-keys-rotated = {
        # https://docs.aws.amazon.com/config/latest/developerguide/access-keys-rotated.html
        id          = "ACCESS_KEYS_ROTATED"
        description = "Checks if active IAM access keys are rotated (changed) within the number of days specified in maxAccessKeyAge."
        input_parameters = jsonencode({
          maxAccessKeyAge = "90"
        })
        alerts = {
          enable_alert_non_compliant = true
        }
      }
      iam-user-unused-credentials-check = {
        # https://docs.aws.amazon.com/config/latest/developerguide/iam-user-unused-credentials-check.html
        id          = "IAM_USER_UNUSED_CREDENTIALS_CHECK"
        description = "Checks if your AWS Identity and Access Management (IAM) users have passwords or active access keys that have not been used within the specified number of days you provided."
        input_parameters = jsonencode({
          maxCredentialUsageAge = "90"
        })
        alerts = {
          enable_alert_non_compliant = true
        }
      }
    }
  }

  used_rules = merge(local.canned_rule_sets[var.canned_rule_set], var.additional_rules)

  alert_on = {
    non_compliant_config_rule_names = (
      [
        for name, rule in local.used_rules :
        name if try(rule.alerts.enable_alert_non_compliant, false)
      ]
    )
  }

  notification_templates = {
    non_compliant = {
      input_paths = {
        awsRegion    = "$.detail.awsRegion",
        resourceId   = "$.detail.resourceId",
        awsAccountId = "$.detail.awsAccountId",
        compliance   = "$.detail.newEvaluationResult.complianceType",
        rule         = "$.detail.configRuleName",
        time         = "$.detail.newEvaluationResult.resultRecordedTime",
        resourceType = "$.detail.resourceType"
      }
      input_template = <<-EOF
        {
          "Subject": "AWS Config Rule Non-Compliance Alert",
          "Account": "<awsAccountId>",
          "Region": "<awsRegion>",
          "Time": "<time>",
          "ConfigRule": "<rule>",
          "Resource": "<resourceId>",
          "ComplianceStatus": "<compliance>",
          "TimelineURL": "https://console.aws.amazon.com/config/home?region=<awsRegion>#/timeline/<resourceType>/<resourceId>/configuration",
          "Message": "On <time> AWS Config rule <rule> evaluated the <resourceType> with ID <resourceId> in the account <awsAccountId> Region <awsRegion> as <compliance>. For more details open the AWS Config console at https://console.aws.amazon.com/config/home?region=<awsRegion>#/timeline/<resourceType>/<resourceId>/configuration"
        }
      EOF
    }
  }
}
