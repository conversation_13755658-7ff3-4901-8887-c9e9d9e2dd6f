# Module: AWS Config Organization rules
#
# This module deploys AWS Config Organization rules which can monitor all AWS accounts and regions
# within your AWS Organization that have AWS Config recording enabled. AWS Config rules can be set
# up to monitor AWS resource for any non-compliance against required configuration. See:
# https://docs.aws.amazon.com/config/latest/developerguide/config-rule-multi-account-deployment.html
#
# Example rules:
#   - `access-keys-rotated`: Checks if active IAM access keys are rotated (changed) within the
#       number of days specified in maxAccessKeyAge.
#   - `iam-user-unused-credentials-check`: Checks if your AWS Identity and Access Management (IAM)
#       users have passwords or active access keys that have not been used within the specified
#       number of days you provided.
#
# NOTE: This module should only be applied on the Organization master account in the home region
# which has an AWS Config aggregator created.

locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# AWS Config: Organization managed rule
# =====
resource "aws_config_organization_managed_rule" "this" {
  for_each = local.used_rules

  name                        = each.key
  rule_identifier             = each.value.id
  description                 = try(each.value.description, null)
  excluded_accounts           = distinct(concat(var.global_excluded_accounts, try(each.value.excluded_accounts, [])))
  input_parameters            = try(each.value.input_parameters, null)
  maximum_execution_frequency = try(each.value.maximum_execution_frequency, null)
  resource_id_scope           = try(each.value.resource_id_scope, null)
  resource_types_scope        = try(each.value.resource_types_scope, null)
  tag_key_scope               = try(each.value.tag_key_scope, null)
  tag_value_scope             = try(each.value.tag_value_scope, null)
}

# =====
# SNS: Topic for AWS Config rule notifications
# =====
resource "aws_sns_topic" "this" {
  name              = var.sns_topic_name
  kms_master_key_id = data.aws_kms_alias.sns.id
  tags              = local.tags
}

resource "aws_sns_topic_policy" "this" {
  arn = aws_sns_topic.this.arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowEventBridgePublish"
        Effect = "Allow"
        Principal = {
          Service = "events.amazonaws.com"
        }
        Action   = "SNS:Publish"
        Resource = aws_sns_topic.this.arn
      }
    ]
  })
}

data "aws_kms_alias" "sns" {
  name = var.sns_kms_key_alias_name
}

resource "aws_sns_topic_subscription" "this" {
  for_each  = toset(var.notify_emails)
  topic_arn = aws_sns_topic.this.arn
  protocol  = "email"
  endpoint  = each.value
}

# =====
# EventBridge: Rule for "NON_COMPLIANT" AWS Config rule events
# =====
resource "aws_cloudwatch_event_rule" "config_rules_non_compliant" {
  name        = "${var.eventbridge_rule_name_prefix}-non-compliant"
  description = "Alerts for resources detected with NON_COMPLIANT AWS Config rules."

  event_pattern = jsonencode({
    source      = ["aws.config"]
    detail-type = ["Config Rules Compliance Change"]
    detail = {
      configRuleName = local.alert_on.non_compliant_config_rule_names
      newEvaluationResult = {
        complianceType = ["NON_COMPLIANT"]
      }
    }
  })

  tags = local.tags
}

resource "aws_cloudwatch_event_target" "config_rules_non_compliant" {
  rule      = aws_cloudwatch_event_rule.config_rules_non_compliant.name
  target_id = "${var.eventbridge_rule_name_prefix}-non-compliant-SendToSNS"
  arn       = aws_sns_topic.this.arn

  input_transformer {
    input_paths    = local.notification_templates.non_compliant.input_paths
    input_template = local.notification_templates.non_compliant.input_template
  }
}
