# =====
# SNS Topic: CloudWatch Alarms for DynamoDB
# =====
variable "sns_topic_arns" {
  description = <<-DOC
    [Optional] List of existing SNS topic ARNs to use for sending CloudWatch Alarm notifications
    for EC2 instances.

    It is recommended to create a centralised SNS topic for multiple CloudWatch Alarm notifications
    using the module `cw_alarm_notifications_sns_topic`:
    https://bitbucket.org/fingermarkltd/fingermark-terraform/src/master/modules/cw_alarm_notifications_sns_topic/
  DOC
  type        = list(string)
  default     = []
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}

# =====
# CloudWatch Alarms: DynamoDB Consumed RCU
# =====
variable "cw_alarm_config_ddb_table_consumed_rcu" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for DynamoDB table read capacity units.
    CloudWatch Alarm Namespace: AWS/DynamoDB
    CloudWatch Alarm Metric: ConsumedReadCapacityUnits
    CloudWatch Alarm Metric Dimensions: TableName
    Comparison Operator: GreaterThanThreshold

    ReadCapacityUnits: The maximum number of strongly consistent reads consumed per second before
    DynamoDB returns a ThrottlingException.

    ConsumedReadCapacityUnits: The number of read capacity units consumed over the specified time
    period for both provisioned and on-demand capacity. Amazon CloudWatch aggregates this metric at
    one-minute intervals
    
    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `table_name` - [Required] DynamoDB table name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold_percentage_of_rcu` - [Optional] The percentage of provisioned read capacity units
      to set the threshold value. Only applies to tables with capacity mode set to "PROVISIONED".
      `threshold_percentage_of_rcu` is used over `threshold` if `threshold_percentage_of_rcu` is
      set and the table capacity mode is set to "PROVISIONED".
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
      `threshold_percentage_of_rcu` is used over `threshold` if `threshold_percentage_of_rcu` is
      set and the table capacity mode is set to "PROVISIONED". 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    table_name                  = string
    evaluation_periods          = optional(number, 3)
    period_seconds              = optional(number, 60) # 1 Minutes
    statistic                   = optional(string, "Sum")
    threshold                   = optional(number, 600) # RCU(10) * PeriodSeconds(60) = 600 
    threshold_percentage_of_rcu = optional(number, 80)  # 80% of RCU
    alarm_description           = optional(string)
    enable_notification         = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_ddb_gsi_consumed_rcu" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for DynamoDB global secondary index read
    capacity units.
    CloudWatch Alarm Namespace: AWS/DynamoDB
    CloudWatch Alarm Metric: ConsumedReadCapacityUnits
    CloudWatch Alarm Metric Dimensions: TableName, GlobalSecondaryIndexName
    Comparison Operator: GreaterThanThreshold

    ReadCapacityUnits: The maximum number of strongly consistent reads consumed per second before
    DynamoDB returns a ThrottlingException.

    ConsumedReadCapacityUnits: The number of read capacity units consumed over the specified time
    period for both provisioned and on-demand capacity. Amazon CloudWatch aggregates this metric at
    one-minute intervals
    
    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `table_name` - [Required] DynamoDB table name.
    * `index_name` - [Required] DynamoDB global secondary index name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold_percentage_of_rcu` - [Optional] The percentage of provisioned read capacity units
      to set the threshold value. Only applies to tables with capacity mode set to "PROVISIONED".
      `threshold_percentage_of_rcu` is used over `threshold` if `threshold_percentage_of_rcu` is
      set and the table capacity mode is set to "PROVISIONED".
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
      `threshold_percentage_of_rcu` is used over `threshold` if `threshold_percentage_of_rcu` is
      set and the table capacity mode is set to "PROVISIONED". 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    table_name                  = string
    index_name                  = string
    evaluation_periods          = optional(number, 3)
    period_seconds              = optional(number, 60) # 1 Minutes
    statistic                   = optional(string, "Sum")
    threshold                   = optional(number, 600) # RCU(10) * PeriodSeconds(60) = 600 
    threshold_percentage_of_rcu = optional(number, 80)  # 80% of RCU
    alarm_description           = optional(string)
    enable_notification         = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: DynamoDB Consumed WCU
# =====
variable "cw_alarm_config_ddb_table_consumed_wcu" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for DynamoDB table write capacity units.
    CloudWatch Alarm Namespace: AWS/DynamoDB
    CloudWatch Alarm Metric: ConsumedWriteCapacityUnits
    CloudWatch Alarm Metric Dimensions: TableName
    Comparison Operator: GreaterThanThreshold

    WriteCapacityUnits: The maximum number of writes consumed per second before DynamoDB returns a
    ThrottlingException.
  
    ConsumedWriteCapacityUnits: The number of write capacity units consumed over the specified time
    period for both provisioned and on-demand capacity. Amazon CloudWatch aggregates this metric at
    one-minute intervals
    
    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `table_name` - [Required] DynamoDB table name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold_percentage_of_wcu` - [Optional] The percentage of provisioned write capacity units
      to set the threshold value. Only applies to tables with capacity mode set to "PROVISIONED".
      `threshold_percentage_of_wcu` is used over `threshold` if `threshold_percentage_of_wcu` is
      set and the table capacity mode is set to "PROVISIONED".
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
      `threshold_percentage_of_wcu` is used over `threshold` if `threshold_percentage_of_wcu` is
      set and the table capacity mode is set to "PROVISIONED". 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    table_name                  = string
    evaluation_periods          = optional(number, 3)
    period_seconds              = optional(number, 60) # 1 Minutes
    statistic                   = optional(string, "Sum")
    threshold                   = optional(number, 300) # WCU(5) * PeriodSeconds(60) = 300 
    threshold_percentage_of_wcu = optional(number, 80)  # 80% of WCU
    alarm_description           = optional(string)
    enable_notification         = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_ddb_gsi_consumed_wcu" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for DynamoDB global secondary index write
    capacity units.
    CloudWatch Alarm Namespace: AWS/DynamoDB
    CloudWatch Alarm Metric: ConsumedWriteCapacityUnits
    CloudWatch Alarm Metric Dimensions: TableName, GlobalSecondaryIndexName
    Comparison Operator: GreaterThanThreshold

    WriteCapacityUnits: The maximum number of writes consumed per second before DynamoDB returns a
    ThrottlingException.
  
    ConsumedWriteCapacityUnits: The number of write capacity units consumed over the specified time
    period for both provisioned and on-demand capacity. Amazon CloudWatch aggregates this metric at
    one-minute intervals
    
    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `table_name` - [Required] DynamoDB table name.
    * `index_name` - [Required] DynamoDB global secondary index name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold_percentage_of_wcu` - [Optional] The percentage of provisioned write capacity units
      to set the threshold value. Only applies to tables with capacity mode set to "PROVISIONED".
      `threshold_percentage_of_wcu` is used over `threshold` if `threshold_percentage_of_wcu` is
      set and the table capacity mode is set to "PROVISIONED".
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
      `threshold_percentage_of_wcu` is used over `threshold` if `threshold_percentage_of_wcu` is
      set and the table capacity mode is set to "PROVISIONED". 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    table_name                  = string
    index_name                  = string
    evaluation_periods          = optional(number, 3)
    period_seconds              = optional(number, 60) # 1 Minutes
    statistic                   = optional(string, "Sum")
    threshold                   = optional(number, 300) # WCU(5) * PeriodSeconds(60) = 300 
    threshold_percentage_of_wcu = optional(number, 80)  # 80% of WCU
    alarm_description           = optional(string)
    enable_notification         = optional(bool, true)
  }))
  default = {}
}
