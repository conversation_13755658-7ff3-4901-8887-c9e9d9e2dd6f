# Module: DynamoDB CloudWatch Alarms

Deploys CloudWatch alarms for DynamoDB table provided CloudWatch metrics. The DynamoDB table must exist for the alarms to be created. CloudWatch Alarms notifications can be optionally sent to emails using a SNS topic.

For available DynamoDB CloudWatch metrics see:
https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/metrics-dimensions.html

CloudWatch alarm thresholds can change based on changed DynamoDB table's **Read Capacity Units** and **Write Capacity Units** (if billing mode is set to "provisioned capacity"). As a result, subsquent Terraform applies may show `aws_cloudwatch_metric_alarm` resource changes as a result of changing Read/Write Capacity Units.

## Example usage

```hcl
module "dynamodb_cw_alarms" {
  source         = "./modules/dynamodb_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]

  cw_alarm_config_ddb_table_consumed_rcu = {
    "my_table_on_demand" = {
      table_name          = "my_table_on_demand"
      threshold           = 50                         # Optional
      evaluation_periods  = 2                          # Optional
      period_seconds      = 60                         # Optional
      statistic           = "Sum"                      # Optional
      alarm_description   = "Custom alarm description" # Optional
      enable_notification = true                       # Optional
    }
    "my_table_provisioned" = {
      table_name                  = "my_table_provisioned"
      threshold_percentage_of_rcu = 90 # Optional
    }
  }
  cw_alarm_config_ddb_gsi_consumed_rcu = {
    "my_table_provisioned-gsi_p_key-index" = { table_name = "my_table_provisioned", index_name = "gsi_p_key-index" }
  }
  cw_alarm_config_ddb_table_consumed_wcu = {
    "my_table_on_demand" = { table_name = "my_table_on_demand" }
  }
  cw_alarm_config_ddb_gsi_consumed_wcu = {
    "my_table_provisioned-gsi_p_key-index" = { table_name = "my_table_provisioned", index_name = "gsi_p_key-index" }
  }
}
```