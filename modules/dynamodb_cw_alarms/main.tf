locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# DynamoDB: Read ensuring existing DynamoDB tables
# =====
locals {
  table_names = {
    consumed_rcu = [for config in values(var.cw_alarm_config_ddb_table_consumed_rcu) : config.table_name]
    consumed_wcu = [for config in values(var.cw_alarm_config_ddb_table_consumed_wcu) : config.table_name]
  }
  all_distinct_table_names_list = distinct(flatten([
    for key, value in local.table_names : value
  ]))
  all_distinct_table_names_set = toset(local.all_distinct_table_names_list)
  tables_with_provisioned_rcu = {
    # Only tables that have ReadCapacityUnits > 0 will be in this map. The map values is the ReadCapacityUnits.
    for table_name in local.all_distinct_table_names_set : table_name => data.aws_dynamodb_table.this[table_name].read_capacity
    if try(data.aws_dynamodb_table.this[table_name].read_capacity > 0, false)
  }
  tables_with_provisioned_wcu = {
    # Only tables that have WriteCapacityUnits > 0 will be in this map. The map values is the WriteCapacityUnits.
    for table_name in local.all_distinct_table_names_set : table_name => data.aws_dynamodb_table.this[table_name].write_capacity
    if try(data.aws_dynamodb_table.this[table_name].write_capacity > 0, false)
  }
  gsi_list = flatten([
    for table_name, table in data.aws_dynamodb_table.this : [
      for gsi in table.global_secondary_index : {
        table_name = table_name
        index_name = gsi.name
        gsi_data   = gsi
      }
    ]
  ])
  gsi_capacity_map = {
    for item in local.gsi_list :
    "${item.table_name}-${item.index_name}" => {
      read_capacity  = item.gsi_data.read_capacity
      write_capacity = item.gsi_data.write_capacity
    }
  }
  gsis_with_provisioned_rcu = {
    # Only global secondary indexes that have ReadCapacityUnits > 0 will be in this map. The map values is the ReadCapacityUnits.
    for key, value in local.gsi_capacity_map : key => value.read_capacity if try(value.read_capacity > 0, false)
  }
  gsis_with_provisioned_wcu = {
    # Only global secondary indexes that have WriteCapacityUnits > 0 will be in this map. The map values is the WriteCapacityUnits.
    for key, value in local.gsi_capacity_map : key => value.write_capacity if try(value.write_capacity > 0, false)
  }
}

data "aws_dynamodb_table" "this" {
  for_each = local.all_distinct_table_names_set
  name     = each.value
}

# =====
# CloudWatch Alarms: DynamoDB Consumed RCU
# =====
locals {
  ddb_table_consumed_rcu_alarm = {
    name_prefix = "ddb-table-consumed-rcu"
    # Threshold value will be calculated from `threshold_percentage_of_rcu` if table has allocated ReadCapacityUnits, 
    # otherwise the explicit value from `threshold` is used.
    thresholds = {
      for k, v in var.cw_alarm_config_ddb_table_consumed_rcu : k => try(
        local.tables_with_provisioned_rcu[v.table_name] * v.threshold_percentage_of_rcu / 100 * v.period_seconds,
        v.threshold
      )
    }
  }
  ddb_gsi_consumed_rcu_alarm = {
    name_prefix = "ddb-gsi-consumed-rcu"
    # Threshold value will be calculated from `threshold_percentage_of_rcu` if GSI has allocated ReadCapacityUnits, 
    # otherwise the explicit value from `threshold` is used.
    thresholds = {
      for k, v in var.cw_alarm_config_ddb_gsi_consumed_rcu : k => try(
        local.gsis_with_provisioned_rcu["${v.table_name}-${v.index_name}"] * v.threshold_percentage_of_rcu / 100 * v.period_seconds,
        v.threshold
      )
    }
  }
}

resource "aws_cloudwatch_metric_alarm" "ddb_table_consumed_rcu_alarm" {
  for_each = var.cw_alarm_config_ddb_table_consumed_rcu

  alarm_name = "${local.ddb_table_consumed_rcu_alarm.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Consumed read capacity units (RCU) threshold exceeded for DynamoDB table ${each.value.table_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "ConsumedReadCapacityUnits"
  namespace           = "AWS/DynamoDB"
  dimensions = {
    TableName = each.value.table_name
  }
  period        = each.value.period_seconds
  statistic     = each.value.statistic
  threshold     = local.ddb_table_consumed_rcu_alarm.thresholds[each.key]
  alarm_actions = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions    = each.value.enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.ddb_table_consumed_rcu_alarm.name_prefix}-${each.key}" })
}

resource "aws_cloudwatch_metric_alarm" "ddb_gsi_consumed_rcu_alarm" {
  for_each = var.cw_alarm_config_ddb_gsi_consumed_rcu

  alarm_name = "${local.ddb_gsi_consumed_rcu_alarm.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Consumed read capacity units (RCU) threshold exceeded for DynamoDB table ${each.value.table_name} GSI ${each.value.index_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "ConsumedReadCapacityUnits"
  namespace           = "AWS/DynamoDB"
  dimensions = {
    TableName                = each.value.table_name
    GlobalSecondaryIndexName = each.value.index_name
  }
  period        = each.value.period_seconds
  statistic     = each.value.statistic
  threshold     = local.ddb_gsi_consumed_rcu_alarm.thresholds[each.key]
  alarm_actions = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions    = each.value.enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.ddb_gsi_consumed_rcu_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: DynamoDB Consumed WCU
# =====
locals {
  ddb_table_consumed_wcu_alarm = {
    name_prefix = "ddb-table-consumed-wcu"
    # Threshold value will be calculated from `threshold_percentage_of_wcu` if table has allocated WriteCapacityUnits, 
    # otherwise the explicit value from `threshold` is used.
    thresholds = {
      for k, v in var.cw_alarm_config_ddb_table_consumed_wcu : k => try(
        local.tables_with_provisioned_wcu[v.table_name] * v.threshold_percentage_of_wcu / 100 * v.period_seconds,
        v.threshold
      )
    }
  }
  ddb_gsi_consumed_wcu_alarm = {
    name_prefix = "ddb-gsi-consumed-wcu"
    # Threshold value will be calculated from `threshold_percentage_of_wcu` if GSI has allocated WriteCapacityUnits, 
    # otherwise the explicit value from `threshold` is used.
    thresholds = {
      for k, v in var.cw_alarm_config_ddb_gsi_consumed_wcu : k => try(
        local.gsis_with_provisioned_wcu["${v.table_name}-${v.index_name}"] * v.threshold_percentage_of_wcu / 100 * v.period_seconds,
        v.threshold
      )
    }
  }
}

resource "aws_cloudwatch_metric_alarm" "ddb_table_consumed_wcu_alarm" {
  for_each = var.cw_alarm_config_ddb_table_consumed_wcu

  alarm_name = "${local.ddb_table_consumed_wcu_alarm.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Consumed write capacity units (WCU) threshold exceeded for DynamoDB table ${each.value.table_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "ConsumedWriteCapacityUnits"
  namespace           = "AWS/DynamoDB"
  dimensions = {
    TableName = each.value.table_name
  }
  period        = each.value.period_seconds
  statistic     = each.value.statistic
  threshold     = local.ddb_table_consumed_wcu_alarm.thresholds[each.key]
  alarm_actions = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions    = each.value.enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.ddb_table_consumed_wcu_alarm.name_prefix}-${each.key}" })
}

resource "aws_cloudwatch_metric_alarm" "ddb_gsi_consumed_wcu_alarm" {
  for_each = var.cw_alarm_config_ddb_gsi_consumed_wcu

  alarm_name = "${local.ddb_gsi_consumed_wcu_alarm.name_prefix}-${each.key}"
  alarm_description = coalesce(
    each.value.alarm_description,
    "Consumed write capacity units (WCU) threshold exceeded for DynamoDB table ${each.value.table_name} GSI ${each.value.index_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = each.value.evaluation_periods
  metric_name         = "ConsumedWriteCapacityUnits"
  namespace           = "AWS/DynamoDB"
  dimensions = {
    TableName                = each.value.table_name
    GlobalSecondaryIndexName = each.value.index_name
  }
  period        = each.value.period_seconds
  statistic     = each.value.statistic
  threshold     = local.ddb_gsi_consumed_wcu_alarm.thresholds[each.key]
  alarm_actions = each.value.enable_notification ? var.sns_topic_arns : null
  ok_actions    = each.value.enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.ddb_gsi_consumed_wcu_alarm.name_prefix}-${each.key}" })
}
