# Example
```
module grafana {
  source = "../../../modules/fargate"
  fargate_application_name = "Grafana"
  container_image = "grafana/grafana:8.3.6"
  container_port = 3000
  env_vars = [
    {
      "name": "GF_INSTALL_PLUGINS"
      "value": "grafana-worldmap-panel, grafana-piechart-panel, grafana-googlesheets-datasource, marcus<PERSON>son-json-datasource, grafana-clock-panel",
      "type": "String"
    },
    {
      "name": "GF_SECURITY_ADMIN_PASSWORD"
      "value": "q1w2e3r4",
      "type": "SecureString"
    }
  ]

  vpc_id = "vpc-aeaea2c9"
  subnets_ids = ["subnet-6d620435", "subnet-61da3b29"]
  
  tags = {
    Stack = "Monitoring"
  }
  # create_alb = false
  # external_alb_security_group_id = "sg-063d5bf978262f76c"
  # external_alb_id = "arn:aws:elasticloadbalancing:ap-southeast-2:979525481730:loadbalancer/app/TestALB/1b366d023e7f071d"
}
```