variable "default_tags" {
  description = "A map of tags to tag resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "vpc_id" {
  description = "VPC's ID where the application will be deployed"
  type        = string
}

variable "fargate_subnets_ids" {
  description = "List of subnet ID where the application will be deployed"
  type        = list(string)
}

variable "alb_subnets_ids" {
  description = "List of subnet ID where the alb will be deployed"
  type        = list(string)
}

variable "fargate_app_security_group_ids" {
  type    = list(any)
  default = []
}

variable "tags" {
  description = "A map of tags to tag resources"
  type        = map(string)
  default     = null
}

variable "fargate_application_name" {
  description = "Name of the application you want to deploy"
  type        = string
  default     = "FingermarkFargateApp"
}

variable "secret_managager_prefix" {
  description = "Prefix for Secret managers keys"
  type        = string
  default     = "/prod/monitoring/grafana/"
}


variable "container_port" {
  description = "Container port where the application is listening"
  type        = number
  default     = 80
}

variable "container_image" {
  description = "Container definition image value"
  type        = string
}

variable "fargate_app_ecs_service_desired_count" {
  description = "Number of tasks running within the service"
  type        = number
  default     = 1
}

variable "load_balancer_listen_port" {
  description = "Port number that the ALB will be listening"
  type        = number
  default     = 80
}

variable "load_balancer_listen_protocol" {
  description = "Protocol name that the ALB will be listening. See port number"
  type        = string
  default     = "HTTP"
}

variable "load_balancer_deletetion_protection" {
  type    = bool
  default = false
}


variable "env_vars" {
  description = "List of environment variables that the application needs."
  type        = list(any)
  default     = [{ "name" = "EMPTY_LIST " }]
}

variable "create_alb" {
  description = "It decides if the ALB is created or not. If not, please specify an existing ALB"
  type        = bool
  default     = true
}

variable "external_alb_security_group_id" {
  description = "The ID of an existing ALB's security group"
  type        = string
  default     = ""
}

variable "external_alb_id" {
  description = "The ID of an existing ALB"
  type        = string
  default     = ""
}

variable "fargate_app_target_group_health_check_path" {
  type    = string
  default = "/"
}

variable "fargate_app_target_group_health_check_healthy_threshold" {
  type    = number
  default = 5
}

variable "fargate_app_target_group_health_check_unhealthy_threshold" {
  type    = number
  default = 2
}

variable "ecs_task_cpu" {
  type    = number
  default = 256
}

variable "ecs_task_memory" {
  type    = number
  default = 512
}

variable "max_capacity" {
  type    = number
  default = 12
}

variable "min_capacity" {
  type    = number
  default = 3
}

variable "cpu_target_value" {
  description = "Target value for ECS CPU utilization"
  type        = number
  default     = 50.0
}

variable "memory_target_value" {
  description = "Target value for ECS memory utilization"
  type        = number
  default     = 60.0
}
