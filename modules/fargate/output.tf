output "fargate_app_load_balancer_dns_address" {
  value = var.create_alb ? aws_alb.fargate_app[0].dns_name : null
}

output "fargate_app_security_group" {
  value = aws_security_group.fargate_app.id
}

output "fargate_app_lb_arn" {
  value = var.create_alb ? aws_alb.fargate_app[0].arn : null
}

output "fargate_app_lb_target_group_arn" {
  value = aws_alb_target_group.fargate_load_balancer.arn
}

output "fargate_ecs_cluster_arn" {
  value = aws_ecs_cluster.fargate_app.arn
}
