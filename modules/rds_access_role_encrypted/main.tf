resource "aws_iam_role" "rds_read_role" {
  name = "RDSReadRoleEncrypted"
  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [{
      "Effect" : "Allow",
      "Principal" : {
        "AWS" : [
          for role in var.roles_allowed_to_assume : "arn:aws:iam::${var.aws_account_id}:role/${role}"
        ]
      },
      "Action" : "sts:AssumeRole"
    }]
  })
  tags = var.tags
}

resource "aws_iam_policy" "rds_lambda_access" {
  name        = "rds_lambda_access_encrypted"
  path        = "/"
  description = "Enable EYECue lambdas to access RDS"
  tags        = var.tags
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Action" : "rds-db:connect",
        "Effect" : "Allow",
        "Resource" : "arn:aws:rds-db:${var.aws_region}:${var.aws_account_id}:dbuser:${var.instance_id}/${var.db_user_name}"
      }
    ]
  })
}

resource "aws_iam_policy_attachment" "rds_lambda_access_attachment" {
  name       = "RDSLambdaAccessAttachmentEncrypted"
  roles      = [aws_iam_role.rds_read_role.name]
  policy_arn = aws_iam_policy.rds_lambda_access.arn
}