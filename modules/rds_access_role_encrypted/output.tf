output "role_arn" {
  description = "The ARN of the RDS read role"
  value       = aws_iam_role.rds_read_role.arn
}

output "role_name" {
  description = "The name of the RDS read role"
  value       = aws_iam_role.rds_read_role.name
}

output "role_id" {
  description = "The ID of the RDS read role"
  value       = aws_iam_role.rds_read_role.id
}

output "policy_arn" {
  description = "The ARN of the RDS Lambda access policy"
  value       = aws_iam_policy.rds_lambda_access.arn
}

output "policy_name" {
  description = "The name of the RDS Lambda access policy"
  value       = aws_iam_policy.rds_lambda_access.name
}

output "policy_id" {
  description = "The ID of the RDS Lambda access policy"
  value       = aws_iam_policy.rds_lambda_access.id
}
