# EYECUE Kommisjon

This module creates resources required by Kommisjon projects which includes:

- https://bitbucket.org/fingermarkltd/kommisjon-bootstrap
- https://bitbucket.org/fingermarkltd/kommisjon-serverless
- https://bitbucket.org/fingermarkltd/kommisjon-slack-bot


## Bootstrap

The edge server runs a bootstrap script and requires authentication with the serverless API, therefore this module creates it's own IAM user with access keys and allow invoking the API. In addition, there is a [dynamodb logging module](../provisioning_events/) that the bootstrap script utilizes, and therefore the IAM user is granted access to insert DB documents


## Slack Bot

The slack bot which is used to verify and specify new machines before enrollment is allowed. This must have access to the bucket which stores the enrollment documents to create new documents and view existing documents