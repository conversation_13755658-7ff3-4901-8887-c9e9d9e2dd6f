variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "Provisioning"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "environment" {
  type    = string
  default = "prod"
  validation {
    condition     = can(regex("^(prod|stg|dev)$", var.environment))
    error_message = "Environment must be one of prod, stg, or dev"
  }
}

variable "s3_inventory_bucket_name" {
  description = "Inventory bucket which holds the inventory node metadata"
  default     = "kommisjon-global-dev-node-inventory"
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "aws_region" {
  description = "AWS region for the current account"
  type        = string
}

variable "aws_account_id" {
  description = "AWS account ID for the current account"
  type        = string
}

variable "api_id" {
  description = "API Gateway ID"
  type        = string
}

variable "provisioning_events_table_name" {
  description = "Name of the DynamoDB table for provisioning events"
  type        = string
}