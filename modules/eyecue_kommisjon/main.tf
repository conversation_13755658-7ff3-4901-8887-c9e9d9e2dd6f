# ======================================
# KOMMISJON BOOTSTRAP
# ======================================
module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = "kommisjon-bootstrap"
  path                          = "/fingermark/eyecue/kommisjon/"
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

resource "aws_iam_user_policy" "kommisjon" {
  name = "KommisjonBootstrapUserPolicy"
  user = module.iam_user.this_iam_user_name
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      # AWS Gateway Permission
      {
        Action   = "execute-api:Invoke"
        Effect   = "Allow"
        Resource = "arn:aws:execute-api:${var.aws_region}:${var.aws_account_id}:${var.api_id}/*/*/*"
      },
      # DynamoDB Permission
      {
        Action   = "dynamodb:PutItem"
        Effect   = "Allow"
        Resource = "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/${var.provisioning_events_table_name}"
      }
    ]
  })
}

# Store the credentials in AWS Secrets Manager
resource "aws_secretsmanager_secret" "kommisjon_bootstrap_credentials" {
  name = "kommisjon-bootstrap-credentials"
  lifecycle {
    prevent_destroy = true
  }

  tags = var.tags
}

# Store the credentials in AWS Secrets Manager
resource "aws_secretsmanager_secret_version" "kommisjon_bootstrap_credentials" {
  secret_id = aws_secretsmanager_secret.kommisjon_bootstrap_credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
    "environment"           = var.environment
  })
}

# ======================================
# KOMMISJON SLACK BOT
# ======================================
resource "aws_iam_policy" "kommisjon_slack_bot" {
  name = "KommisjonSlackBotPolicy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObject"
        ],
        Effect = "Allow"
        Resource = [
          "arn:aws:s3:::${var.s3_inventory_bucket_name}",
          "arn:aws:s3:::${var.s3_inventory_bucket_name}/*"
        ]
      }
    ]
  })
  tags = var.tags
}

resource "aws_iam_role" "kommisjon_slack_bot" {
  name = "KommisjonSlackBotRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          AWS = compact([
            "arn:aws:iam::************:root", # EKS Account
            var.environment == "dev" ? "arn:aws:iam::${var.aws_account_id}:role/DevAccess" : null
          ])
        }
      }
    ]
  })
  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "kommisjon_slack_bot" {
  policy_arn = aws_iam_policy.kommisjon_slack_bot.arn
  role       = aws_iam_role.kommisjon_slack_bot.name
}