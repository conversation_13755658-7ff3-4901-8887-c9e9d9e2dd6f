# Bucket for DVC datasets
module "s3_bucket" {
  source                  = "../s3_bucket"
  bucket_name             = var.bucket_name
  acl                     = null # Setting to null to avoid using ACLs
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  enable_versioning       = var.enable_versioning
  tags                    = var.tags
}

# Add S3 bucket ownership controls separately
resource "aws_s3_bucket_ownership_controls" "dvc_bucket_ownership" {
  bucket = var.bucket_name

  rule {
    object_ownership = "BucketOwnerEnforced"
  }
}

# IAM user for DVC access
module "iam_user" {
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.iam_user_name
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase_pgp_key
  tags                          = var.tags
}

# Define IAM policy for DVC user
data "aws_iam_policy_document" "dvc_policy_doc" {
  statement {
    sid = "DVCAccessToBucket"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      module.s3_bucket.bucket_arn
    ]
  }
  statement {
    sid = "DVCAccessToObjects"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject"
    ]
    resources = [
      "${module.s3_bucket.bucket_arn}/*"
    ]
  }
}

# Create IAM policy for DVC user
resource "aws_iam_policy" "dvc_policy" {
  name        = "${var.iam_user_name}-policy"
  path        = "/"
  description = "Policy for DVC user ${var.iam_user_name} to access the ${var.bucket_name} S3 bucket"
  policy      = data.aws_iam_policy_document.dvc_policy_doc.json
  tags        = var.tags
}

# Attach policy to DVC user
resource "aws_iam_user_policy_attachment" "dvc_policy_attach" {
  user       = module.iam_user.this_iam_user_name
  policy_arn = aws_iam_policy.dvc_policy.arn
}

# Create secret for DVC credentials
resource "aws_secretsmanager_secret" "dvc_credentials" {
  name = var.secret_name
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

# Store credentials in secret version
resource "aws_secretsmanager_secret_version" "dvc_credentials" {
  secret_id = aws_secretsmanager_secret.dvc_credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
  lifecycle {
    ignore_changes = [secret_string] # Avoid Terraform re-applying the initial secret value if it changes
  }
}
