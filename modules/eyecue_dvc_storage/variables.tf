variable "bucket_name" {
  description = "The name of the S3 bucket for DVC storage."
  type        = string
}

variable "iam_user_name" {
  description = "The name for the IAM user accessing the DVC bucket."
  type        = string
}

variable "secret_name" {
  description = "The name for the AWS Secrets Manager secret storing the IAM credentials."
  type        = string
}

variable "keybase_pgp_key" {
  description = "The Keybase PGP key for encrypting the IAM secret access key."
  type        = string
  sensitive   = true
}

variable "tags" {
  description = "A map of tags to assign to the resources."
  type        = map(string)
  default     = {}
}

variable "enable_versioning" {
  description = "Whether to enable versioning on the S3 bucket."
  type        = bool
  default     = true
}
