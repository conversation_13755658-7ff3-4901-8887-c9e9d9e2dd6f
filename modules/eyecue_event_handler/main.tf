module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 5.0"
  name                          = "eyecue-event-handler"
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}


data "aws_iam_policy_document" "eyecue_event_handler_policy_document" {
  statement {
    sid = "EventHandlerPolicyDocument"
    actions = [
      "lambda:InvokeFunction",
    ]
    resources = [
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-trigger-service-${var.environment}-getSites",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-trigger-service-${var.environment}-fireTrigger",
      "arn:aws:lambda:us-east-1:${var.aws_account_id}:function:eyecue-trigger-proxy-fireTrigger-${var.environment}",
      "arn:aws:lambda:us-east-1:${var.aws_account_id}:function:eyecue-trigger-proxy-getSites-${var.environment}",
    ]
  }

  statement {
    sid = "EventHandlerSQSPolicyDocument"
    actions = [
      "sqs:GetQueueUrl",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
    ]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.aws_account_id}:eyecue-trigger-service-events-*",
    ]
  }
}

resource "aws_iam_policy" "eyecue_event_handler_policy" {
  name        = "EventHandlerPolicy"
  depends_on  = [module.iam_user]
  description = "Enable the Event Handler to invoke the required functions"
  policy      = data.aws_iam_policy_document.eyecue_event_handler_policy_document.json
}

resource "aws_iam_policy_attachment" "eyecue_event_handler_policy_attachment" {
  name       = "EventHandlerPolicyAttachment"
  users      = [module.iam_user.iam_user_name]
  policy_arn = aws_iam_policy.eyecue_event_handler_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${module.iam_user.iam_user_name}-credentials"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.iam_user_name,
    "aws_access_key_id"     = module.iam_user.iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
