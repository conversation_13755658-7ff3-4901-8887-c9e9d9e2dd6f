variable "aws_region" {
  type        = string
  description = "AWS region"
}

variable "aws_account_id" {
  type        = string
  description = "AWS account ID"
}

variable "keybase" {
  type    = string
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform      = "True"
    Serverless     = "False"
    Environment    = "prod"
    Stack          = "Application"
    Application    = "Event Handler"
    Squad          = "Core"
    CustomerFacing = "True"
    Product        = "Eyecue"
    System         = "Python3.11"
  }
}

variable "environment" {
  type    = string
  default = "prod"
  validation {
    condition     = can(regex("^(prod|stg|dev)$", var.environment))
    error_message = "Environment must be one of prod, stg, or dev"
  }
}
