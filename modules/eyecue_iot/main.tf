module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags = {
    created_by   = "Terraform"
    "serverless" = false
  }
}

data "aws_iam_policy_document" "eyecue_iot_policy_document" {
  statement {
    sid = "EyecueIoTPolicy"
    actions = [
      "lambda:InvokeFunction",
      "lambda:InvokeAsync"
    ]
    resources = [
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-things-certificate-create"
    ]
  }
}

resource "aws_iam_policy" "eyecue_iot_policy" {
  name        = "EyecueThingsCertificateCreatePolicy"
  depends_on  = [module.iam_user]
  path        = "/"
  description = ""
  policy      = data.aws_iam_policy_document.eyecue_iot_policy_document.json
}


resource "aws_iam_user_policy_attachment" "eyecue_iot_creator_attachment" {
  user       = var.aws_iam_user
  policy_arn = aws_iam_policy.eyecue_iot_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
