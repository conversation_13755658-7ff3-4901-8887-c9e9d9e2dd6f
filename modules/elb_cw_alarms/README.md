# Module: AWS Elastic Load Balancer CloudWatch Alarms

Deploys CloudWatch alarms for the following support AWS Elastic Load Balancer types:
- [Application Load Balancer](https://docs.aws.amazon.com/elasticloadbalancing/latest/application/introduction.html)
- [Network Load Balancer](https://docs.aws.amazon.com/elasticloadbalancing/latest/network/introduction.html)
- [Classic Load Balancer](https://docs.aws.amazon.com/elasticloadbalancing/latest/classic/introduction.html)

The Load Balancer and applicable target groups must exists for the alarms to be created. CloudWatch Alarms notifications can be optionally sent to emails using an existing SNS topic.

For available AWS Elastic Load Balancer metrics see:
- Application Load Balancer: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-cloudwatch-metrics.html
- Network Load Balancer: https://docs.aws.amazon.com/elasticloadbalancing/latest/network/load-balancer-cloudwatch-metrics.html
- Classic Load Balancer: https://docs.aws.amazon.com/elasticloadbalancing/latest/classic/elb-cloudwatch-metrics.html


## Example usage

### Example: Application Load Balancer Defaults

CloudWatch Alarms with default thresholds on Application Load Balancer `my-alb` with target group `web-hosts-tg`, for the following metrics:
- HealthyHostCount
- UnHealthyHostCount
- TargetResponseTime
- HTTPCode_ELB_5XX_Count
- HTTPCode_Target_5XX_Count

```hcl
module "elb_cw_alarms" {
  source                                        = "./modules/elb_cw_alarms"
  sns_topic_arns                                = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_alb_healthy_host_count        = { my-alb-defaults = { lb_name = "my-alb", tg_names = ["web-hosts-tg"] } }
  cw_alarm_config_alb_unhealthy_host_count      = { my-alb-defaults = { lb_name = "my-alb", tg_names = ["web-hosts-tg"] } }
  cw_alarm_config_alb_target_response_time      = { my-alb-defaults = { lb_name = "my-alb" } }
  cw_alarm_config_alb_httpcode_elb_5xx_count    = { my-alb-defaults = { lb_name = "my-alb" } }
  cw_alarm_config_alb_httpcode_target_5xx_count = { my-alb-defaults = { lb_name = "my-alb" } }
}
```

### Example: Network Load Balancer Defaults

CloudWatch Alarms with default thresholds on Network Load Balancer `my-nlb` with target group `tcp-hosts-tg`, for the following metrics:
- HealthyHostCount
- UnHealthyHostCount

```hcl
module "elb_cw_alarms" {
  source                                        = "./modules/elb_cw_alarms"
  sns_topic_arns                                = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_nlb_healthy_host_count        = { my-nlb-defaults = { lb_name = "my-nlb", tg_names = ["tcp-hosts-tg"] } }
  cw_alarm_config_nlb_unhealthy_host_count      = { my-nlb-defaults = { lb_name = "my-nlb", tg_names = ["tcp-hosts-tg"] } }
}
```

### Example: Classic Load Balancer Defaults

CloudWatch Alarms with default thresholds on Classic Load Balancer `my-clb`, for the following metrics:
- HealthyHostCount
- UnHealthyHostCount
- Latency
- HTTPCode_Backend_5XX
- HTTPCode_ELB_5XX

```hcl
module "elb_cw_alarms" {
  source                                   = "./modules/elb_cw_alarms"
  sns_topic_arns                           = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_clb_healthy_host_count   = { my-clb-defaults = { lb_name = "my-clb" } }
  cw_alarm_config_clb_unhealthy_host_count = { my-clb-defaults = { lb_name = "my-clb" } }
  cw_alarm_config_clb_latency              = { my-clb-defaults = { lb_name = "my-clb" } }
  cw_alarm_config_clb_httpcode_elb_5xx     = { my-clb-defaults = { lb_name = "my-clb" } }
  cw_alarm_config_clb_httpcode_backend_5xx = { my-clb-defaults = { lb_name = "my-clb" } }
}
```

### Example: Application Load Balancer Custom

CloudWatch Alarms with custom thresholds on Application Load Balancer `my-alb` with target group `web-hosts-tg`, for the following metrics:
- HealthyHostCount
- UnHealthyHostCount

```hcl
module "elb_cw_alarms" {
  source         = "./modules/elb_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_alb_healthy_host_count = {
    my-alb-custom = {
      lb_name  = "my-alb",
      tg_names = ["web-hosts-tg"]
      evaluation_periods  = 3
      period_seconds      = 60
      statistic           = "Average"
      threshold           = 2
      alarm_description   = "Less than 2 healthy hosts"
      enable_notification = true
    }
  }
  cw_alarm_config_alb_unhealthy_host_count = {
    my-alb-custom = {
      lb_name  = "my-alb",
      tg_names = ["web-hosts-tg"]
      evaluation_periods  = 3
      period_seconds      = 60
      statistic           = "Average"
      threshold           = 1
      alarm_description   = "More than 1 unhealthy hosts"
      enable_notification = true
    }
  }
}
```

### Example: Multiple Application Load Balancer Defaults

CloudWatch Alarms with defaults thresholds on mulitple Application Load Balancers, for the following metrics:
- HealthyHostCount
- UnHealthyHostCount
- TargetResponseTime
- HTTPCode_ELB_5XX_Count
- HTTPCode_Target_5XX_Count

```hcl
locals {
  elb_cw_alarms = {
    lb_configs = {
      alb-1-defaults = { lb_name = "alb-1" }
      alb-2-defaults = { lb_name = "alb-2" }
      alb-3-defaults = { lb_name = "alb-3" }
    }
    lb_tg_configs = {
      alb-1-defaults = { lb_name = "alb-1", tg_names = ["alb-1-tg"] }
      alb-2-defaults = { lb_name = "alb-2", tg_names = ["alb-1-tg"] }
      alb-3-defaults = { lb_name = "alb-3", tg_names = ["alb-1-tg"] }
    }
  }
}

module "elb_cw_alarms" {
  source                                        = "./modules/elb_cw_alarms"
  sns_topic_arns                                = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_alb_healthy_host_count        = local.elb_cw_alarms.lb_tg_configs
  cw_alarm_config_alb_unhealthy_host_count      = local.elb_cw_alarms.lb_tg_configs
  cw_alarm_config_alb_target_response_time      = local.elb_cw_alarms.lb_configs
  cw_alarm_config_alb_httpcode_elb_5xx_count    = local.elb_cw_alarms.lb_configs
  cw_alarm_config_alb_httpcode_target_5xx_count = local.elb_cw_alarms.lb_configs
}
```