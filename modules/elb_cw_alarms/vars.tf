# =====
# SNS Topic: CloudWatch Alarms for ELB
# =====
variable "sns_topic_arns" {
  description = <<-DOC
    [Optional] List of existing SNS topic ARNs to use for sending CloudWatch Alarm notifications
    for ELB.

    It is recommended to create a centralised SNS topic for multiple CloudWatch Alarm notifications
    using the module `cw_alarm_notifications_sns_topic`:
    https://bitbucket.org/fingermarkltd/fingermark-terraform/src/master/modules/cw_alarm_notifications_sns_topic/
  DOC
  type        = list(string)
  default     = []
}

# =====
# CloudWatch Alarms: Application Load Balancer
# =====
variable "cw_alarm_config_alb_healthy_host_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Application Load Balancer.
    CloudWatch Alarm Namespace: AWS/ApplicationELB
    CloudWatch Alarm Metric: HealthyHostCount
    CloudWatch Alarm Metric Dimensions: LoadBalancer, TargetGroup
    Comparison Operator: LessThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `tg_names` - [Required] List of target group names attached to the load balancer.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    tg_names            = list(string)
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Minimum")
    threshold           = optional(number, 1) # "Less than 1 healthy"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_alb_unhealthy_host_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Application Load Balancer.
    CloudWatch Alarm Namespace: AWS/ApplicationELB
    CloudWatch Alarm Metric: UnHealthyHostCount
    CloudWatch Alarm Metric Dimensions: LoadBalancer, TargetGroup
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `tg_names` - [Required] List of target group names attached to the load balancer.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    tg_names            = list(string)
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Maximum")
    threshold           = optional(number, 0) # "More than 0 unhealthy"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_alb_target_response_time" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Application Load Balancer.
    CloudWatch Alarm Namespace: AWS/ApplicationELB
    CloudWatch Alarm Metric: TargetResponseTime
    CloudWatch Alarm Metric Dimensions: LoadBalancer
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 0.5) # "More than 0.5 seconds"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_alb_httpcode_elb_5xx_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Application Load Balancer.
    CloudWatch Alarm Namespace: AWS/ApplicationELB
    CloudWatch Alarm Metric: HTTPCode_ELB_5XX_Count
    CloudWatch Alarm Metric Dimensions: LoadBalancer
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 3)  # Over 3 * 1 minutes = 3 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Sum")
    threshold           = optional(number, 10) # "More than 10 occurences"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_alb_httpcode_target_5xx_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Application Load Balancer.
    CloudWatch Alarm Namespace: AWS/ApplicationELB
    CloudWatch Alarm Metric: HTTPCode_Target_5XX_Count
    CloudWatch Alarm Metric Dimensions: LoadBalancer
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 3)  # Over 3 * 1 minutes = 3 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Sum")
    threshold           = optional(number, 10) # "More than 10 occurences"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: Network Load Balancer
# =====
variable "cw_alarm_config_nlb_healthy_host_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Network Load Balancer.
    CloudWatch Alarm Namespace: AWS/NetworkELB
    CloudWatch Alarm Metric: HealthyHostCount
    CloudWatch Alarm Metric Dimensions: LoadBalancer, TargetGroup
    Comparison Operator: LessThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `tg_names` - [Required] List of target group names attached to the load balancer.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    tg_names            = list(string)
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Minimum")
    threshold           = optional(number, 1) # "Less than 1 healthy"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_nlb_unhealthy_host_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Network Load Balancer.
    CloudWatch Alarm Namespace: AWS/NetworkELB
    CloudWatch Alarm Metric: UnHealthyHostCount
    CloudWatch Alarm Metric Dimensions: LoadBalancer, TargetGroup
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `tg_names` - [Required] List of target group names attached to the load balancer.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    tg_names            = list(string)
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Maximum")
    threshold           = optional(number, 0) # "More than 0 unhealthy"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# CloudWatch Alarms: Classic Load Balancer
# =====
variable "cw_alarm_config_clb_healthy_host_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Classic Load Balancer.
    CloudWatch Alarm Namespace: AWS/ELB
    CloudWatch Alarm Metric: HealthyHostCount
    CloudWatch Alarm Metric Dimensions: LoadBalancerName
    Comparison Operator: LessThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.B
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Minimum")
    threshold           = optional(number, 1) # "Less than 1 healthy"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_clb_unhealthy_host_count" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Classic Load Balancer.
    CloudWatch Alarm Namespace: AWS/ELB
    CloudWatch Alarm Metric: UnHealthyHostCount
    CloudWatch Alarm Metric Dimensions: LoadBalancerName
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Maximum")
    threshold           = optional(number, 0) # "More than 0 unhealthy"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_clb_latency" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Classic Load Balancer.
    CloudWatch Alarm Namespace: AWS/ELB
    CloudWatch Alarm Metric: Latency
    CloudWatch Alarm Metric Dimensions: LoadBalancerName
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 5)  # Over 5 * 1 minutes = 5 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Average")
    threshold           = optional(number, 0.5) # "More than 0.5 seconds"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_clb_httpcode_elb_5xx" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Classic Load Balancer.
    CloudWatch Alarm Namespace: AWS/ELB
    CloudWatch Alarm Metric: HTTPCode_ELB_5XX
    CloudWatch Alarm Metric Dimensions: LoadBalancerName
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 3)  # Over 3 * 1 minutes = 3 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Sum")
    threshold           = optional(number, 10) # "More than 10 occurences"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

variable "cw_alarm_config_clb_httpcode_backend_5xx" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for Classic Load Balancer.
    CloudWatch Alarm Namespace: AWS/ELB
    CloudWatch Alarm Metric: HTTPCode_Backend_5XX
    CloudWatch Alarm Metric Dimensions: LoadBalancerName
    Comparison Operator: GreaterThanThreshold

    For each map item, the key is used for the name of CloudWatch Alarm. The value is an object
    with the following attributes.

    * `lb_name` - [Required] Load balancer name.
    * `evaluation_periods` - [Optional] The number of periods to analyze for the alarm's
      corresponding metric. Must be an integer.
    * `period_seconds` - [Optional] The period in seconds over which the specified statistic
      is applied. Must be an integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared. 
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    lb_name             = string
    evaluation_periods  = optional(number, 3)  # Over 3 * 1 minutes = 3 minutes
    period_seconds      = optional(number, 60) # 1 Minutes
    statistic           = optional(string, "Sum")
    threshold           = optional(number, 10) # "More than 10 occurences"
    alarm_description   = optional(string)
    enable_notification = optional(bool, true)
  }))
  default = {}
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
