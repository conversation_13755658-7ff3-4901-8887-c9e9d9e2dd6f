# =====
# Tags: Consolidated
# =====
locals {
  tags = merge(var.default_tags, var.tags)
}

# =================================================================================================

# =====
# Application Load Balancer: Read
# =====
locals {
  alb_names = {
    healthy_host_count        = [for config in values(var.cw_alarm_config_alb_healthy_host_count) : config.lb_name]
    unhealth_host_count       = [for config in values(var.cw_alarm_config_alb_unhealthy_host_count) : config.lb_name]
    target_response_time      = [for config in values(var.cw_alarm_config_alb_target_response_time) : config.lb_name]
    httpcode_elb_5xx_count    = [for config in values(var.cw_alarm_config_alb_httpcode_elb_5xx_count) : config.lb_name]
    httpcode_target_5xx_count = [for config in values(var.cw_alarm_config_alb_httpcode_target_5xx_count) : config.lb_name]
  }
  all_distinct_alb_names = distinct(flatten([
    for key, value in local.alb_names : value
  ]))
  all_distinct_alb_names_set = toset(local.all_distinct_alb_names)

  alb_tg_names = {
    healthy_host_count  = flatten([for config in values(var.cw_alarm_config_alb_healthy_host_count) : config.tg_names])
    unhealth_host_count = flatten([for config in values(var.cw_alarm_config_alb_unhealthy_host_count) : config.tg_names])
  }
  all_distinct_alb_tg_names = distinct(flatten([
    for key, value in local.alb_tg_names : value
  ]))
  all_distinct_alb_tg_names_set = toset(local.all_distinct_alb_tg_names)
}

data "aws_lb" "alb" {
  for_each = local.all_distinct_alb_names_set
  name     = each.value
}

data "aws_lb_target_group" "alb" {
  for_each = local.all_distinct_alb_tg_names_set
  name     = each.value
}

# =====
# CloudWatch Alarms: Application Load Balancer - HealthyHostCount 
# =====
locals {
  alb_healthy_host_count_alarm = {
    name_prefix = "alb-healthy-host-count"
    lb_tg_combo_config = flatten([
      for alarm_key, config in var.cw_alarm_config_alb_healthy_host_count : [
        for tg_name in config.tg_names : {
          alarm_key  = alarm_key
          alarm_name = join("-", [substr(alarm_key, 0, 100), substr(tg_name, 0, 100)])
          lb_name    = config.lb_name
          tg_name    = tg_name
        }
      ]
    ])
  }
}
resource "aws_cloudwatch_metric_alarm" "alb_healthy_host_count_alarm" {
  for_each = {
    for item in local.alb_healthy_host_count_alarm.lb_tg_combo_config : item.alarm_name => item
  }
  alarm_name = "${local.alb_healthy_host_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].alarm_description,
    "Healthy host count less than threshold for ALB ${each.value.lb_name} target group ${each.value.tg_name}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].evaluation_periods
  metric_name         = "HealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  dimensions = {
    LoadBalancer = data.aws_lb.alb[each.value.lb_name].arn_suffix
    TargetGroup  = data.aws_lb_target_group.alb[each.value.tg_name].arn_suffix
  }
  period             = var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].threshold
  treat_missing_data = "breaching" # Treat missing data as unhealthy
  alarm_actions      = var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_alb_healthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.alb_healthy_host_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Application Load Balancer - UnHealthyHostCount 
# =====
locals {
  alb_unhealthy_host_count_alarm = {
    name_prefix = "alb-unhealthy-host-count"
    lb_tg_combo_config = flatten([
      for alarm_key, config in var.cw_alarm_config_alb_unhealthy_host_count : [
        for tg_name in config.tg_names : {
          alarm_key  = alarm_key
          alarm_name = join("-", [substr(alarm_key, 0, 100), substr(tg_name, 0, 100)])
          lb_name    = config.lb_name
          tg_name    = tg_name
        }
      ]
    ])
  }
}
resource "aws_cloudwatch_metric_alarm" "alb_unhealthy_host_count_alarm" {
  for_each = {
    for item in local.alb_unhealthy_host_count_alarm.lb_tg_combo_config : item.alarm_name => item
  }
  alarm_name = "${local.alb_unhealthy_host_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].alarm_description,
    "Unhealthy host count breached threshold for ALB ${each.value.lb_name} target group ${each.value.tg_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].evaluation_periods
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  dimensions = {
    LoadBalancer = data.aws_lb.alb[each.value.lb_name].arn_suffix
    TargetGroup  = data.aws_lb_target_group.alb[each.value.tg_name].arn_suffix
  }
  period             = var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].threshold
  treat_missing_data = "breaching" # Treat missing data as unhealthy
  alarm_actions      = var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_alb_unhealthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.alb_unhealthy_host_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Application Load Balancer - TargetResponseTime 
# =====
locals {
  alb_target_response_time_alarm = {
    name_prefix = "alb-target-response-time"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_alb_target_response_time : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "alb_target_response_time_alarm" {
  for_each = {
    for item in local.alb_target_response_time_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.alb_target_response_time_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].alarm_description,
    "Target response time breached threshold for ALB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].evaluation_periods
  metric_name         = "TargetResponseTime"
  namespace           = "AWS/ApplicationELB"
  dimensions = {
    LoadBalancer = data.aws_lb.alb[each.value.lb_name].arn_suffix
  }
  period        = var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].period_seconds
  statistic     = var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].statistic
  threshold     = var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].threshold
  alarm_actions = var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions    = var.cw_alarm_config_alb_target_response_time[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.alb_target_response_time_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Application Load Balancer - HTTPCode_ELB_5XX_Count 
# =====
locals {
  alb_httpcode_elb_5xx_count_alarm = {
    name_prefix = "alb-httpcode-elb-5xx-count"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_alb_httpcode_elb_5xx_count : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "alb_httpcode_elb_5xx_count_alarm" {
  for_each = {
    for item in local.alb_httpcode_elb_5xx_count_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.alb_httpcode_elb_5xx_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].alarm_description,
    "The number of HTTP 5XX server error codes that originate from the load balancer breached threshold for ALB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].evaluation_periods
  metric_name         = "HTTPCode_ELB_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  dimensions = {
    LoadBalancer = data.aws_lb.alb[each.value.lb_name].arn_suffix
  }
  period             = var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].threshold
  treat_missing_data = "notBreaching"
  alarm_actions      = var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_alb_httpcode_elb_5xx_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.alb_httpcode_elb_5xx_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Application Load Balancer - HTTPCode_Target_5XX_Count 
# =====
locals {
  alb_httpcode_target_5xx_count_alarm = {
    name_prefix = "alb-httpcode-target-5xx-count"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_alb_httpcode_target_5xx_count : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "alb_httpcode_target_5xx_count_alarm" {
  for_each = {
    for item in local.alb_httpcode_target_5xx_count_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.alb_httpcode_target_5xx_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].alarm_description,
    "The number of HTTP 5XX server error codes that generated by the targets breached threshold for ALB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].evaluation_periods
  metric_name         = "HTTPCode_Target_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  dimensions = {
    LoadBalancer = data.aws_lb.alb[each.value.lb_name].arn_suffix
  }
  period             = var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].threshold
  treat_missing_data = "notBreaching"
  alarm_actions      = var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_alb_httpcode_target_5xx_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.alb_httpcode_target_5xx_count_alarm.name_prefix}-${each.key}" })
}

# =================================================================================================

# =====
# Network Load Balancer: Read
# =====
locals {
  nlb_names = {
    healthy_host_count  = [for config in values(var.cw_alarm_config_nlb_healthy_host_count) : config.lb_name]
    unhealth_host_count = [for config in values(var.cw_alarm_config_nlb_unhealthy_host_count) : config.lb_name]
  }
  all_distinct_nlb_names = distinct(flatten([
    for key, value in local.nlb_names : value
  ]))
  all_distinct_nlb_names_set = toset(local.all_distinct_nlb_names)

  nlb_tg_names = {
    healthy_host_count  = flatten([for config in values(var.cw_alarm_config_nlb_healthy_host_count) : config.tg_names])
    unhealth_host_count = flatten([for config in values(var.cw_alarm_config_nlb_unhealthy_host_count) : config.tg_names])
  }
  all_distinct_nlb_tg_names = distinct(flatten([
    for key, value in local.nlb_tg_names : value
  ]))
  all_distinct_nlb_tg_names_set = toset(local.all_distinct_nlb_tg_names)
}

data "aws_lb" "nlb" {
  for_each = local.all_distinct_nlb_names_set
  name     = each.value
}

data "aws_lb_target_group" "nlb" {
  for_each = local.all_distinct_nlb_tg_names_set
  name     = each.value
}

# =====
# CloudWatch Alarms: Network Load Balancer - HealthyHostCount 
# =====
locals {
  nlb_healthy_host_count_alarm = {
    name_prefix = "nlb-healthy-host-count"
    lb_tg_combo_config = flatten([
      for alarm_key, config in var.cw_alarm_config_nlb_healthy_host_count : [
        for tg_name in config.tg_names : {
          alarm_key  = alarm_key
          alarm_name = join("-", [substr(alarm_key, 0, 100), substr(tg_name, 0, 100)])
          lb_name    = config.lb_name
          tg_name    = tg_name
        }
      ]
    ])
  }
}
resource "aws_cloudwatch_metric_alarm" "nlb_healthy_host_count_alarm" {
  for_each = {
    for item in local.nlb_healthy_host_count_alarm.lb_tg_combo_config : item.alarm_name => item
  }
  alarm_name = "${local.nlb_healthy_host_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].alarm_description,
    "Healthy host count less than threshold for NLB ${each.value.lb_name} target group ${each.value.tg_name}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].evaluation_periods
  metric_name         = "HealthyHostCount"
  namespace           = "AWS/NetworkELB"
  dimensions = {
    LoadBalancer = data.aws_lb.nlb[each.value.lb_name].arn_suffix
    TargetGroup  = data.aws_lb_target_group.nlb[each.value.tg_name].arn_suffix
  }
  period             = var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].threshold
  treat_missing_data = "breaching" # Treat missing data as unhealthy
  alarm_actions      = var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_nlb_healthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.nlb_healthy_host_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Network Load Balancer - UnHealthyHostCount 
# =====
locals {
  nlb_unhealthy_host_count_alarm = {
    name_prefix = "nlb-unhealthy-host-count"
    lb_tg_combo_config = flatten([
      for alarm_key, config in var.cw_alarm_config_nlb_unhealthy_host_count : [
        for tg_name in config.tg_names : {
          alarm_key  = alarm_key
          alarm_name = join("-", [substr(alarm_key, 0, 100), substr(tg_name, 0, 100)])
          lb_name    = config.lb_name
          tg_name    = tg_name
        }
      ]
    ])
  }
}
resource "aws_cloudwatch_metric_alarm" "nlb_unhealthy_host_count_alarm" {
  for_each = {
    for item in local.nlb_unhealthy_host_count_alarm.lb_tg_combo_config : item.alarm_name => item
  }
  alarm_name = "${local.nlb_unhealthy_host_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].alarm_description,
    "Unhealthy host count breached threshold for NLB ${each.value.lb_name} target group ${each.value.tg_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].evaluation_periods
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/NetworkELB"
  dimensions = {
    LoadBalancer = data.aws_lb.nlb[each.value.lb_name].arn_suffix
    TargetGroup  = data.aws_lb_target_group.nlb[each.value.tg_name].arn_suffix
  }
  period             = var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].threshold
  treat_missing_data = "breaching" # Treat missing data as unhealthy
  alarm_actions      = var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_nlb_unhealthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.nlb_unhealthy_host_count_alarm.name_prefix}-${each.key}" })
}

# =================================================================================================

# =====
# Classic Load Balancer: Read
# =====
locals {
  clb_names = {
    healthy_host_count   = [for config in values(var.cw_alarm_config_clb_healthy_host_count) : config.lb_name]
    unhealth_host_count  = [for config in values(var.cw_alarm_config_clb_unhealthy_host_count) : config.lb_name]
    latency              = [for config in values(var.cw_alarm_config_clb_latency) : config.lb_name]
    httpcode_elb_5xx     = [for config in values(var.cw_alarm_config_clb_httpcode_elb_5xx) : config.lb_name]
    httpcode_backend_5xx = [for config in values(var.cw_alarm_config_clb_httpcode_backend_5xx) : config.lb_name]
  }
  all_distinct_clb_names = distinct(flatten([
    for key, value in local.clb_names : value
  ]))
  all_distinct_clb_names_set = toset(local.all_distinct_clb_names)
}

data "aws_elb" "clb" {
  for_each = local.all_distinct_clb_names_set
  name     = each.value
}

# =====
# CloudWatch Alarms: Classic Load Balancer - HealthyHostCount 
# =====
locals {
  clb_healthy_host_count_alarm = {
    name_prefix = "clb-healthy-host-count"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_clb_healthy_host_count : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "clb_healthy_host_count_alarm" {
  for_each = {
    for item in local.clb_healthy_host_count_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.clb_healthy_host_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].alarm_description,
    "Healthy host count less than threshold for CLB ${each.value.lb_name}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].evaluation_periods
  metric_name         = "HealthyHostCount"
  namespace           = "AWS/ELB"
  dimensions = {
    LoadBalancerName = data.aws_elb.clb[each.value.lb_name].name
  }
  period             = var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].threshold
  treat_missing_data = "breaching" # Treat missing data as unhealthy
  alarm_actions      = var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_clb_healthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.clb_healthy_host_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Classic Load Balancer - UnHealthyHostCount 
# =====
locals {
  clb_unhealthy_host_count_alarm = {
    name_prefix = "clb-unhealthy-host-count"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_clb_unhealthy_host_count : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "clb_unhealthy_host_count_alarm" {
  for_each = {
    for item in local.clb_unhealthy_host_count_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.clb_unhealthy_host_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].alarm_description,
    "Unhealthy host count breached threshold for CLB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].evaluation_periods
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ELB"
  dimensions = {
    LoadBalancerName = data.aws_elb.clb[each.value.lb_name].name
  }
  period             = var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].threshold
  treat_missing_data = "breaching" # Treat missing data as unhealthy
  alarm_actions      = var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_clb_unhealthy_host_count[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.clb_unhealthy_host_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Classic Load Balancer - Latency 
# =====
locals {
  clb_latency_alarm = {
    name_prefix = "clb-latency"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_clb_latency : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "clb_latency_alarm" {
  for_each = {
    for item in local.clb_latency_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.clb_latency_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_clb_latency[each.value.alarm_key].alarm_description,
    "Latency breached threshold for CLB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_clb_latency[each.value.alarm_key].evaluation_periods
  metric_name         = "Latency"
  namespace           = "AWS/ELB"
  dimensions = {
    LoadBalancerName = data.aws_elb.clb[each.value.lb_name].name
  }
  period        = var.cw_alarm_config_clb_latency[each.value.alarm_key].period_seconds
  statistic     = var.cw_alarm_config_clb_latency[each.value.alarm_key].statistic
  threshold     = var.cw_alarm_config_clb_latency[each.value.alarm_key].threshold
  alarm_actions = var.cw_alarm_config_clb_latency[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions    = var.cw_alarm_config_clb_latency[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.clb_latency_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Classic Load Balancer - HTTPCode_ELB_5XX 
# =====
locals {
  clb_httpcode_elb_5xx_count_alarm = {
    name_prefix = "clb-httpcode-elb-5xx"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_clb_httpcode_elb_5xx : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "clb_httpcode_elb_5xx_count_alarm" {
  for_each = {
    for item in local.clb_httpcode_elb_5xx_count_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.clb_httpcode_elb_5xx_count_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].alarm_description,
    "The number of HTTP 5XX server error codes that originate from the load balancer breached threshold for CLB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].evaluation_periods
  metric_name         = "HTTPCode_ELB_5XX"
  namespace           = "AWS/ELB"
  dimensions = {
    LoadBalancerName = data.aws_elb.clb[each.value.lb_name].name
  }
  period             = var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].threshold
  treat_missing_data = "notBreaching"
  alarm_actions      = var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_clb_httpcode_elb_5xx[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.clb_httpcode_elb_5xx_count_alarm.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: Classic Load Balancer - HTTPCode_Backend_5XX 
# =====
locals {
  clb_httpcode_backend_5xx_alarm = {
    name_prefix = "clb-httpcode-target-5xx"
    lb_config = [
      for alarm_key, config in var.cw_alarm_config_clb_httpcode_backend_5xx : {
        alarm_key  = alarm_key
        alarm_name = substr(alarm_key, 0, 200)
        lb_name    = config.lb_name
      }
    ]
  }
}
resource "aws_cloudwatch_metric_alarm" "clb_httpcode_backend_5xx_alarm" {
  for_each = {
    for item in local.clb_httpcode_backend_5xx_alarm.lb_config : item.alarm_name => item
  }
  alarm_name = "${local.clb_httpcode_backend_5xx_alarm.name_prefix}-${each.key}" # max length 255
  alarm_description = coalesce(
    var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].alarm_description,
    "The number of HTTP 5XX server error codes that generated by the targets breached threshold for CLB ${each.value.lb_name}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].evaluation_periods
  metric_name         = "HTTPCode_Backend_5XX"
  namespace           = "AWS/ELB"
  dimensions = {
    LoadBalancerName = data.aws_elb.clb[each.value.lb_name].name
  }
  period             = var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].period_seconds
  statistic          = var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].statistic
  threshold          = var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].threshold
  treat_missing_data = "notBreaching"
  alarm_actions      = var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null
  ok_actions         = var.cw_alarm_config_clb_httpcode_backend_5xx[each.value.alarm_key].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.clb_httpcode_backend_5xx_alarm.name_prefix}-${each.key}" })
}
