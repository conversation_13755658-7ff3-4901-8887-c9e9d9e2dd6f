module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

data "aws_iam_policy_document" "eyecue_dashboard_iot_policy_document" {
  statement {
    sid       = "EyecueDashboardIoTPolicyDocument1"
    actions   = ["iot:Connect"]
    resources = ["arn:aws:iot:${var.aws_region}:${var.aws_account_id}:*"]
  }

  statement {
    sid       = "EyecueDashboardIoTPolicyDocument2"
    actions   = ["iot:Subscribe"]
    resources = ["arn:aws:iot:${var.aws_region}:${var.aws_account_id}:topicfilter//eyeq/*"]
  }

  statement {
    sid       = "EyecueDashboardIoTPolicyDocument3"
    actions   = ["iot:Receive"]
    resources = ["arn:aws:iot:${var.aws_region}:${var.aws_account_id}:topic//eyeq/*"]
  }

  statement {
    sid       = "EyecueDashboardIoTPolicyDocument4"
    actions   = ["iot:Publish"]
    resources = ["arn:aws:iot:${var.aws_region}:${var.aws_account_id}:topic//eyeq/*"]
  }

  statement {
    sid = "EyecueDashboardIoTPolicyDocument5"
    actions = [
      "dynamodb:Query",
      "dynamodb:UpdateItem"
    ]
    resources = ["arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-things-shadow"]
  }
  statement {
    sid = "EyecueDashboardIoTDynamoDBPolicyDocument"
    actions = [
      "dynamodb:PutItem",
      "dynamodb:DeleteItem",
      "dynamodb:Scan",
      "dynamodb:Query",
      "dynamodb:UpdateItem"
    ]
    resources = [
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-deployer-params",
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-helm-values",
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-weights",
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-weights-template",
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/*/index/*"
    ]
  }
}


resource "aws_iam_policy" "eyecue_dashboard_iot_policy" {
  name       = "EyecueDashboardIoTPolicy"
  depends_on = [module.iam_user]
  policy     = data.aws_iam_policy_document.eyecue_dashboard_iot_policy_document.json
}

resource "aws_iam_policy_attachment" "eyecue_dashboard_iot_policy_attachment" {
  name       = "EyecueDashboardIoTPolicyAttachment"
  users      = [var.aws_iam_user]
  policy_arn = aws_iam_policy.eyecue_dashboard_iot_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}

# ===============================================
# Read Only IoT User
# ===============================================
module "iam_user_read_only" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user_read_only
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

data "aws_iam_policy_document" "eyecue_dashboard_read_only_iot_policy_document" {
  statement {
    sid = "EyecueDashboardReadOnlyIoTPolicyDocument1"
    actions = [
      "iot:Connect"
    ]
    resources = [
      "arn:aws:iot:${var.aws_region}:${var.aws_account_id}:*"
    ]
  }

  statement {
    sid       = "EyecueDashboardReadOnlyIoTPolicyDocument2"
    actions   = ["iot:Subscribe"]
    resources = ["arn:aws:iot:${var.aws_region}:${var.aws_account_id}:topicfilter//eyeq/*"]
  }

  statement {
    sid       = "EyecueDashboardReadOnlyIoTPolicyDocument3"
    actions   = ["iot:Receive"]
    resources = ["arn:aws:iot:${var.aws_region}:${var.aws_account_id}:topic//eyeq/*"]
  }
}


resource "aws_iam_policy" "eyecue_dashboard_read_only_iot_policy" {
  name       = "EyecueDashboardReadOnlyIoTPolicy"
  depends_on = [module.iam_user_read_only]
  policy     = data.aws_iam_policy_document.eyecue_dashboard_read_only_iot_policy_document.json
}

resource "aws_iam_policy_attachment" "eyecue_dashboard_read_only_iot_policy_attachment" {
  name       = "EyecueDashboardReadOnlyIoTPolicyAttachment"
  users      = [var.aws_iam_user_read_only]
  policy_arn = aws_iam_policy.eyecue_dashboard_read_only_iot_policy.arn
}

resource "aws_secretsmanager_secret" "read_only_credentials" {
  name = "${var.aws_iam_user_read_only}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "read_only_credentials" {
  secret_id = aws_secretsmanager_secret.read_only_credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user_read_only.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user_read_only.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user_read_only.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user_read_only.keybase_secret_key_decrypt_command
  })
}
