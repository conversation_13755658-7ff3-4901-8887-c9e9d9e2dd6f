variable "aws_region" {
  description = "The AWS region to deploy resources in"
}

variable "aws_account_id" {
  description = "The AWS account ID"
}

variable "aws_iam_user" {
  type        = string
  default     = "eyecue-dashboard-iot"
  description = "The name of the IAM user to create"
}

variable "aws_iam_user_read_only" {
  type        = string
  default     = "eyecue-dashboard-iot-read-only"
  description = "The name of the IAM user to create for read-only access"
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
