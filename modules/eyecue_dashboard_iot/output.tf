output "user" {
  value = {
    "name" : module.iam_user.this_iam_user_name,
    "arn" : module.iam_user.this_iam_user_arn,
    "access_key" : module.iam_user.this_iam_access_key_id,
    "encrypted_secret_key" : module.iam_user.this_iam_access_key_encrypted_secret,
    "pgp_key" : module.iam_user.pgp_key,
    "keybase_command" : module.iam_user.keybase_secret_key_decrypt_command
  }
  sensitive = true
}

output "user_read_only" {
  value = {
    "name" : module.iam_user_read_only.this_iam_user_name,
    "arn" : module.iam_user_read_only.this_iam_user_arn,
    "access_key" : module.iam_user_read_only.this_iam_access_key_id,
    "encrypted_secret_key" : module.iam_user_read_only.this_iam_access_key_encrypted_secret,
    "pgp_key" : module.iam_user_read_only.pgp_key,
    "keybase_command" : module.iam_user_read_only.keybase_secret_key_decrypt_command
  }
  sensitive = true
}
