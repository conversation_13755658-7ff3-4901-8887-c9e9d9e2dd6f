# RDS Monitoring Module

Automatically discovers RDS instances and creates CloudWatch alarms for Vanta/SOC2 compliance.

## What it does

- Discovers all RDS instances and Aurora clusters in your AWS account
- Creates CloudWatch alarms for CPU, memory, IO, and storage metrics
- Only creates alarms if they don't already exist
- Runs on a schedule via EventBridge

## Vanta Tests Addressed

1. **SQL database CPU monitored** - Creates `CPUUtilization` alarms
2. **SQL database freeable memory monitored** - Creates `FreeableMemory` alarms
3. **Database IO monitored** - Creates `DiskQueueDepth` alarms
4. **SQL database free storage space monitored** - Creates appropriate storage alarms based on engine type

## Usage

```hcl
module "rds_monitoring" {
  source = "./modules/rds_monitoring"

  lambda_function_name = "rds-monitoring-remediation"
  sns_topic_arn       = "arn:aws:sns:us-east-1:************:cloudwatch-alarms"

  tags = {
    Environment = "production"
    Compliance  = "SOC2"
  }
}
```

## Default Configuration

The module uses sensible defaults for all monitoring thresholds:

- **CPU**: 80% threshold, 3 evaluation periods, 2-minute intervals
- **Memory**: 1GB threshold, 3 evaluation periods, 2-minute intervals
- **IO**: 10 queue depth threshold, 3 evaluation periods, 2-minute intervals
- **Storage**: 10GB threshold, 3 evaluation periods, 5-minute intervals
- **Schedule**: Runs every hour
- **Retention**: 14-day log retention

## Variables

See `variables.tf` for customizable parameters including thresholds, filtering, and scheduling options.
