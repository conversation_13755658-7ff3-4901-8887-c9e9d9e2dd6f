# ==========================================
# RDS Monitoring Module Outputs
# ==========================================

output "lambda_function_name" {
  description = "Name of the RDS monitoring Lambda function"
  value       = aws_lambda_function.rds_monitoring.function_name
}

output "lambda_function_arn" {
  description = "ARN of the RDS monitoring Lambda function"
  value       = aws_lambda_function.rds_monitoring.arn
}

output "lambda_execution_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = aws_iam_role.lambda_execution_role.arn
}

output "lambda_execution_role_name" {
  description = "Name of the Lambda execution role"
  value       = aws_iam_role.lambda_execution_role.name
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for Lambda logs"
  value       = aws_cloudwatch_log_group.lambda_logs.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for Lambda logs"
  value       = aws_cloudwatch_log_group.lambda_logs.arn
}

output "eventbridge_rule_name" {
  description = "Name of the EventBridge rule for scheduled execution"
  value       = aws_cloudwatch_event_rule.rds_monitoring_schedule.name
}

output "eventbridge_rule_arn" {
  description = "ARN of the EventBridge rule for scheduled execution"
  value       = aws_cloudwatch_event_rule.rds_monitoring_schedule.arn
}

output "schedule_expression" {
  description = "Schedule expression used for the EventBridge rule"
  value       = aws_cloudwatch_event_rule.rds_monitoring_schedule.schedule_expression
}
