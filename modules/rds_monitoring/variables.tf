# ==========================================
# RDS Monitoring Module Variables
# ==========================================

# ==========================================
# Lambda Function Configuration
# ==========================================

variable "lambda_function_name" {
  description = "Name of the Lambda function for RDS monitoring"
  type        = string
  default     = "rds-monitoring-remediation"
}

variable "lambda_timeout" {
  description = "Timeout for the Lambda function in seconds"
  type        = number
  default     = 300
}

variable "lambda_memory_size" {
  description = "Memory size for the Lambda function in MB"
  type        = number
  default     = 256
}

variable "log_retention_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 14
}

variable "log_level" {
  description = "Log level for the Lambda function (DEBUG, INFO, WARNING, ERROR)"
  type        = string
  default     = "INFO"
}

# ==========================================
# Scheduling Configuration
# ==========================================

variable "schedule_expression" {
  description = "EventBridge schedule expression for running the monitoring function"
  type        = string
  default     = "rate(1 hour)"
}

# ==========================================
# SNS Configuration
# ==========================================

variable "sns_topic_arn" {
  description = "SNS topic ARN for CloudWatch alarm notifications"
  type        = string
}

# ==========================================
# CPU Monitoring Configuration
# ==========================================

variable "cpu_alarm_threshold" {
  description = "CPU utilization threshold percentage for alarms"
  type        = number
  default     = 80
}

variable "cpu_alarm_evaluation_periods" {
  description = "Number of evaluation periods for CPU alarms"
  type        = number
  default     = 3
}

variable "cpu_alarm_period" {
  description = "Period in seconds for CPU alarm evaluation"
  type        = number
  default     = 120
}

variable "cpu_alarm_statistic" {
  description = "Statistic for CPU alarm evaluation"
  type        = string
  default     = "Average"
}

variable "cpu_alarm_name_prefix" {
  description = "Prefix for CPU CloudWatch alarm names"
  type        = string
  default     = "RDS-Dynamic-CPU-Utilization"
}

# ==========================================
# Memory Monitoring Configuration
# ==========================================

variable "memory_alarm_threshold" {
  description = "Freeable memory threshold in bytes for alarms"
  type        = number
  default     = 1073741824 # 1 GB
}

variable "memory_alarm_evaluation_periods" {
  description = "Number of evaluation periods for memory alarms"
  type        = number
  default     = 3
}

variable "memory_alarm_period" {
  description = "Period in seconds for memory alarm evaluation"
  type        = number
  default     = 120
}

variable "memory_alarm_statistic" {
  description = "Statistic for memory alarm evaluation"
  type        = string
  default     = "Average"
}

variable "memory_alarm_name_prefix" {
  description = "Prefix for memory CloudWatch alarm names"
  type        = string
  default     = "RDS-Dynamic-Freeable-Memory"
}

# ==========================================
# IO Monitoring Configuration
# ==========================================

variable "disk_queue_depth_threshold" {
  description = "Disk queue depth threshold for alarms"
  type        = number
  default     = 10
}

variable "disk_queue_depth_evaluation_periods" {
  description = "Number of evaluation periods for disk queue depth alarms"
  type        = number
  default     = 3
}

variable "disk_queue_depth_period" {
  description = "Period in seconds for disk queue depth alarm evaluation"
  type        = number
  default     = 120
}

variable "disk_queue_depth_statistic" {
  description = "Statistic for disk queue depth alarm evaluation"
  type        = string
  default     = "Average"
}

variable "disk_queue_depth_name_prefix" {
  description = "Prefix for disk queue depth CloudWatch alarm names"
  type        = string
  default     = "RDS-Dynamic-Disk-Queue-Depth"
}

# ==========================================
# Storage Monitoring Configuration
# ==========================================

variable "free_storage_threshold" {
  description = "Free storage space threshold in bytes for alarms"
  type        = number
  default     = 10737418240 # 10 GB
}

variable "free_storage_evaluation_periods" {
  description = "Number of evaluation periods for free storage alarms"
  type        = number
  default     = 3
}

variable "free_storage_period" {
  description = "Period in seconds for free storage alarm evaluation"
  type        = number
  default     = 300
}

variable "free_storage_statistic" {
  description = "Statistic for free storage alarm evaluation"
  type        = string
  default     = "Average"
}

variable "free_storage_name_prefix" {
  description = "Prefix for free storage CloudWatch alarm names"
  type        = string
  default     = "RDS-Dynamic-Free-Storage"
}

# ==========================================
# Filtering Configuration
# ==========================================

variable "db_instance_filter" {
  description = "Optional filter for DB instance identifiers (regex pattern)"
  type        = string
  default     = ""
}

variable "excluded_db_instances" {
  description = "List of DB instance identifiers to exclude from monitoring"
  type        = list(string)
  default     = []
}

# ==========================================
# General Alarm Configuration
# ==========================================

variable "treat_missing_data" {
  description = "How to treat missing data points in alarms"
  type        = string
  default     = "missing"

  validation {
    condition = contains([
      "missing",
      "ignore",
      "breaching",
      "notBreaching"
    ], var.treat_missing_data)
    error_message = "treat_missing_data must be one of: missing, ignore, breaching, notBreaching."
  }
}

# ==========================================
# Tags
# ==========================================

variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "default_tags" {
  description = "Default tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
