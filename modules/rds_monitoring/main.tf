# ==========================================
# RDS Monitoring Module
# ==========================================
# Automatically discovers RDS instances and creates CloudWatch alarms
# for Vanta/SOC2 compliance monitoring

# ==========================================
# Data Sources
# ==========================================

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# ==========================================
# Local Values
# ==========================================

locals {
  lambda_function_name = var.lambda_function_name
  lambda_zip_file      = "${path.module}/rds_monitoring_lambda.zip"

  tags = merge(var.default_tags, var.tags, {
    Module = "rds_monitoring"
  })
}

# ==========================================
# Lambda Function Package
# ==========================================

# Create ZIP file for Lambda function
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_file = "${path.module}/lambda_function.py"
  output_path = local.lambda_zip_file
}

# ==========================================
# IAM Role for Lambda Function
# ==========================================

resource "aws_iam_role" "lambda_execution_role" {
  name = "${local.lambda_function_name}-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = local.tags
}

# ==========================================
# IAM Policies for Lambda Function
# ==========================================

# Basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.lambda_execution_role.name
}

# Custom policy for RDS and CloudWatch access
resource "aws_iam_role_policy" "lambda_rds_monitoring_policy" {
  name = "${local.lambda_function_name}-policy"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "rds:DescribeDBInstances",
          "rds:DescribeDBClusters",
          "rds:ListTagsForResource"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricAlarm",
          "cloudwatch:DescribeAlarms",
          "cloudwatch:DeleteAlarms",
          "cloudwatch:ListTagsForResource",
          "cloudwatch:TagResource"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${local.lambda_function_name}*"
      }
    ]
  })
}

# ==========================================
# CloudWatch Log Group
# ==========================================

resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/${local.lambda_function_name}"
  retention_in_days = var.log_retention_days

  tags = local.tags
}

# ==========================================
# Lambda Function
# ==========================================

resource "aws_lambda_function" "rds_monitoring" {
  filename      = local.lambda_zip_file
  function_name = local.lambda_function_name
  role          = aws_iam_role.lambda_execution_role.arn
  handler       = "lambda_function.lambda_handler"
  runtime       = "python3.12"
  timeout       = var.lambda_timeout
  memory_size   = var.lambda_memory_size

  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      # CPU Monitoring Configuration
      CPU_ALARM_THRESHOLD          = var.cpu_alarm_threshold
      CPU_ALARM_EVALUATION_PERIODS = var.cpu_alarm_evaluation_periods
      CPU_ALARM_PERIOD             = var.cpu_alarm_period
      CPU_ALARM_STATISTIC          = var.cpu_alarm_statistic
      CPU_ALARM_NAME_PREFIX        = var.cpu_alarm_name_prefix

      # Memory Monitoring Configuration
      MEMORY_ALARM_THRESHOLD          = var.memory_alarm_threshold
      MEMORY_ALARM_EVALUATION_PERIODS = var.memory_alarm_evaluation_periods
      MEMORY_ALARM_PERIOD             = var.memory_alarm_period
      MEMORY_ALARM_STATISTIC          = var.memory_alarm_statistic
      MEMORY_ALARM_NAME_PREFIX        = var.memory_alarm_name_prefix

      # IO Monitoring Configuration
      DISK_QUEUE_DEPTH_THRESHOLD          = var.disk_queue_depth_threshold
      DISK_QUEUE_DEPTH_EVALUATION_PERIODS = var.disk_queue_depth_evaluation_periods
      DISK_QUEUE_DEPTH_PERIOD             = var.disk_queue_depth_period
      DISK_QUEUE_DEPTH_STATISTIC          = var.disk_queue_depth_statistic
      DISK_QUEUE_DEPTH_NAME_PREFIX        = var.disk_queue_depth_name_prefix

      # Storage Monitoring Configuration
      FREE_STORAGE_THRESHOLD          = var.free_storage_threshold
      FREE_STORAGE_EVALUATION_PERIODS = var.free_storage_evaluation_periods
      FREE_STORAGE_PERIOD             = var.free_storage_period
      FREE_STORAGE_STATISTIC          = var.free_storage_statistic
      FREE_STORAGE_NAME_PREFIX        = var.free_storage_name_prefix

      # General Configuration
      SNS_TOPIC_ARN         = var.sns_topic_arn
      TREAT_MISSING_DATA    = var.treat_missing_data
      DB_INSTANCE_FILTER    = var.db_instance_filter
      EXCLUDED_DB_INSTANCES = join(",", var.excluded_db_instances)

      # Logging
      LOG_LEVEL = var.log_level
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_iam_role_policy.lambda_rds_monitoring_policy,
    aws_cloudwatch_log_group.lambda_logs
  ]

  tags = local.tags
}

# ==========================================
# EventBridge Rule for Scheduled Execution
# ==========================================

resource "aws_cloudwatch_event_rule" "rds_monitoring_schedule" {
  name                = "${local.lambda_function_name}-schedule"
  description         = "Scheduled execution for RDS monitoring remediation"
  schedule_expression = var.schedule_expression

  tags = local.tags
}

# ==========================================
# EventBridge Target
# ==========================================

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.rds_monitoring_schedule.name
  target_id = "RDSMonitoringLambdaTarget"
  arn       = aws_lambda_function.rds_monitoring.arn
}

# ==========================================
# Lambda Permission for EventBridge
# ==========================================

resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.rds_monitoring.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.rds_monitoring_schedule.arn
}
