"""
RDS Monitoring Lambda Function

This Lambda function automatically discovers all RDS instances in an AWS account
and creates CloudWatch alarms for key metrics to ensure compliance with 
Vanta/SOC2 monitoring requirements:

1. SQL database CPU monitored - CPUUtilization alarms
2. SQL database freeable memory monitored - FreeableMemory alarms  
3. Database IO monitored - DiskQueueDepth alarms
4. SQL database free storage space monitored - FreeStorageSpace/FreeLocalStorage alarms

The function only creates alarms if they don't already exist, making it idempotent.
"""

import json
import logging
import os
import re
from typing import Dict, List, Optional, Tuple

import boto3
from botocore.exceptions import ClientError

# Configure logging
logger = logging.getLogger()
logger.setLevel(os.environ.get('LOG_LEVEL', 'INFO'))

# Initialize AWS clients
rds_client = boto3.client('rds')
cloudwatch_client = boto3.client('cloudwatch')


def get_env_var(name: str, default: str = '') -> str:
    """Get environment variable with optional default value."""
    return os.environ.get(name, default)


def get_env_list(name: str) -> List[str]:
    """Get environment variable as a list (comma-separated)."""
    value = get_env_var(name, '')
    return [item.strip() for item in value.split(',') if item.strip()]


def discover_rds_instances() -> List[Dict]:
    """
    Discover all RDS instances in the account.
    
    Returns:
        List of dictionaries containing RDS instance information
    """
    instances = []
    
    try:
        # Get all DB instances
        paginator = rds_client.get_paginator('describe_db_instances')
        
        for page in paginator.paginate():
            for db_instance in page['DBInstances']:
                instance_info = {
                    'identifier': db_instance['DBInstanceIdentifier'],
                    'engine': db_instance['Engine'],
                    'engine_version': db_instance['EngineVersion'],
                    'instance_class': db_instance['DBInstanceClass'],
                    'status': db_instance['DBInstanceStatus'],
                    'multi_az': db_instance.get('MultiAZ', False),
                    'storage_type': db_instance.get('StorageType', ''),
                    'allocated_storage': db_instance.get('AllocatedStorage', 0),
                    'is_aurora': False  # Regular RDS instance
                }
                instances.append(instance_info)
                
        # Get Aurora cluster instances
        cluster_paginator = rds_client.get_paginator('describe_db_clusters')
        
        for page in cluster_paginator.paginate():
            for cluster in page['DBClusters']:
                for member in cluster.get('DBClusterMembers', []):
                    # Find the corresponding instance info
                    instance_id = member['DBInstanceIdentifier']
                    for instance in instances:
                        if instance['identifier'] == instance_id:
                            instance['is_aurora'] = True
                            instance['cluster_identifier'] = cluster['DBClusterIdentifier']
                            break
                            
        logger.info(f"Discovered {len(instances)} RDS instances")
        return instances
        
    except ClientError as e:
        logger.exception(f"Error discovering RDS instances: {e}")
        raise


def should_monitor_instance(instance_id: str, instance_filter: str, excluded_instances: List[str]) -> bool:
    """
    Determine if an RDS instance should be monitored based on filters.
    
    Args:
        instance_id: RDS instance identifier
        instance_filter: Regex pattern for filtering instances
        excluded_instances: List of instance IDs to exclude
        
    Returns:
        True if instance should be monitored, False otherwise
    """
    # Check exclusion list
    if instance_id in excluded_instances:
        logger.info(f"Skipping excluded instance: {instance_id}")
        return False
    
    # Check filter pattern
    if instance_filter:
        try:
            if not re.search(instance_filter, instance_id):
                logger.info(f"Instance {instance_id} doesn't match filter pattern: {instance_filter}")
                return False
        except re.error as e:
            logger.warning(f"Invalid regex pattern '{instance_filter}': {e}")
            return True  # Continue monitoring if regex is invalid
    
    return True


def alarm_exists(alarm_name: str) -> bool:
    """
    Check if a CloudWatch alarm already exists.
    
    Args:
        alarm_name: Name of the alarm to check
        
    Returns:
        True if alarm exists, False otherwise
    """
    try:
        response = cloudwatch_client.describe_alarms(AlarmNames=[alarm_name])
        return len(response['MetricAlarms']) > 0
    except ClientError as e:
        logger.warning(f"Error checking alarm existence for {alarm_name}: {e}")
        return False


def create_cpu_alarm(instance: Dict, config: Dict) -> bool:
    """Create CPU utilization alarm for RDS instance."""
    alarm_name = f"{config['cpu_alarm_name_prefix']}-{instance['identifier']}"
    
    if alarm_exists(alarm_name):
        logger.info(f"CPU alarm already exists: {alarm_name}")
        return False
    
    try:
        alarm_params = {
            'AlarmName': alarm_name,
            'ComparisonOperator': 'GreaterThanThreshold',
            'EvaluationPeriods': int(config['cpu_alarm_evaluation_periods']),
            'MetricName': 'CPUUtilization',
            'Namespace': 'AWS/RDS',
            'Period': int(config['cpu_alarm_period']),
            'Statistic': config['cpu_alarm_statistic'],
            'Threshold': float(config['cpu_alarm_threshold']),
            'ActionsEnabled': True,
            'AlarmDescription': f'CPU utilization monitoring for RDS instance {instance["identifier"]} - Vanta/SOC2 compliance',
            'Dimensions': [
                {
                    'Name': 'DBInstanceIdentifier',
                    'Value': instance['identifier']
                }
            ],
            'Unit': 'Percent',
            'TreatMissingData': config['treat_missing_data'],
            'Tags': [
                {'Key': 'Purpose', 'Value': 'VantaSOC2Compliance'},
                {'Key': 'CreatedBy', 'Value': 'RDSMonitoringLambda'},
                {'Key': 'DBInstanceIdentifier', 'Value': instance['identifier']},
                {'Key': 'MetricType', 'Value': 'CPU'}
            ]
        }
        
        if config['sns_topic_arn']:
            alarm_params['AlarmActions'] = [config['sns_topic_arn']]
            alarm_params['OKActions'] = [config['sns_topic_arn']]
        
        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created CPU alarm: {alarm_name}")
        return True
        
    except ClientError as e:
        logger.exception(f"Error creating CPU alarm for {instance['identifier']}: {e}")
        return False


def create_memory_alarm(instance: Dict, config: Dict) -> bool:
    """Create freeable memory alarm for RDS instance."""
    alarm_name = f"{config['memory_alarm_name_prefix']}-{instance['identifier']}"
    
    if alarm_exists(alarm_name):
        logger.info(f"Memory alarm already exists: {alarm_name}")
        return False
    
    try:
        alarm_params = {
            'AlarmName': alarm_name,
            'ComparisonOperator': 'LessThanThreshold',
            'EvaluationPeriods': int(config['memory_alarm_evaluation_periods']),
            'MetricName': 'FreeableMemory',
            'Namespace': 'AWS/RDS',
            'Period': int(config['memory_alarm_period']),
            'Statistic': config['memory_alarm_statistic'],
            'Threshold': float(config['memory_alarm_threshold']),
            'ActionsEnabled': True,
            'AlarmDescription': f'Freeable memory monitoring for RDS instance {instance["identifier"]} - Vanta/SOC2 compliance',
            'Dimensions': [
                {
                    'Name': 'DBInstanceIdentifier',
                    'Value': instance['identifier']
                }
            ],
            'Unit': 'Bytes',
            'TreatMissingData': config['treat_missing_data'],
            'Tags': [
                {'Key': 'Purpose', 'Value': 'VantaSOC2Compliance'},
                {'Key': 'CreatedBy', 'Value': 'RDSMonitoringLambda'},
                {'Key': 'DBInstanceIdentifier', 'Value': instance['identifier']},
                {'Key': 'MetricType', 'Value': 'Memory'}
            ]
        }
        
        if config['sns_topic_arn']:
            alarm_params['AlarmActions'] = [config['sns_topic_arn']]
            alarm_params['OKActions'] = [config['sns_topic_arn']]
        
        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created memory alarm: {alarm_name}")
        return True
        
    except ClientError as e:
        logger.exception(f"Error creating memory alarm for {instance['identifier']}: {e}")
        return False


def create_disk_queue_depth_alarm(instance: Dict, config: Dict) -> bool:
    """Create disk queue depth alarm for RDS instance."""
    alarm_name = f"{config['disk_queue_depth_name_prefix']}-{instance['identifier']}"

    if alarm_exists(alarm_name):
        logger.info(f"Disk queue depth alarm already exists: {alarm_name}")
        return False

    try:
        alarm_params = {
            'AlarmName': alarm_name,
            'ComparisonOperator': 'GreaterThanThreshold',
            'EvaluationPeriods': int(config['disk_queue_depth_evaluation_periods']),
            'MetricName': 'DiskQueueDepth',
            'Namespace': 'AWS/RDS',
            'Period': int(config['disk_queue_depth_period']),
            'Statistic': config['disk_queue_depth_statistic'],
            'Threshold': float(config['disk_queue_depth_threshold']),
            'ActionsEnabled': True,
            'AlarmDescription': f'Disk queue depth monitoring for RDS instance {instance["identifier"]} - Vanta/SOC2 compliance',
            'Dimensions': [
                {
                    'Name': 'DBInstanceIdentifier',
                    'Value': instance['identifier']
                }
            ],
            'Unit': 'Count',
            'TreatMissingData': config['treat_missing_data'],
            'Tags': [
                {'Key': 'Purpose', 'Value': 'VantaSOC2Compliance'},
                {'Key': 'CreatedBy', 'Value': 'RDSMonitoringLambda'},
                {'Key': 'DBInstanceIdentifier', 'Value': instance['identifier']},
                {'Key': 'MetricType', 'Value': 'IO'}
            ]
        }

        if config['sns_topic_arn']:
            alarm_params['AlarmActions'] = [config['sns_topic_arn']]
            alarm_params['OKActions'] = [config['sns_topic_arn']]

        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created disk queue depth alarm: {alarm_name}")
        return True

    except ClientError as e:
        logger.exception(f"Error creating disk queue depth alarm for {instance['identifier']}: {e}")
        return False


def create_storage_alarm(instance: Dict, config: Dict) -> bool:
    """Create storage space alarm for RDS instance."""
    # Determine the appropriate metric based on engine and storage type
    if instance['is_aurora']:
        if 'mysql' in instance['engine'].lower():
            # Aurora MySQL uses AuroraVolumeBytesLeftTotal
            metric_name = 'AuroraVolumeBytesLeftTotal'
            alarm_suffix = 'Aurora-Volume-Bytes-Left'
        else:
            # Aurora PostgreSQL uses FreeLocalStorage
            metric_name = 'FreeLocalStorage'
            alarm_suffix = 'Free-Local-Storage'
    else:
        # Regular RDS instances use FreeStorageSpace
        metric_name = 'FreeStorageSpace'
        alarm_suffix = 'Free-Storage-Space'

    alarm_name = f"{config['free_storage_name_prefix']}-{alarm_suffix}-{instance['identifier']}"

    if alarm_exists(alarm_name):
        logger.info(f"Storage alarm already exists: {alarm_name}")
        return False

    try:
        alarm_params = {
            'AlarmName': alarm_name,
            'ComparisonOperator': 'LessThanThreshold',
            'EvaluationPeriods': int(config['free_storage_evaluation_periods']),
            'MetricName': metric_name,
            'Namespace': 'AWS/RDS',
            'Period': int(config['free_storage_period']),
            'Statistic': config['free_storage_statistic'],
            'Threshold': float(config['free_storage_threshold']),
            'ActionsEnabled': True,
            'AlarmDescription': f'{metric_name} monitoring for RDS instance {instance["identifier"]} - Vanta/SOC2 compliance',
            'Dimensions': [
                {
                    'Name': 'DBInstanceIdentifier',
                    'Value': instance['identifier']
                }
            ],
            'Unit': 'Bytes',
            'TreatMissingData': config['treat_missing_data'],
            'Tags': [
                {'Key': 'Purpose', 'Value': 'VantaSOC2Compliance'},
                {'Key': 'CreatedBy', 'Value': 'RDSMonitoringLambda'},
                {'Key': 'DBInstanceIdentifier', 'Value': instance['identifier']},
                {'Key': 'MetricType', 'Value': 'Storage'},
                {'Key': 'MetricName', 'Value': metric_name}
            ]
        }

        if config['sns_topic_arn']:
            alarm_params['AlarmActions'] = [config['sns_topic_arn']]
            alarm_params['OKActions'] = [config['sns_topic_arn']]

        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created storage alarm: {alarm_name} (metric: {metric_name})")
        return True

    except ClientError as e:
        logger.exception(f"Error creating storage alarm for {instance['identifier']}: {e}")
        return False


def lambda_handler(event, context):
    """
    Main Lambda handler function.

    Discovers RDS instances and creates CloudWatch alarms for:
    - CPU utilization (Vanta: SQL database CPU monitored)
    - Freeable memory (Vanta: SQL database freeable memory monitored)
    - Disk queue depth (Vanta: Database IO monitored)
    - Storage space (Vanta: SQL database free storage space monitored)
    """
    logger.info("Starting RDS monitoring remediation")

    try:
        # Load configuration from environment variables
        config = {
            # CPU Configuration
            'cpu_alarm_threshold': get_env_var('CPU_ALARM_THRESHOLD', '80'),
            'cpu_alarm_evaluation_periods': get_env_var('CPU_ALARM_EVALUATION_PERIODS', '3'),
            'cpu_alarm_period': get_env_var('CPU_ALARM_PERIOD', '120'),
            'cpu_alarm_statistic': get_env_var('CPU_ALARM_STATISTIC', 'Average'),
            'cpu_alarm_name_prefix': get_env_var('CPU_ALARM_NAME_PREFIX', 'RDS-Dynamic-CPU-Utilization'),

            # Memory Configuration
            'memory_alarm_threshold': get_env_var('MEMORY_ALARM_THRESHOLD', '1073741824'),  # 1 GB
            'memory_alarm_evaluation_periods': get_env_var('MEMORY_ALARM_EVALUATION_PERIODS', '3'),
            'memory_alarm_period': get_env_var('MEMORY_ALARM_PERIOD', '120'),
            'memory_alarm_statistic': get_env_var('MEMORY_ALARM_STATISTIC', 'Average'),
            'memory_alarm_name_prefix': get_env_var('MEMORY_ALARM_NAME_PREFIX', 'RDS-Dynamic-Freeable-Memory'),

            # Disk Queue Depth Configuration
            'disk_queue_depth_threshold': get_env_var('DISK_QUEUE_DEPTH_THRESHOLD', '10'),
            'disk_queue_depth_evaluation_periods': get_env_var('DISK_QUEUE_DEPTH_EVALUATION_PERIODS', '3'),
            'disk_queue_depth_period': get_env_var('DISK_QUEUE_DEPTH_PERIOD', '120'),
            'disk_queue_depth_statistic': get_env_var('DISK_QUEUE_DEPTH_STATISTIC', 'Average'),
            'disk_queue_depth_name_prefix': get_env_var('DISK_QUEUE_DEPTH_NAME_PREFIX', 'RDS-Dynamic-Disk-Queue-Depth'),

            # Storage Configuration
            'free_storage_threshold': get_env_var('FREE_STORAGE_THRESHOLD', '10737418240'),  # 10 GB
            'free_storage_evaluation_periods': get_env_var('FREE_STORAGE_EVALUATION_PERIODS', '3'),
            'free_storage_period': get_env_var('FREE_STORAGE_PERIOD', '300'),
            'free_storage_statistic': get_env_var('FREE_STORAGE_STATISTIC', 'Average'),
            'free_storage_name_prefix': get_env_var('FREE_STORAGE_NAME_PREFIX', 'RDS-Dynamic-Free-Storage'),

            # General Configuration
            'sns_topic_arn': get_env_var('SNS_TOPIC_ARN'),
            'treat_missing_data': get_env_var('TREAT_MISSING_DATA', 'missing'),
            'db_instance_filter': get_env_var('DB_INSTANCE_FILTER', ''),
            'excluded_db_instances': get_env_list('EXCLUDED_DB_INSTANCES')
        }

        logger.info(f"Configuration loaded: {json.dumps({k: v for k, v in config.items() if 'threshold' in k or 'prefix' in k})}")

        # Discover RDS instances
        instances = discover_rds_instances()

        if not instances:
            logger.info("No RDS instances found")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'No RDS instances found',
                    'total_instances': 0,
                    'instances_processed': 0,
                    'alarms_created': 0
                })
            }

        # Process each instance
        instances_processed = 0
        alarms_created = 0

        for instance in instances:
            instance_id = instance['identifier']

            # Apply filters
            if not should_monitor_instance(instance_id, config['db_instance_filter'], config['excluded_db_instances']):
                continue

            instances_processed += 1
            logger.info(f"Processing RDS instance: {instance_id} (engine: {instance['engine']}, aurora: {instance['is_aurora']})")

            # Create CPU alarm
            if create_cpu_alarm(instance, config):
                alarms_created += 1

            # Create memory alarm
            if create_memory_alarm(instance, config):
                alarms_created += 1

            # Create disk queue depth alarm (IO monitoring)
            if create_disk_queue_depth_alarm(instance, config):
                alarms_created += 1

            # Create storage alarm
            if create_storage_alarm(instance, config):
                alarms_created += 1

        result = {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'RDS monitoring remediation completed successfully',
                'total_instances': len(instances),
                'instances_processed': instances_processed,
                'alarms_created': alarms_created,
                'compliance_tests_addressed': [
                    'SQL database CPU monitored',
                    'SQL database freeable memory monitored (AWS)',
                    'Database IO monitored (AWS)',
                    'SQL database free storage space monitored (AWS)'
                ]
            })
        }

        logger.info(f"Remediation completed: {result['body']}")
        return result

    except Exception as e:
        logger.exception(f"Error in RDS monitoring remediation: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': 'RDS monitoring remediation failed',
                'error': str(e)
            })
        }
