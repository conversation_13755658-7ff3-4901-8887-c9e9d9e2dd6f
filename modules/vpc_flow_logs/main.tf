locals {
  vpc_ids = [
    for vpc in data.aws_vpcs.all.ids : vpc
    if length(data.aws_vpcs.all.ids) > 0
  ]
}

resource "aws_flow_log" "vpc_logs" {
  for_each             = toset(local.vpc_ids)
  log_destination      = var.log_destination
  log_destination_type = "s3"
  traffic_type         = "REJECT"
  vpc_id               = each.key
  tags                 = var.tags

  destination_options {
    file_format                = "parquet"
    hive_compatible_partitions = true
    per_hour_partition         = true
  }
}
