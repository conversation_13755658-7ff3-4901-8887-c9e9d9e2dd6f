# ==========================================
# ALB Monitoring Module
# ==========================================

# Data source for current AWS region and account
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# ==========================================
# Local Values
# ==========================================

locals {
  tags = merge(
    var.tags,
    {
      Module    = "alb-monitoring"
      Purpose   = "VantaCompliance"
      CreatedBy = "Terraform"
    }
  )
}

# ==========================================
# Lambda ZIP Archive
# ==========================================

data "archive_file" "lambda_zip" {
  count       = var.enable_lambda_function ? 1 : 0
  type        = "zip"
  source_file = "${path.module}/lambda_function.py"
  output_path = "${path.module}/alb_monitoring_lambda.zip"
}

# ==========================================
# IAM Role for Lambda
# ==========================================

resource "aws_iam_role" "lambda_execution_role" {
  count = var.enable_lambda_function ? 1 : 0
  name  = "${var.lambda_function_name}-execution-role"
  tags  = local.tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# ==========================================
# IAM Policy for ALB and CloudWatch Access
# ==========================================

resource "aws_iam_policy" "lambda_alb_cloudwatch_policy" {
  count       = var.enable_lambda_function ? 1 : 0
  name        = "${var.lambda_function_name}-alb-cloudwatch-policy"
  description = "Policy for ALB monitoring Lambda to access ALB and CloudWatch services"
  tags        = local.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "elasticloadbalancing:DescribeLoadBalancers",
          "elasticloadbalancing:DescribeListeners",
          "elasticloadbalancing:DescribeRules",
          "elasticloadbalancing:DescribeTargetGroups",
          "elasticloadbalancing:DescribeTargetHealth"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:DescribeAlarms",
          "cloudwatch:DescribeAlarmsForMetric",
          "cloudwatch:PutMetricAlarm",
          "cloudwatch:TagResource"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.lambda_function_name}*"
      }
    ]
  })
}

# ==========================================
# SNS Policy (if SNS topic is provided)
# ==========================================

resource "aws_iam_policy" "lambda_sns_policy" {
  count       = var.enable_lambda_function && var.sns_topic_arn != null ? 1 : 0
  name        = "${var.lambda_function_name}-sns-policy"
  description = "Policy for ALB monitoring Lambda to publish to SNS"
  tags        = local.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = var.sns_topic_arn
      }
    ]
  })
}

# ==========================================
# IAM Policy Attachments
# ==========================================

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  count      = var.enable_lambda_function ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "lambda_alb_cloudwatch" {
  count      = var.enable_lambda_function ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = aws_iam_policy.lambda_alb_cloudwatch_policy[0].arn
}

resource "aws_iam_role_policy_attachment" "lambda_sns" {
  count      = var.enable_lambda_function && var.sns_topic_arn != null ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = aws_iam_policy.lambda_sns_policy[0].arn
}

# ==========================================
# CloudWatch Log Group
# ==========================================

resource "aws_cloudwatch_log_group" "lambda_logs" {
  count             = var.enable_lambda_function ? 1 : 0
  name              = "/aws/lambda/${var.lambda_function_name}"
  retention_in_days = var.log_retention_days
  tags              = local.tags
}

# ==========================================
# Lambda Function
# ==========================================

resource "aws_lambda_function" "alb_monitoring" {
  count            = var.enable_lambda_function ? 1 : 0
  filename         = data.archive_file.lambda_zip[0].output_path
  function_name    = var.lambda_function_name
  role             = aws_iam_role.lambda_execution_role[0].arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.12"
  timeout          = var.lambda_timeout
  memory_size      = var.lambda_memory_size
  source_code_hash = data.archive_file.lambda_zip[0].output_base64sha256
  tags             = local.tags

  environment {
    variables = {
      # Unhealthy Host Count Configuration
      UNHEALTHY_HOST_ALARM_NAME_PREFIX   = var.unhealthy_host_alarm_name_prefix
      UNHEALTHY_HOST_THRESHOLD           = var.unhealthy_host_threshold
      UNHEALTHY_HOST_EVALUATION_PERIODS  = var.unhealthy_host_evaluation_periods
      UNHEALTHY_HOST_PERIOD              = var.unhealthy_host_period
      UNHEALTHY_HOST_STATISTIC           = var.unhealthy_host_statistic
      UNHEALTHY_HOST_COMPARISON_OPERATOR = var.unhealthy_host_comparison_operator

      # Server Error Configuration
      SERVER_ERROR_ALARM_NAME_PREFIX   = var.server_error_alarm_name_prefix
      SERVER_ERROR_THRESHOLD           = var.server_error_threshold
      SERVER_ERROR_EVALUATION_PERIODS  = var.server_error_evaluation_periods
      SERVER_ERROR_PERIOD              = var.server_error_period
      SERVER_ERROR_STATISTIC           = var.server_error_statistic
      SERVER_ERROR_COMPARISON_OPERATOR = var.server_error_comparison_operator

      # Latency Configuration
      LATENCY_ALARM_NAME_PREFIX   = var.latency_alarm_name_prefix
      LATENCY_THRESHOLD           = var.latency_threshold
      LATENCY_EVALUATION_PERIODS  = var.latency_evaluation_periods
      LATENCY_PERIOD              = var.latency_period
      LATENCY_STATISTIC           = var.latency_statistic
      LATENCY_COMPARISON_OPERATOR = var.latency_comparison_operator

      # General Configuration
      TREAT_MISSING_DATA    = var.treat_missing_data
      SNS_TOPIC_ARN         = var.sns_topic_arn
      ALB_NAME_FILTER       = var.alb_name_filter
      EXCLUDED_ALB_NAMES    = join(",", var.excluded_alb_names)
      MONITOR_INTERNAL_ALBS = var.monitor_internal_albs
    }
  }

  depends_on = [
    aws_cloudwatch_log_group.lambda_logs,
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_iam_role_policy_attachment.lambda_alb_cloudwatch
  ]
}

# ==========================================
# EventBridge Rule for Scheduling
# ==========================================

resource "aws_cloudwatch_event_rule" "alb_monitoring_schedule" {
  count               = var.enable_lambda_function ? 1 : 0
  name                = "${var.lambda_function_name}-schedule"
  description         = "Trigger ALB monitoring Lambda on schedule"
  schedule_expression = var.schedule_expression
  tags                = local.tags
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  count     = var.enable_lambda_function ? 1 : 0
  rule      = aws_cloudwatch_event_rule.alb_monitoring_schedule[0].name
  target_id = "ALBMonitoringLambdaTarget"
  arn       = aws_lambda_function.alb_monitoring[0].arn
}

resource "aws_lambda_permission" "allow_eventbridge" {
  count         = var.enable_lambda_function ? 1 : 0
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.alb_monitoring[0].function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.alb_monitoring_schedule[0].arn
}
