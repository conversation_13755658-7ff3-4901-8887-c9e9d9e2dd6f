# ALB Monitoring Mo<PERSON><PERSON>

Automatically discovers ALBs and creates CloudWatch alarms for Vanta SOC2 compliance.

## What it does

- Discovers all ALBs in your AWS account
- Creates CloudWatch alarms for unhealthy hosts, server errors, and latency
- Validates HTTP to HTTPS redirects and reports violations
- Only creates alarms if they don't already exist
- Runs on a schedule via EventBridge

## Vanta Tests Addressed

1. **Load balancer unhealthy host count monitored** - Creates `UnHealthyHostCount` alarms
2. **Load balancers redirect HTTP to HTTPS** - Validates redirect configuration
3. **Load balancer server errors monitored** - Creates `HTTPCode_ELB_5XX_Count` alarms
4. **Load balancer latency monitored** - Creates `TargetResponseTime` alarms

## Usage

```hcl
module "alb_monitoring" {
  source = "./modules/alb_monitoring"

  lambda_function_name = "alb-monitoring-remediation"
  sns_topic_arn       = "arn:aws:sns:us-east-1:************:cloudwatch-alarms"

  tags = {
    Environment = "production"
    Compliance  = "Vanta"
  }
}
```

## Key Variables

| Name | Description | Default |
|------|-------------|---------|
| `lambda_function_name` | Name of the Lambda function | `"alb-monitoring-remediation"` |
| `sns_topic_arn` | SNS topic ARN for notifications | `null` |
| `unhealthy_host_threshold` | Unhealthy host count threshold | `1` |
| `server_error_threshold` | Server error count threshold | `10` |
| `latency_threshold` | Latency threshold in seconds | `1.0` |
| `excluded_alb_names` | List of ALB names to exclude | `[]` |
| `tags` | Tags for all resources | `{}` |
