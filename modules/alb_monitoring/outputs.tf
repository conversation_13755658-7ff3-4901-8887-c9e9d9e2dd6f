# ==========================================
# ALB Monitoring Module Outputs
# ==========================================

output "lambda_function_arn" {
  description = "ARN of the ALB monitoring Lambda function"
  value       = var.enable_lambda_function ? aws_lambda_function.alb_monitoring[0].arn : null
}

output "lambda_function_name" {
  description = "Name of the ALB monitoring Lambda function"
  value       = var.enable_lambda_function ? aws_lambda_function.alb_monitoring[0].function_name : null
}

output "lambda_execution_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = var.enable_lambda_function ? aws_iam_role.lambda_execution_role[0].arn : null
}

output "lambda_execution_role_name" {
  description = "Name of the Lambda execution role"
  value       = var.enable_lambda_function ? aws_iam_role.lambda_execution_role[0].name : null
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for Lambda logs"
  value       = var.enable_lambda_function ? aws_cloudwatch_log_group.lambda_logs[0].name : null
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for Lambda logs"
  value       = var.enable_lambda_function ? aws_cloudwatch_log_group.lambda_logs[0].arn : null
}

output "eventbridge_rule_arn" {
  description = "ARN of the EventBridge rule for scheduling"
  value       = var.enable_lambda_function ? aws_cloudwatch_event_rule.alb_monitoring_schedule[0].arn : null
}

output "eventbridge_rule_name" {
  description = "Name of the EventBridge rule for scheduling"
  value       = var.enable_lambda_function ? aws_cloudwatch_event_rule.alb_monitoring_schedule[0].name : null
}

output "module_configuration" {
  description = "Configuration summary of the module"
  value = {
    alarm_name_prefixes = {
      unhealthy_host = var.unhealthy_host_alarm_name_prefix
      server_error   = var.server_error_alarm_name_prefix
      latency        = var.latency_alarm_name_prefix
    }
    thresholds = {
      unhealthy_host = var.unhealthy_host_threshold
      server_error   = var.server_error_threshold
      latency        = var.latency_threshold
    }
    alarm_settings = {
      treat_missing_data = var.treat_missing_data
      sns_topic_arn      = var.sns_topic_arn
    }
    filtering = {
      alb_name_filter       = var.alb_name_filter
      excluded_alb_names    = var.excluded_alb_names
      monitor_internal_albs = var.monitor_internal_albs
    }
    lambda_settings = {
      function_name = var.lambda_function_name
      timeout       = var.lambda_timeout
      memory_size   = var.lambda_memory_size
      enabled       = var.enable_lambda_function
    }
    scheduling = {
      schedule_expression = var.schedule_expression
    }
  }
}
