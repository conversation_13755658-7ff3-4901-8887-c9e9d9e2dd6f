"""
ALB Monitoring Lambda Function

This Lambda function automatically discovers all Application Load Balancers (ALBs) in an AWS account
and creates CloudWatch alarms for Vanta compliance tests:

1. Load balancer unhealthy host count monitored (AWS)
2. Load balancers redirect HTTP to HTTPS (AWS) - Validation only, no alarms created
3. Load balancer server errors monitored (AWS)
4. Load balancer latency monitored (AWS)

Environment Variables:
- UNHEALTHY_HOST_ALARM_NAME_PREFIX: Prefix for unhealthy host alarms
- UNHEALTHY_HOST_THRESHOLD: Threshold for unhealthy host count
- UNHEALTHY_HOST_EVALUATION_PERIODS: Evaluation periods for unhealthy host alarms
- UNHEALTHY_HOST_PERIOD: Period for unhealthy host alarms
- UNHEALTHY_HOST_STATISTIC: Statistic for unhealthy host alarms
- UNHEALTHY_HOST_COMPARISON_OPERATOR: Comparison operator for unhealthy host alarms
- SERVER_ERROR_ALARM_NAME_PREFIX: Prefix for server error alarms
- SERVER_ERROR_THRESHOLD: Threshold for server error count
- SERVER_ERROR_EVALUATION_PERIODS: Evaluation periods for server error alarms
- SERVER_ERROR_PERIOD: Period for server error alarms
- SERVER_ERROR_STATISTIC: Statistic for server error alarms
- SERVER_ERROR_COMPARISON_OPERATOR: Comparison operator for server error alarms
- LATENCY_ALARM_NAME_PREFIX: Prefix for latency alarms
- LATENCY_THRESHOLD: Threshold for latency in seconds
- LATENCY_EVALUATION_PERIODS: Evaluation periods for latency alarms
- LATENCY_PERIOD: Period for latency alarms
- LATENCY_STATISTIC: Statistic for latency alarms
- LATENCY_COMPARISON_OPERATOR: Comparison operator for latency alarms
- TREAT_MISSING_DATA: How to treat missing data
- SNS_TOPIC_ARN: SNS topic ARN for notifications (optional)
- ALB_NAME_FILTER: Regex pattern to filter ALB names (optional)
- EXCLUDED_ALB_NAMES: Comma-separated list of ALB names to exclude (optional)
- MONITOR_INTERNAL_ALBS: Whether to monitor internal ALBs (default: true)
"""

import json
import logging
import os
import re
import boto3
from botocore.exceptions import ClientError
from typing import List, Dict, Tuple

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
elbv2_client = boto3.client('elbv2')
cloudwatch_client = boto3.client('cloudwatch')

def get_env_var(name: str, default: str = None, required: bool = False) -> str:
    """Get environment variable with optional default and required validation."""
    value = os.environ.get(name, default)
    if required and not value:
        raise ValueError(f"Required environment variable {name} is not set")
    return value

def get_env_list(name: str, default: List[str] = None) -> List[str]:
    """Get environment variable as a list (comma-separated)."""
    value = os.environ.get(name, "")
    if not value:
        return default or []
    return [item.strip() for item in value.split(",") if item.strip()]

def get_env_bool(name: str, default: bool = True) -> bool:
    """Get environment variable as boolean."""
    value = os.environ.get(name, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')

def list_all_albs() -> List[Dict]:
    """List all Application Load Balancers in the account."""
    try:
        albs = []
        paginator = elbv2_client.get_paginator('describe_load_balancers')

        for page in paginator.paginate():
            for lb in page.get('LoadBalancers', []):
                # Only process Application Load Balancers
                if lb.get('Type') == 'application':
                    alb_info = {
                        'name': lb['LoadBalancerName'],
                        'arn': lb['LoadBalancerArn'],
                        'scheme': lb.get('Scheme', 'internet-facing'),
                        'state': lb.get('State', {}).get('Code', 'unknown'),
                        'vpc_id': lb.get('VpcId'),
                        'availability_zones': lb.get('AvailabilityZones', [])
                    }
                    albs.append(alb_info)

        logger.info(f"Found {len(albs)} Application Load Balancers")
        return albs

    except ClientError as e:
        logger.exception(f"Error listing ALBs: {e}")
        raise

def get_alb_listeners(alb_arn: str) -> List[Dict]:
    """Get listeners for an ALB."""
    try:
        response = elbv2_client.describe_listeners(LoadBalancerArn=alb_arn)
        return response.get('Listeners', [])
    except ClientError as e:
        logger.warning(f"Error getting listeners for ALB {alb_arn}: {e}")
        return []

def check_http_to_https_redirect(alb_info: Dict) -> Tuple[bool, str]:
    """Check if ALB redirects HTTP to HTTPS (Vanta Test 2)."""
    if alb_info['scheme'] != 'internet-facing':
        return True, "Internal ALB - HTTP redirect check not applicable"
    
    listeners = get_alb_listeners(alb_info['arn'])
    http_listeners = [l for l in listeners if l.get('Protocol') == 'HTTP']
    
    if not http_listeners:
        return True, "No HTTP listeners found"
    
    for listener in http_listeners:
        # Check if listener has redirect action to HTTPS
        default_actions = listener.get('DefaultActions', [])
        has_redirect = any(
            action.get('Type') == 'redirect' and 
            action.get('RedirectConfig', {}).get('Protocol') == 'HTTPS'
            for action in default_actions
        )
        
        if not has_redirect:
            return False, f"HTTP listener {listener['ListenerArn']} does not redirect to HTTPS"
    
    return True, "All HTTP listeners redirect to HTTPS"

def should_monitor_alb(alb_name: str, alb_scheme: str, name_filter: str, excluded_names: List[str], monitor_internal: bool) -> bool:
    """Determine if an ALB should be monitored based on filters."""
    # Check if ALB is in exclusion list
    if alb_name in excluded_names:
        logger.info(f"Skipping excluded ALB: {alb_name}")
        return False

    # Check if we should monitor internal ALBs
    if alb_scheme == 'internal' and not monitor_internal:
        logger.info(f"Skipping internal ALB: {alb_name}")
        return False

    # Check name filter if provided
    if name_filter:
        try:
            if not re.match(name_filter, alb_name):
                logger.info(f"ALB {alb_name} doesn't match filter pattern: {name_filter}")
                return False
        except re.error as e:
            logger.warning(f"Invalid regex pattern '{name_filter}': {e}")
            raise ValueError(f"Invalid ALB_NAME_FILTER regex pattern '{name_filter}': {e}")

    return True

def alarm_exists(alb_name: str, alarm_name_prefix: str, metric_name: str) -> bool:
    """Check if CloudWatch alarm already exists for the ALB and metric."""
    alarm_name = f"{alarm_name_prefix}-{alb_name}"

    try:
        response = cloudwatch_client.describe_alarms_for_metric(
            Namespace='AWS/ApplicationELB',
            MetricName=metric_name,
            Dimensions=[
                {
                    'Name': 'LoadBalancer',
                    'Value': alb_name
                }
            ]
        )

        # Check if any alarm matches our naming pattern
        for alarm in response.get('MetricAlarms', []):
            if alarm['AlarmName'] == alarm_name:
                logger.info(f"Alarm already exists for ALB {alb_name}: {alarm_name}")
                return True

        return False

    except ClientError as e:
        logger.exception(f"Error checking existing alarms for ALB {alb_name}: {e}")
        return False

def create_cloudwatch_alarm(alb_name: str, metric_name: str, config: Dict, alarm_type: str) -> bool:
    """Create CloudWatch alarm for ALB metric."""
    alarm_name = f"{config['alarm_name_prefix']}-{alb_name}"

    alarm_params = {
        'AlarmName': alarm_name,
        'ComparisonOperator': config['comparison_operator'],
        'EvaluationPeriods': config['evaluation_periods'],
        'MetricName': metric_name,
        'Namespace': 'AWS/ApplicationELB',
        'Period': config['period'],
        'Statistic': config['statistic'],
        'Threshold': config['threshold'],
        'ActionsEnabled': True,
        'AlarmDescription': f'Monitors {alarm_type} for ALB {alb_name} - Vanta compliance',
        'Dimensions': [
            {
                'Name': 'LoadBalancer',
                'Value': alb_name
            }
        ],
        'TreatMissingData': config['treat_missing_data'],
        'Tags': [
            {'Key': 'Purpose', 'Value': 'VantaCompliance'},
            {'Key': 'CreatedBy', 'Value': 'ALBMonitoringLambda'},
            {'Key': 'LoadBalancerName', 'Value': alb_name},
            {'Key': 'AlarmType', 'Value': alarm_type}
        ]
    }

    # Add unit for latency metrics
    if metric_name == 'TargetResponseTime':
        alarm_params['Unit'] = 'Seconds'

    # Add SNS topic if provided
    if config.get('sns_topic_arn'):
        alarm_params['AlarmActions'] = [config['sns_topic_arn']]
        alarm_params['OKActions'] = [config['sns_topic_arn']]

    try:
        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created CloudWatch alarm: {alarm_name}")
        return True

    except ClientError as e:
        logger.exception(f"Error creating {alarm_type} alarm for ALB {alb_name}: {e}")
        return False

def lambda_handler(event, context):
    """Main Lambda handler function."""
    logger.info("Starting ALB monitoring remediation")

    try:
        # Load configuration from environment variables
        config = {
            'unhealthy_host': {
                'alarm_name_prefix': get_env_var('UNHEALTHY_HOST_ALARM_NAME_PREFIX', 'ALB-UnhealthyHostCount'),
                'threshold': float(get_env_var('UNHEALTHY_HOST_THRESHOLD', '1')),
                'evaluation_periods': int(get_env_var('UNHEALTHY_HOST_EVALUATION_PERIODS', '2')),
                'period': int(get_env_var('UNHEALTHY_HOST_PERIOD', '300')),
                'statistic': get_env_var('UNHEALTHY_HOST_STATISTIC', 'Average'),
                'comparison_operator': get_env_var('UNHEALTHY_HOST_COMPARISON_OPERATOR', 'GreaterThanOrEqualToThreshold'),
                'treat_missing_data': get_env_var('TREAT_MISSING_DATA', 'missing'),
                'sns_topic_arn': get_env_var('SNS_TOPIC_ARN')
            },
            'server_error': {
                'alarm_name_prefix': get_env_var('SERVER_ERROR_ALARM_NAME_PREFIX', 'ALB-ServerErrors5XX'),
                'threshold': float(get_env_var('SERVER_ERROR_THRESHOLD', '10')),
                'evaluation_periods': int(get_env_var('SERVER_ERROR_EVALUATION_PERIODS', '2')),
                'period': int(get_env_var('SERVER_ERROR_PERIOD', '300')),
                'statistic': get_env_var('SERVER_ERROR_STATISTIC', 'Sum'),
                'comparison_operator': get_env_var('SERVER_ERROR_COMPARISON_OPERATOR', 'GreaterThanThreshold'),
                'treat_missing_data': get_env_var('TREAT_MISSING_DATA', 'missing'),
                'sns_topic_arn': get_env_var('SNS_TOPIC_ARN')
            },
            'latency': {
                'alarm_name_prefix': get_env_var('LATENCY_ALARM_NAME_PREFIX', 'ALB-TargetResponseTime'),
                'threshold': float(get_env_var('LATENCY_THRESHOLD', '1.0')),
                'evaluation_periods': int(get_env_var('LATENCY_EVALUATION_PERIODS', '2')),
                'period': int(get_env_var('LATENCY_PERIOD', '300')),
                'statistic': get_env_var('LATENCY_STATISTIC', 'Average'),
                'comparison_operator': get_env_var('LATENCY_COMPARISON_OPERATOR', 'GreaterThanThreshold'),
                'treat_missing_data': get_env_var('TREAT_MISSING_DATA', 'missing'),
                'sns_topic_arn': get_env_var('SNS_TOPIC_ARN')
            },
            'filtering': {
                'alb_name_filter': get_env_var('ALB_NAME_FILTER', ''),
                'excluded_alb_names': get_env_list('EXCLUDED_ALB_NAMES'),
                'monitor_internal_albs': get_env_bool('MONITOR_INTERNAL_ALBS', True)
            }
        }

        logger.info("Configuration loaded successfully")

        # Get all ALBs
        albs = list_all_albs()

        if not albs:
            logger.info("No Application Load Balancers found in the account")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'No ALBs found',
                    'albs_processed': 0,
                    'alarms_created': 0,
                    'http_redirect_violations': 0
                })
            }

        # Process each ALB
        albs_processed = 0
        alarms_created = 0
        http_redirect_violations = 0
        violations = []

        for alb in albs:
            alb_name = alb['name']
            alb_scheme = alb['scheme']

            # Apply filters
            if not should_monitor_alb(
                alb_name,
                alb_scheme,
                config['filtering']['alb_name_filter'],
                config['filtering']['excluded_alb_names'],
                config['filtering']['monitor_internal_albs']
            ):
                continue

            albs_processed += 1
            logger.info(f"Processing ALB: {alb_name} (scheme: {alb_scheme})")

            # Check HTTP to HTTPS redirect (Vanta Test 2)
            redirect_compliant, redirect_message = check_http_to_https_redirect(alb)
            if not redirect_compliant:
                http_redirect_violations += 1
                violations.append({
                    'alb_name': alb_name,
                    'violation_type': 'HTTP_REDIRECT',
                    'message': redirect_message
                })
                logger.warning(f"HTTP redirect violation for ALB {alb_name}: {redirect_message}")

            # Create alarms for monitoring metrics

            # 1. Unhealthy Host Count (Vanta Test 1)
            if not alarm_exists(alb_name, config['unhealthy_host']['alarm_name_prefix'], 'UnHealthyHostCount'):
                if create_cloudwatch_alarm(alb_name, 'UnHealthyHostCount', config['unhealthy_host'], 'unhealthy_host_count'):
                    alarms_created += 1

            # 2. Server Errors 5XX (Vanta Test 3)
            if not alarm_exists(alb_name, config['server_error']['alarm_name_prefix'], 'HTTPCode_ELB_5XX_Count'):
                if create_cloudwatch_alarm(alb_name, 'HTTPCode_ELB_5XX_Count', config['server_error'], 'server_errors_5xx'):
                    alarms_created += 1

            # 3. Latency (Vanta Test 4)
            if not alarm_exists(alb_name, config['latency']['alarm_name_prefix'], 'TargetResponseTime'):
                if create_cloudwatch_alarm(alb_name, 'TargetResponseTime', config['latency'], 'latency'):
                    alarms_created += 1

        result = {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'ALB monitoring remediation completed successfully',
                'total_albs': len(albs),
                'albs_processed': albs_processed,
                'alarms_created': alarms_created,
                'http_redirect_violations': http_redirect_violations,
                'violations': violations
            })
        }

        logger.info(f"Remediation completed: {result['body']}")
        return result

    except Exception as e:
        logger.exception(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'message': 'ALB monitoring remediation failed'
            })
        }
