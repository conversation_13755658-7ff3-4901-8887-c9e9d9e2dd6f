# ==========================================
# ALB Monitoring Module Variables
# ==========================================

# ==========================================
# Alarm Configuration - Unhealthy Host Count
# ==========================================

variable "unhealthy_host_alarm_name_prefix" {
  description = "Prefix for unhealthy host count CloudWatch alarm names"
  type        = string
  default     = "ALB-UnhealthyHostCount"
}

variable "unhealthy_host_threshold" {
  description = "Threshold for unhealthy host count alarm"
  type        = number
  default     = 1
}

variable "unhealthy_host_evaluation_periods" {
  description = "Number of evaluation periods for unhealthy host alarm"
  type        = number
  default     = 2
}

variable "unhealthy_host_period" {
  description = "Period in seconds for unhealthy host alarm evaluation"
  type        = number
  default     = 300
}

variable "unhealthy_host_statistic" {
  description = "Statistic to use for unhealthy host alarm"
  type        = string
  default     = "Average"
  validation {
    condition     = contains(["Average", "Maximum", "Minimum", "Sum", "SampleCount"], var.unhealthy_host_statistic)
    error_message = "Alarm statistic must be one of: Average, Maximum, Minimum, Sum, SampleCount."
  }
}

variable "unhealthy_host_comparison_operator" {
  description = "Comparison operator for unhealthy host alarm"
  type        = string
  default     = "GreaterThanOrEqualToThreshold"
  validation {
    condition = contains([
      "GreaterThanOrEqualToThreshold",
      "GreaterThanThreshold",
      "LessThanThreshold",
      "LessThanOrEqualToThreshold"
    ], var.unhealthy_host_comparison_operator)
    error_message = "Comparison operator must be one of: GreaterThanOrEqualToThreshold, GreaterThanThreshold, LessThanThreshold, LessThanOrEqualToThreshold."
  }
}

# ==========================================
# Alarm Configuration - Server Errors (5XX)
# ==========================================

variable "server_error_alarm_name_prefix" {
  description = "Prefix for server error (5XX) CloudWatch alarm names"
  type        = string
  default     = "ALB-ServerErrors5XX"
}

variable "server_error_threshold" {
  description = "Threshold for server error count alarm"
  type        = number
  default     = 10
}

variable "server_error_evaluation_periods" {
  description = "Number of evaluation periods for server error alarm"
  type        = number
  default     = 2
}

variable "server_error_period" {
  description = "Period in seconds for server error alarm evaluation"
  type        = number
  default     = 300
}

variable "server_error_statistic" {
  description = "Statistic to use for server error alarm"
  type        = string
  default     = "Sum"
  validation {
    condition     = contains(["Average", "Maximum", "Minimum", "Sum", "SampleCount"], var.server_error_statistic)
    error_message = "Alarm statistic must be one of: Average, Maximum, Minimum, Sum, SampleCount."
  }
}

variable "server_error_comparison_operator" {
  description = "Comparison operator for server error alarm"
  type        = string
  default     = "GreaterThanThreshold"
  validation {
    condition = contains([
      "GreaterThanOrEqualToThreshold",
      "GreaterThanThreshold",
      "LessThanThreshold",
      "LessThanOrEqualToThreshold"
    ], var.server_error_comparison_operator)
    error_message = "Comparison operator must be one of: GreaterThanOrEqualToThreshold, GreaterThanThreshold, LessThanThreshold, LessThanOrEqualToThreshold."
  }
}

# ==========================================
# Alarm Configuration - Latency
# ==========================================

variable "latency_alarm_name_prefix" {
  description = "Prefix for latency CloudWatch alarm names"
  type        = string
  default     = "ALB-TargetResponseTime"
}

variable "latency_threshold" {
  description = "Threshold for latency alarm in seconds"
  type        = number
  default     = 1.0
}

variable "latency_evaluation_periods" {
  description = "Number of evaluation periods for latency alarm"
  type        = number
  default     = 2
}

variable "latency_period" {
  description = "Period in seconds for latency alarm evaluation"
  type        = number
  default     = 300
}

variable "latency_statistic" {
  description = "Statistic to use for latency alarm"
  type        = string
  default     = "Average"
  validation {
    condition     = contains(["Average", "Maximum", "Minimum", "Sum", "SampleCount"], var.latency_statistic)
    error_message = "Alarm statistic must be one of: Average, Maximum, Minimum, Sum, SampleCount."
  }
}

variable "latency_comparison_operator" {
  description = "Comparison operator for latency alarm"
  type        = string
  default     = "GreaterThanThreshold"
  validation {
    condition = contains([
      "GreaterThanOrEqualToThreshold",
      "GreaterThanThreshold",
      "LessThanThreshold",
      "LessThanOrEqualToThreshold"
    ], var.latency_comparison_operator)
    error_message = "Comparison operator must be one of: GreaterThanOrEqualToThreshold, GreaterThanThreshold, LessThanThreshold, LessThanOrEqualToThreshold."
  }
}

# ==========================================
# General Alarm Configuration
# ==========================================

variable "treat_missing_data" {
  description = "How to treat missing data for alarms"
  type        = string
  default     = "missing"
  validation {
    condition     = contains(["missing", "ignore", "breaching", "notBreaching"], var.treat_missing_data)
    error_message = "treat_missing_data must be one of: missing, ignore, breaching, notBreaching."
  }
}

variable "sns_topic_arn" {
  description = "SNS topic ARN for alarm notifications"
  type        = string
  default     = null
}

# ==========================================
# Lambda Configuration
# ==========================================

variable "lambda_function_name" {
  description = "Name of the Lambda function"
  type        = string
  default     = "alb-monitoring-remediation"
}

variable "lambda_timeout" {
  description = "Timeout for the Lambda function in seconds"
  type        = number
  default     = 300
}

variable "lambda_memory_size" {
  description = "Memory size for the Lambda function in MB"
  type        = number
  default     = 512
}

variable "enable_lambda_function" {
  description = "Whether to create the Lambda function and related resources"
  type        = bool
  default     = true
}

# ==========================================
# Scheduling Configuration
# ==========================================

variable "schedule_expression" {
  description = "EventBridge schedule expression for triggering the Lambda"
  type        = string
  default     = "cron(0 2 * * ? *)" # Daily at 2 AM UTC
}

# ==========================================
# Filtering Configuration
# ==========================================

variable "alb_name_filter" {
  description = "Regex pattern to filter ALB names (optional)"
  type        = string
  default     = ""
}

variable "excluded_alb_names" {
  description = "List of ALB names to exclude from monitoring"
  type        = list(string)
  default     = []
}

variable "monitor_internal_albs" {
  description = "Whether to monitor internal (non-internet-facing) ALBs"
  type        = bool
  default     = true
}

# ==========================================
# Logging Configuration
# ==========================================

variable "log_retention_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 14
  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.log_retention_days)
    error_message = "Log retention days must be one of the valid CloudWatch log retention periods."
  }
}

# ==========================================
# Tagging
# ==========================================

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
