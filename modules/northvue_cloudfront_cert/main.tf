resource "tls_private_key" "cloudfront_private_key" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_secretsmanager_secret" "cloudfront_access_key_secret" {
  name = "nvu-cloudfront-video-key"
  tags = var.default_tags
}

resource "aws_secretsmanager_secret_version" "cloudfront_access_key_secret_version" {
  secret_id     = aws_secretsmanager_secret.cloudfront_access_key_secret.id
  secret_string = tls_private_key.cloudfront_private_key.private_key_pem
}

resource "aws_cloudfront_public_key" "cloudfront_public_key" {
  name        = "access-key"
  encoded_key = tls_private_key.cloudfront_private_key.public_key_pem
  comment     = "Key used to secure Cloudfront CDN and sign access cookies"
}

resource "aws_cloudfront_key_group" "cloudfront_key_group" {
  name = "access-key-group"

  items = [
    aws_cloudfront_public_key.cloudfront_public_key.id
  ]
}