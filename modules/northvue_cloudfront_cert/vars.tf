variable "key_algorithm" {
  description = "The algorithm used to generate the private key"
  type        = string
  default     = "RSA"
}

variable "rsa_bits" {
  description = "The number of bits in the generated RSA private key"
  type        = number
  default     = 2048
}

variable "secret_name" {
  description = "The name of the secret in AWS Secrets Manager"
  type        = string
  default     = "cloudfront-video-key"
}

variable "default_tags" {
  description = "Default tags to be applied to resources"
  type        = map(string)
  default     = {}
}

variable "public_key_name" {
  description = "The name of the CloudFront public key"
  type        = string
  default     = "access-key"
}

variable "key_group_name" {
  description = "The name of the CloudFront key group"
  type        = string
  default     = "access-key-group"
}
