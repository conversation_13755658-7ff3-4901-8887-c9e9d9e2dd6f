output "secretsmanager_secret_id" {
  description = "The ID of the Secrets Manager secret"
  value       = aws_secretsmanager_secret.cloudfront_access_key_secret.id
}

output "private_key_pem" {
  description = "The private key PEM"
  value       = tls_private_key.cloudfront_private_key.private_key_pem
  sensitive   = true
}

output "public_key_pem" {
  description = "The public key PEM"
  value       = tls_private_key.cloudfront_private_key.public_key_pem
}

output "cloudfront_public_key_id" {
  description = "The ID of the CloudFront public key"
  value       = aws_cloudfront_public_key.cloudfront_public_key.id
}

output "cloudfront_key_group_id" {
  description = "The ID of the CloudFront key group"
  value       = aws_cloudfront_key_group.cloudfront_key_group.id
}
