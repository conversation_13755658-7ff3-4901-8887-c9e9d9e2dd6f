
variable "client_acronym" {
  type    = string
  default = "cvland-dev"
}

variable "client_name_kinesis" {
  type    = string
  default = "cvland-dev-aus"
}

variable "kinesis_stream_name_roi" {
  type    = string
  default = ""
}

variable "kinesis_stream_name_hvi" {
  type    = string
  default = ""
}

variable "kinesis_stream_name_aggregate" {
  type    = string
  default = ""
}

variable "kinesis_stream_name_departure" {
  type    = string
  default = ""
}

variable "kinesis_stream_name_arrival" {
  type    = string
  default = ""
}

variable "kinesis_stream_name_danger_zone" {
  type    = string
  default = ""
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Data"
  }
}