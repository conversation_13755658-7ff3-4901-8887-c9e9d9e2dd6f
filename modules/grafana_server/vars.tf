variable "default_tags" {
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Monitored   = "true"
    Environment = "Production"
    Squad       = "Platform"
  }
}

variable "tags" {
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "grafana_iam_user_name" {
  description = "The IAM users's name"
  type        = string
  default     = "grafana"
}

variable "grafana_iam_user_name_list" {
  type    = list(string)
  default = ["grafana"]
}

variable "vpc_id" {
  description = "The VPC's ID where the EC2 will be deployed"
  type        = string
}

variable "grafana_db_user_name" {
  description = "The master DB username"
  type        = string
  default     = "admin"
}

variable "grafana_db_schema_name" {
  type    = string
  default = "grafana"
}

variable "grafana_db_engine" {
  type    = string
  default = "aurora-mysql"
}

variable "grafana_db_engine_version" {
  type = string
}

variable "grafana_db_scaling_config" {
  type = map(any)
  default = {
    min_capacity = 1
    max_capacity = 4
  }
}

variable "grafana_db_subnet_group_name" {
  type = string
}

variable "create_db_subnet_group" {
  type    = bool
  default = true
}


variable "grafana_db_subnet_ids" {
  type = list(any)
}

variable "grafana_db_allowed_sec_groups" {
  type = list(any)
}

variable "grafana_db_vpc_sec_group_ids" {
  type = list(any)
}
variable "grafana_web_image_name" {
  type    = string
  default = "grafana/grafana"
}
variable "grafana_web_image_tag" {
  type    = string
  default = "8.4.4"
}

variable "grafana_web_container_port" {
  type    = number
  default = 3000
}

variable "grafana_web_env_vars" {
  type    = list(map(string))
  default = [{ "name" = "EMPTY_LIST " }]
}

variable "grafana_web_vpc_id" {
  type = string
}

variable "grafana_web_subnet_ids" {
  type = list(any)
}

variable "grafana_alb_subnet_ids" {
  type = list(any)
}

variable "grafana_web_create_alb" {
  type    = bool
  default = true
}

variable "grafana_web_external_sec_group" {
  type    = string
  default = ""
}

variable "grafana_web_external_alb_arn" {
  type    = string
  default = ""
}

variable "grafana_web_target_group_health_check_path" {
  type    = string
  default = "/"
}

variable "grafana_web_security_group_ids" {
  type    = list(any)
  default = []
}

variable "grafana_web_ecs_service_desired_count" {
  type    = number
  default = 3
}

variable "create_attachment_policy" {
  type    = bool
  default = true
}

variable "grafana_web_ecs_task_cpu" {
  type    = number
  default = 256
}

variable "grafana_web_ecs_task_memory" {
  type    = number
  default = 512
}

variable "grafana_web_cpu_target_value" {
  description = "Target value for ECS CPU utilization"
  type        = number
  default     = 50.0
}

variable "grafana_web_memory_target_value" {
  description = "Target value for ECS memory utilization"
  type        = number
  default     = 60.0
}
