locals {
  account_id = data.aws_caller_identity.current.account_id
}
resource "aws_iam_policy" "kinesis_stream_policy" {
  count       = length(var.client_aws_account_details)
  name        = "redshift_stream_policy_${var.client_aws_account_details[count.index].account_id}"
  path        = "/"
  description = "Kinesis stream access for redshift"
  tags        = var.tags
  policy = jsonencode(
    {
      Version = "2012-10-17",
      Statement = [
        {
          Sid    = "ReadStream",
          Effect = "Allow",
          Action = [
            "kinesis:DescribeStreamSummary",
            "kinesis:GetShardIterator",
            "kinesis:GetRecords",
            "kinesis:DescribeStream"
          ],
          Resource = [for entry in var.client_granted_stream_names : "arn:aws:kinesis:*:${local.account_id}:stream/${entry.stream_name}"]

        }
        ,
        {
          Sid    = "ListStream",
          Effect = "Allow",
          Action = [
            "kinesis:ListStreams",
            "kinesis:ListShards"
          ],
          Resource = "*"
        }
      ]
  })
}
resource "aws_iam_role" "kinesis_stream_role" {
  count = length(var.client_aws_account_details)
  name  = "kinesis_stream_access_${var.client_aws_account_details[count.index].account_id}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect    = "Allow",
        Action    = "sts:AssumeRole",
        Principal = { "AWS" = ["arn:aws:iam::${var.client_aws_account_details[count.index].account_id}:role/${var.client_aws_account_details[count.index].role_name}"] }

    }]
  })
  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "kinesis_stream_policy_attachment" {
  count      = length(var.client_aws_account_details)
  role       = aws_iam_role.kinesis_stream_role[count.index].name
  policy_arn = aws_iam_policy.kinesis_stream_policy[count.index].arn
}
