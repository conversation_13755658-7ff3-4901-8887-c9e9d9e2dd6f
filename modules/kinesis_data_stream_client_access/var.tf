data "aws_caller_identity" "current" {}
variable "aws_region" {
  type    = string
  default = ""
}
variable "client_name" {
  type    = string
  default = ""
}
variable "client_aws_account_details" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = []
}
variable "client_granted_stream_names" {
  type = list(map(string))
  default = [
    { stream_name = "ds-cfa-eyecue-roi" },
    { stream_name = "ds-cfa-eyecue-hvi" },
    { stream_name = "ds-cfa-eyecue-departure" },
    { stream_name = "ds-cfa-eyecue-eventstream" },
    { stream_name = "ds-cfa-eyecue-aggregate" }
  ]
}
variable "client_account_id" {
  type    = string
  default = ""
}

variable "databricks_role_arn" {
  type    = string
  default = null

}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Data Team"
  }
}