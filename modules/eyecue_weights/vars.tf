variable "aws_iam_user" {
  description = "Name of the IAM User who it depends on"
}
variable "aws_account_id" {}

variable "aws_region" {
  description = "AWS Region"
}

variable "country" {}

variable "client_name" {}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
variable "bucket_name" {
  default = "eyecue-weights"
}