
locals {
  kms_key_id = var.is_data_sharing_enabled ? aws_kms_key.kinesis_key[0].arn : "alias/aws/kinesis"
}


locals {
  policy_1 = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "ReadStream",
        Effect = "Allow",
        Action = [
          "kinesis:DescribeStreamSummary",
          "kinesis:GetShardIterator",
          "kinesis:GetRecords",
          "kinesis:DescribeStream"
        ],
        Resource = "arn:aws:kinesis:*:${var.current_account_id}:stream/*"
      },
      {
        Sid    = "ListStream",
        Effect = "Allow",
        Action = [
          "kinesis:ListStreams",
          "kinesis:ListShards"
        ],
        Resource = "*"
      },
      {
        Effect   = "Allow",
        Action   = "kms:Decrypt",
        Resource = var.is_data_sharing_enabled ? aws_kms_key.kinesis_key[0].arn : " "
      }
    ]
  })

}

locals {
  policy_2 = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "ReadStream",
        Effect = "Allow",
        Action = [
          "kinesis:DescribeStreamSummary",
          "kinesis:GetShardIterator",
          "kinesis:GetRecords",
          "kinesis:DescribeStream"
        ],
        Resource = "arn:aws:kinesis:*:${var.current_account_id}:stream/*"
      },
      {
        Sid    = "ListStream",
        Effect = "Allow",
        Action = [
          "kinesis:ListStreams",
          "kinesis:ListShards"
        ],
        Resource = "*"
      }
    ]
  })
}


resource "aws_kms_key" "kinesis_key" {
  count       = var.is_data_sharing_enabled ? 1 : 0
  description = "KMS key for Kinesis Data Streams"
  policy      = <<EOF
          {
              "Id": "key-consolepolicy-3",
              "Version": "2012-10-17",
              "Statement": [
                  {
                      "Sid": "Enable IAM User Permissions",
                      "Effect": "Allow",
                      "Principal": {
                          "AWS": "arn:aws:iam::${var.current_account_id}:root"
                      },
                      "Action": "kms:*",
                      "Resource": "*"
                  },
                  {
                      "Sid": "Allow access for Key Administrators",
                      "Effect": "Allow",
                      "Principal": {
                          "AWS": "arn:aws:iam::${var.current_account_id}:role/AdminAccess"
                      },
                      "Action": [
                          "kms:Create*",
                          "kms:Describe*",
                          "kms:Enable*",
                          "kms:List*",
                          "kms:Put*",
                          "kms:Update*",
                          "kms:Revoke*",
                          "kms:Disable*",
                          "kms:Get*",
                          "kms:Delete*",
                          "kms:TagResource",
                          "kms:UntagResource",
                          "kms:ScheduleKeyDeletion",
                          "kms:CancelKeyDeletion",
                          "kms:RotateKeyOnDemand"
                      ],
                      "Resource": "*"
                  },
                  {
                      "Sid": "Allow use of the key",
                      "Effect": "Allow",
                      "Principal": {
                          "AWS": ["${var.cross_account_lambda_role_arn}" ]
                      },
                      "Action": [
                          "kms:Decrypt",
                          "kms:DescribeKey"
                      ],
                      "Resource": "*"
                  }
              ]
          }
          EOF
  tags        = merge(var.default_tags, var.addtional_tags)
}


resource "aws_kms_alias" "kinesis_key_alias" {
  count         = var.is_data_sharing_enabled ? 1 : 0
  name          = "alias/kinesis_key"
  target_key_id = aws_kms_key.kinesis_key[count.index].arn
}


resource "aws_kinesis_stream" "eyecue_kinesis_data_streams" {
  count = length(var.stream_name_list)
  name  = "ds-${var.client_name}-eyecue-${var.stream_name_list[count.index]}"

  retention_period = var.retention_period
  encryption_type  = "KMS"
  kms_key_id       = local.kms_key_id
  shard_level_metrics = [
    "IncomingBytes",
    "OutgoingBytes",
  ]
  stream_mode_details {
    stream_mode = var.stream_mode
  }
  tags = merge(var.default_tags, var.addtional_tags)
}


resource "aws_kinesis_resource_policy" "kinesis_stream_data_sharing_policy" {
  count        = var.is_data_sharing_enabled ? length(var.stream_name_list) : 0
  resource_arn = aws_kinesis_stream.eyecue_kinesis_data_streams[count.index].arn

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Id": "writePolicy",
  "Statement": [{
    "Sid": "StreamRead",
    "Effect": "Allow",
    "Principal": {
      "AWS": "${var.cross_account_lambda_role_arn}"
    },
    "Action": [
      "kinesis:DescribeStreamSummary",
      "kinesis:GetRecords",
      "kinesis:GetShardIterator",
      "kinesis:DescribeStream",
      "kinesis:ListShards"
    ],
    "Resource": ["${aws_kinesis_stream.eyecue_kinesis_data_streams[count.index].arn}"]
  }]
}
EOF
}

resource "random_id" "suffix" {
  byte_length = 4
}


resource "aws_iam_policy" "kinesis_stream_policy" {
  # Add a random suffix to avoid policy name conflicts with existing clients, and make it conditional to prevent changes to existing clients
  name        = "${var.redshift_stream_access_policy_name}${var.policy_suffix != "" ? "-${var.policy_suffix}" : ""}"
  count       = var.create_role ? 1 : 0
  path        = "/"
  description = "Kinesis stream access for redshift"
  policy      = var.is_data_sharing_enabled ? local.policy_1 : local.policy_2
  tags        = merge(var.default_tags, var.addtional_tags)
}

resource "aws_iam_role" "kinesis_stream_role" {
  #make the role name conditional to prevent changes to existing clients. planing to remove this condition in future and correct the role name for all clients
  name  = var.redshift_stream_access_role_name
  count = var.create_role ? 1 : 0
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect    = "Allow",
        Action    = "sts:AssumeRole",
        Principal = { "AWS" : [for entry in var.redshift_aws_account_ids_roles : "arn:aws:iam::${entry.account_id}:role/${entry.role_name}"] }

    }]
  })
  tags = merge(var.default_tags, var.addtional_tags)
}

resource "aws_iam_role_policy_attachment" "kinesis_stream_role_policy_attachment" {
  count      = var.create_role ? 1 : 0
  role       = aws_iam_role.kinesis_stream_role[count.index].name
  policy_arn = aws_iam_policy.kinesis_stream_policy[count.index].arn
}
