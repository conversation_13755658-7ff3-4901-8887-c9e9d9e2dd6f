data "aws_caller_identity" "current" {}
variable "aws_region" {
  type    = string
  default = ""
}
variable "client_name" {
  type    = string
  default = ""
}
variable "country" {
  type    = string
  default = ""
}
variable "redshift_aws_account_ids_roles" {
  type    = list(map(string))
  default = []
}
variable "retention_period" {
  type    = number
  default = 24
}
variable "stream_mode" {
  type    = string
  default = "ON_DEMAND"
}
variable "current_account_id" {
  type    = string
  default = null
}

variable "data_prod_account_id" {
  type    = string
  default = "************"
}

variable "redshift_stream_role" {
  description = "value of the redshift stream role name. This is used to create the role in the redshift account and pass via the module"
  type        = string
  default     = "redshift_stream_role"
}

variable "firehose_roi_bucket_arn" {
  description = "value of the firehose roi bucket arn. This is used to create the role in the redshift account and pass via the module"
  type        = string
  default     = ""
}
variable "stream_name_list" {
  description = "List of stream types"
  type        = list(string)
  default     = ["roi", "hvi", "aggregate", "departure"]
}

variable "roi_stream_exists" {
  description = "Set to true if the ROI stream exists, otherwise set to false."
  default     = true
}

variable "create_role" {
  description = "Set to true to create the IAM role, false to skip creation"
  type        = bool
  default     = true
}

variable "cross_account_lambda_role_arn" {
  description = "value of the aws data account lambda role arn. This is used to create the role in the redshift account and pass via the module"
  type        = string
  default     = "arn:aws:iam::************:role/eops_mcd_nzd_dev_role"
}


variable "is_data_sharing_enabled" {
  description = "Set to true if data sharing is enabled with data aws lambda function, otherwise set to false."
  type        = bool
  default     = false

}

variable "default_tags" {
  type = map(string)
  default = {
    Name        = "kinesis_stream_data_sharing"
    Squad       = "Data Team"
    Environment = "Production"
    Terraform   = "true"
  }

}

variable "addtional_tags" {
  type    = map(string)
  default = {}

}

variable "redshift_stream_access_role_name" {
  description = "value of the redshift stream access role name. This is used to create the role in the redshift account and pass via the module"
  type        = string
  default     = "redshift_stream_aaccess_role"
}

variable "redshift_stream_access_policy_name" {
  description = "value of the redshift stream access role name. This is used to create the role in the redshift account and pass via the module"
  type        = string
  default     = "redshift_stream_policy"
}

variable "policy_suffix" {
  description = "value of the policy suffix. This is used to create the policy. use with kms key enabled streams"
  type        = string
  default     = ""

}