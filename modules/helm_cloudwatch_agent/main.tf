#####################################
# Helm release for CloudWatch Agent
#####################################
resource "helm_release" "cloudwatch_agent" {
  name       = var.release_name
  repository = "https://aws.github.io/eks-charts"
  chart      = "aws-cloudwatch-metrics"
  version    = var.chart_version
  namespace  = var.namespace

  create_namespace = var.create_namespace

  set {
    name  = "clusterName"
    value = var.eks_cluster_name
  }

  # Configure the service account to use the IRSA role
  set {
    name  = "serviceAccount.name"
    value = "cloudwatch-metrics-agent"
  }
  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.cw_agent_irsa.iam_role_arn
  }

  depends_on = [aws_cloudwatch_log_group.performance_log_group]
}

data "aws_iam_policy" "cloudwatch_agent_server_policy" {
  # Managed policy allowing remote write to AMP
  arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

module "cw_agent_irsa" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.0"

  role_name = "${var.eks_cluster_name}-cw-agent-irsa"

  oidc_providers = {
    main = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["amazon-cloudwatch:cloudwatch-metrics-agent"]
    }
  }

  role_policy_arns = {
    "cloudwatch_agent_server_policy" = data.aws_iam_policy.cloudwatch_agent_server_policy.arn
  }

  tags = var.tags
}

resource "aws_cloudwatch_log_group" "performance_log_group" {
  name              = "/aws/containerinsights/${var.eks_cluster_name}/performance"
  retention_in_days = 60
  tags              = var.tags
  lifecycle { # allow overwrite by cw_log_retention module
    ignore_changes = [retention_in_days]
  }
}
