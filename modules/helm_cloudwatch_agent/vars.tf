variable "release_name" {
  description = "The release name for the Helm chart."
  type        = string
  default     = "cloudwatch-agent"
}

variable "chart_version" {
  description = "The version of the chart to deploy."
  type        = string
  default     = "0.0.11"
}

variable "namespace" {
  description = "The Kubernetes namespace in which to deploy."
  type        = string
  default     = "amazon-cloudwatch"
}

variable "create_namespace" {
  description = "Whether to create the namespace if it does not exist."
  type        = bool
  default     = true
}

# ==================================================================

variable "eks_cluster_name" {
  description = "The name of the EKS cluster."
  type        = string
}

variable "oidc_provider_arn" {
  description = "The OIDC provider ARN for the EKS cluster."
  type        = string
}

variable "tags" {
  description = "A map of tags to apply to the resources."
  type        = map(string)
  default     = {}
}
