# README

This repository is a Terraform module for creating permission sets and links on AWS Identity Center.

## <PERSON><PERSON>

Follow the below code snippet for consuming this module:

```
module "aws-identity-center" {
  source         = "git::*****************:fingermarkltd/terraform-aws-identity-center.git/?ref=master"
  aws_account_id = data.aws_caller_identity.current.account_id
  permission_sets = [
    {
      name = "",
      description = "",
      policy_name = "",
      aws_managed_policy = true/false
    },
    {
      name = "",
      description = "",
      policy_name = "",
      aws_managed_policy = true/false
    }
  ]
  permission_set_associations = [
    {
      name = ""
      permission_sets = [
        {
          name = ""
          account = [
            "",
            ""
          ]
        }
      ]
    }
  ]
}
```

## VARIABLES

```
variable "permission_sets" {
  description = "List of Permission Sets and their descriptions"
  VARIABLE STRUCTURE EXAMPLE
  default     = [
    {
      name = "",
      description = "",
      policy_name = "",
      aws_managed_policy = true/false
    },
    {
      name = "",
      description = "",
      policy_name = "",
      aws_managed_policy = true/false
    }
  ]
}
variable "permission_set_associations" {
  description = "Permission Set associations"
  VARIABLE STRUCTURE EXAMPLE
  default = [
    {
      name = ""
      permission_sets = [
        {
          name = ""
          account = [
            "",
            ""
          ]
        }
      ]
    }
  ]
}
```
