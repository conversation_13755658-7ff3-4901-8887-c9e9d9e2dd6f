data "aws_ssoadmin_instances" "ssoadmin_instances" {}

data "aws_identitystore_group" "admin_sso_group" {
  for_each          = { for group in var.permission_set_associations : group.name => group }
  identity_store_id = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.identity_store_ids)[0]
  alternate_identifier {
    unique_attribute {
      attribute_path  = "DisplayName"
      attribute_value = each.value.name
    }
  }
}

data "aws_ssoadmin_permission_set" "permission_set_with_customer_managed_policy" {
  for_each     = { for k, permission_set in var.permission_sets : k => permission_set if permission_set.aws_managed_policy == false }
  name         = each.value.name
  instance_arn = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.arns)[0]
  depends_on   = [aws_ssoadmin_permission_set.permission_set]
}

data "aws_ssoadmin_permission_set" "permission_set_with_aws_managed_policy" {
  for_each     = { for k, permission_set in var.permission_sets : k => permission_set if permission_set.aws_managed_policy == true }
  name         = each.value.name
  instance_arn = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.arns)[0]
  depends_on   = [aws_ssoadmin_permission_set.permission_set]
}
