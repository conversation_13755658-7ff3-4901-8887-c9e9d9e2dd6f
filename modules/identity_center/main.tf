locals {
  permission_set_links = flatten([
    for group in var.permission_set_associations : [
      for permission_set in group.permission_sets : [
        for account_id in permission_set.account : {
          group_name          = group.name
          permission_set_name = permission_set.name
          account_id          = account_id
        }
      ]
    ]
  ])
  permission_set_names = [for ps in var.permission_sets : ps.name]
}
resource "aws_ssoadmin_permission_set" "permission_set" {
  for_each         = { for ps in var.permission_sets : ps.name => ps }
  name             = each.value.name
  description      = each.value.description
  session_duration = each.value.session_duration
  instance_arn     = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.arns)[0]
}

resource "aws_ssoadmin_customer_managed_policy_attachment" "permission_set_policy_attachment" {
  for_each           = { for permission_set in var.permission_sets : permission_set.name => permission_set if permission_set.aws_managed_policy == false }
  instance_arn       = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.permission_set[each.key].arn
  customer_managed_policy_reference {
    name = each.value.policy_name
    path = "/"
  }
}

resource "aws_ssoadmin_managed_policy_attachment" "permission_set_policy_attachment_1" {
  for_each           = { for permission_set in var.permission_sets : permission_set.name => permission_set if permission_set.aws_managed_policy == true }
  instance_arn       = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.arns)[0]
  managed_policy_arn = "arn:aws:iam::aws:policy/${each.value.policy_name}"
  permission_set_arn = aws_ssoadmin_permission_set.permission_set[each.key].arn
}

resource "aws_ssoadmin_account_assignment" "account_assignment" {
  for_each           = { for permission_set_link in local.permission_set_links : "${permission_set_link.group_name}/${permission_set_link.permission_set_name}/${permission_set_link.account_id}" => permission_set_link }
  instance_arn       = tolist(data.aws_ssoadmin_instances.ssoadmin_instances.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.permission_set[each.value.permission_set_name].arn
  principal_id       = data.aws_identitystore_group.admin_sso_group[each.value.group_name].group_id
  principal_type     = "GROUP"
  target_id          = each.value.account_id
  target_type        = "AWS_ACCOUNT"
}
