variable "permission_sets" {
  description = "List of permission sets to be created in AWS SSO"
  type = list(object({
    name               = string
    description        = string
    policy_name        = string
    aws_managed_policy = optional(bool, false)
    session_duration   = optional(string, "PT4H")
  }))
}

variable "permission_set_associations" {
  description = "List of groups and their associated permission sets with target accounts"
  type = list(object({
    name = string
    permission_sets = list(object({
      name    = string
      account = list(string)
    }))
  }))
}
