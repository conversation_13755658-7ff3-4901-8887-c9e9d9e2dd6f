# =====
# Tags: Consolidated
# =====
locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# CloudWatch Metric Alarm for Global Lambda Error Rate
# =====
resource "aws_cloudwatch_metric_alarm" "global_lambda_error_rate" {
  alarm_name          = var.alarm_name
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.evaluation_periods
  threshold           = var.error_threshold_percent / 100 # Convert percentage to decimal
  alarm_description   = coalesce(var.alarm_description, "Global Lambda Error Rate exceeding ${var.error_threshold_percent}% threshold")
  treat_missing_data  = var.treat_missing_data
  alarm_actions       = var.enable_notification ? var.sns_topic_arns : null
  ok_actions          = var.enable_notification ? var.sns_topic_arns : null

  metric_query {
    id          = "e1"
    expression  = "FILL(m1,0)"
    label       = "Errors"
    return_data = false
  }

  metric_query {
    id          = "i1"
    expression  = "FILL(m2,0)"
    label       = "Invocations"
    return_data = false
  }

  metric_query {
    id          = "m1"
    return_data = false
    metric {
      metric_name = "Errors"
      namespace   = "AWS/Lambda"
      period      = var.period_seconds
      stat        = var.statistic
    }
  }

  metric_query {
    id          = "m2"
    return_data = false
    metric {
      metric_name = "Invocations"
      namespace   = "AWS/Lambda"
      period      = var.period_seconds
      stat        = var.statistic
    }
  }

  metric_query {
    id          = "error_rate"
    expression  = "IF(i1 > 0, (e1 / i1) * 100, 0)" # Calculate error percentage
    label       = "Error Rate (%)"
    return_data = true
  }

  tags = merge(local.tags, {
    Name = var.alarm_name
  })
}
