# Module: Lambda Error Monitoring

This module deploys a CloudWatch alarm that monitors global Lambda error rates across all Lambda functions in the account. It uses metric math to calculate the ratio of total errors to total invocations and triggers an alarm when this ratio exceeds a configurable threshold.

This approach is particularly useful for SOC2 compliance requirements where you need to monitor error rates without setting up individual alarms for each Lambda function.

## Metrics and Calculation

The module calculates the error rate using the following CloudWatch metric math:

```
(Total Lambda Errors / Total Lambda Invocations) * 100
```

If there are no invocations during the period, the error rate is considered 0%.

## Example usage

```hcl
module "lambda_error_monitoring" {
  source = "./modules/lambda_error_monitoring"
  
  # Custom alarm configuration
  alarm_name             = "SOC2-GlobalLambdaErrorRate"
  error_threshold_percent = 1  # Triggers when error rate is greater than 1%
  evaluation_periods     = 3   # Require breach for 3 consecutive periods
  period_seconds         = 300 # 5 minutes

  # SNS notification
  sns_topic_arns        = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification   = true
  
  # Additional tags
  tags = {
    Compliance = "SOC2"
    Purpose    = "ErrorMonitoring"
  }
}
```

## Notes

- This approach monitors the global error rate across all Lambda functions, which serves as a "quick win" for SOC2 compliance.
- For more advanced monitoring, consider implementing per-function alarms or filtering for specific Lambda functions based on tagging.
- The alarm uses the `notBreaching` behavior for missing data by default.
