# =====
# SNS Topic: CloudWatch Alarms for Lambda
# =====
variable "sns_topic_arns" {
  description = <<-DOC
    [Optional] List of existing SNS topic ARNs to use for sending CloudWatch Alarm notifications
    for Lambda error monitoring.

    It is recommended to create a centralised SNS topic for multiple CloudWatch Alarm notifications
    using the module `cw_alarm_notifications_sns_topic`:
    https://bitbucket.org/fingermarkltd/fingermark-terraform/src/master/modules/cw_alarm_notifications_sns_topic/
  DOC
  type        = list(string)
  default     = []
}

# =====
# CloudWatch Alarms: Lambda Error Rate
# =====
variable "alarm_name" {
  description = "Name for the CloudWatch alarm that monitors global Lambda error rate"
  type        = string
  default     = "GlobalLambdaErrorRate"
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm. If not provided, a default description will be used."
  type        = string
  default     = null
}

variable "evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold"
  type        = number
  default     = 1
}

variable "period_seconds" {
  description = "The period in seconds over which the specified statistic is applied"
  type        = number
  default     = 300 # 5 minutes
}

variable "statistic" {
  description = "The statistic to apply to the alarm's associated metric"
  type        = string
  default     = "Sum"
}

variable "error_threshold_percent" {
  description = "The percentage threshold for Lambda errors (e.g., 1 for 1% error rate)"
  type        = number
  default     = 1
}

variable "treat_missing_data" {
  description = "How to treat missing data when evaluating the alarm"
  type        = string
  default     = "notBreaching"
  validation {
    condition     = contains(["missing", "ignore", "breaching", "notBreaching"], var.treat_missing_data)
    error_message = "The treat_missing_data value must be one of: missing, ignore, breaching, notBreaching."
  }
}

variable "enable_notification" {
  description = "Whether to enable alarm notification using a SNS topic"
  type        = bool
  default     = true
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "soc2"
    Environment = "prod"
  }
}
