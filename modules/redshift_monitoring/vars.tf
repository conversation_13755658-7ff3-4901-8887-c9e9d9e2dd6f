variable "default_tags" {
  type = map(string)
  default = {
    Terraform   = "True"
    Stack       = "Monitoring"
    Product     = "Eyecue"
    Squad       = "Infra"
    Environment = "Prod"
  }
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "True"
    Stack       = "Monitoring"
    Product     = "Eyecue"
    Squad       = "Infra"
    Environment = "Prod"
  }
}

variable "redshift_monitoring_ami_id" {
  type    = string
  default = ""
}

variable "redshift_monitoring_name" {
  type    = string
  default = "redshift-monitoring"
}

variable "redshift_monitoring_user_data_template" {
  type        = string
  default     = "user_data/user_data.sh"
  description = "User Data template to use for provisioning EC2"
}

variable "redshift_monitoring_key_name" {
  type    = string
  default = "matias"
}

variable "redshift_monitoring_authorized_keys_path" {
  type        = string
  description = "A path to read authorized_keys file and append the one in the server"
  default     = null
}

variable "redshift_monitoring_systemd_service_path" {
  type        = string
  description = "A path to read the systemd service and copying it to the EC2"
  default     = null
}

variable "redshift_monitoring_systemd_dev_envfile_path" {
  type        = string
  description = "A path to read the systemd service DEV env vars file and copying it to the EC2"
  default     = null
}

variable "redshift_monitoring_systemd_prod_envfile_path" {
  type        = string
  description = "A path to read the systemd service PROD env vars file and copying it to the EC2"
  default     = null
}

variable "redshift_monitoring_allowed_security_groups" {
  type = list(string)
}

variable "redshift_monitoring_vpc_id" {
  type = string
}

variable "redshift_monitoring_subnet_id" {
  type = string
}

variable "redshift_monitoring_availability_zone" {
  type = string
}

variable "redshift_monitoring_instance_type" {
  type    = string
  default = "t3.micro"
}

variable "redshift_monitoring_security_group_ids" {
  type = list(string)
}

variable "redshift_monitoring_user_data_replace_on_change" {
  type    = bool
  default = true
}
