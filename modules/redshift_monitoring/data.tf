data "aws_region" "current" {}

data "aws_ami" "default" {
  most_recent = "true"

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]
  }
  # aws ec2 describe-images --image-ids ami-0194c3e07668a7e36 --region eu-west-2 | jq ".Images[0].OwnerId"  
  owners = ["099720109477"]
}

data "template_file" "redshift_monitoring_user_data" {
  template = file(var.redshift_monitoring_user_data_template)
  vars = {
    authorized_keys_content      = file(var.redshift_monitoring_authorized_keys_path)
    systemd_service_content      = file(var.redshift_monitoring_systemd_service_path)
    systemd_dev_envfile_content  = file(var.redshift_monitoring_systemd_dev_envfile_path)
    systemd_prod_envfile_content = file(var.redshift_monitoring_systemd_prod_envfile_path)
  }
}

