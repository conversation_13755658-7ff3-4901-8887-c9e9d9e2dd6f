module "redshift_monitoring" {
  source                      = "terraform-aws-modules/ec2-instance/aws"
  version                     = "4.0.0"
  name                        = var.redshift_monitoring_name
  availability_zone           = var.redshift_monitoring_availability_zone
  user_data                   = data.template_file.redshift_monitoring_user_data.rendered
  ami                         = coalesce(var.redshift_monitoring_ami_id, data.aws_ami.default.id)
  instance_type               = var.redshift_monitoring_instance_type
  key_name                    = var.redshift_monitoring_key_name
  monitoring                  = true
  vpc_security_group_ids      = concat(var.redshift_monitoring_security_group_ids, ["${aws_security_group.redshift_monitoring.id}"])
  subnet_id                   = var.redshift_monitoring_subnet_id
  user_data_replace_on_change = var.redshift_monitoring_user_data_replace_on_change

  tags = var.tags
}


resource "aws_security_group" "redshift_monitoring" {
  name   = "${title(var.redshift_monitoring_name)}-SecurityGroup"
  vpc_id = var.redshift_monitoring_vpc_id

  ingress = [
    {
      description      = "Allow incoming connections to Redshift from trusted locations"
      from_port        = 5439
      to_port          = 5439
      protocol         = "TCP"
      self             = true
      security_groups  = var.redshift_monitoring_allowed_security_groups
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      cidr_blocks      = null
    }
  ]

  egress = [
    {
      description      = "Allow all outgoing connections "
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = ["0.0.0.0/0"]
    }
  ]

  tags = merge(var.tags, { Name = "${title(var.redshift_monitoring_name)}-SecurityGroup" })
}

resource "aws_eip" "redshift_monitoring" {
  instance = module.redshift_monitoring.id
  tags     = merge(var.tags, { "Name" = var.redshift_monitoring_name })
}
