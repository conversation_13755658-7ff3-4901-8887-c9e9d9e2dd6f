variable "customer" {
  description = "Customer name for resource naming"
  type        = string
}

variable "region" {
  description = "AWS Region"
  type        = string
}

variable "account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "images_bucket_arn" {
  description = "ARN of the images bucket"
  type        = string
}

variable "camera_images_bucket_arn" {
  description = "ARN of the camera images bucket"
  type        = string
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
}

variable "slack_webhook_url" {
  description = "Slack webhook URL for SNS notifications"
  type        = string
}

variable "image_sync_user" {
  description = "Name of the image sync user"
  type        = string
}
