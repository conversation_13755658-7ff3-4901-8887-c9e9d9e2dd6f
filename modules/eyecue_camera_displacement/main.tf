resource "aws_sqs_queue" "camera_displacement_sqs" {
  name                       = "eyecue-camera-displacement-sqs-${var.customer}"
  visibility_timeout_seconds = 120

  kms_master_key_id = aws_kms_key.key.arn

  tags = var.tags
}

resource "aws_iam_role" "lambda_role" {
  name = "eyecue-camera-displacement-lambda-role-${var.customer}"

  tags = var.tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "lambda_policy" {
  name        = "eyecue-camera-displacement-lambda-policy-${var.customer}"
  description = "IAM policy for Lambda function"

  tags = var.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogStream",
          "logs:CreateLogGroup",
          "logs:TagResource"
        ]
        Resource = "arn:aws:logs:${var.region}:${var.account_id}:log-group:/aws/lambda/eyecue-camera-displacement-lambda-${var.customer}:*"
        Effect   = "Allow"
      },
      {
        Action = [
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${var.region}:${var.account_id}:log-group:/aws/lambda/eyecue-camera-displacement-lambda-${var.customer}:*:*"
        Effect   = "Allow"
      },
      {
        Action = [
          "sqs:GetQueueUrl",
          "sqs:ListQueues",
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:GetQueueAttributes",
          "sqs:SendMessage"
        ]
        Resource = "arn:aws:sqs:${var.region}:${var.account_id}:eyecue-camera-displacement-sqs-${var.customer}"
        Effect   = "Allow"
      },
      {
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ],
        Resource = aws_kms_key.key.arn
        Effect   = "Allow"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObjectAcl",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObjectAcl",
          "s3:GetObject"
        ]
        Resource = [
          var.camera_images_bucket_arn,
          "${var.camera_images_bucket_arn}/*",
          var.images_bucket_arn,
          "${var.images_bucket_arn}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_role_policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

resource "aws_sns_topic" "oldest_message_sns" {
  name = "${var.customer}-OldestMessageAge-camera-displacement"

  kms_master_key_id = aws_kms_key.key.arn

  tags = var.tags
}

resource "aws_sns_topic_subscription" "slack_subscription" {
  topic_arn = aws_sns_topic.oldest_message_sns.arn
  protocol  = "https"
  endpoint  = var.slack_webhook_url
}

resource "aws_cloudwatch_metric_alarm" "sqs_oldest_message_alarm" {
  alarm_name          = "eyecue-camera-displacement-sqs-age-oldest-message-${var.customer}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "ApproximateAgeOfOldestMessage"
  namespace           = "AWS/SQS"
  period              = 300
  statistic           = "Maximum"
  threshold           = 300
  alarm_description   = "Alarm when messages in the SQS queue are not processed in time"
  alarm_actions       = [aws_sns_topic.oldest_message_sns.arn]

  tags = var.tags

  dimensions = {
    QueueName = aws_sqs_queue.camera_displacement_sqs.name
  }
}

resource "aws_kms_key" "key" {
  description             = "Used to encrypt camera displacement resources"
  deletion_window_in_days = 30
  tags                    = var.tags
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.account_id}:user/${var.image_sync_user}"
        }
        Action   = "kms:GenerateDataKey"
        Resource = "*"
      },
      {
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Effect = "Allow"
        Principal = {
          AWS = "${aws_iam_role.lambda_role.arn}"
        }
        Action   = "kms:Decrypt"
        Resource = "*"
      }
    ]
  })
}

module "camera_displacement_ecr" {
  source   = "../ecr"
  ecr_name = "serverless-eyecue-camera-displacement-prod"
  tags     = var.tags

  ecr_image_tag_mutability = "MUTABLE"
}

resource "aws_ecr_repository_policy" "eyecue_ecr_policy" {
  repository = "serverless-eyecue-camera-displacement-prod"

  policy = jsonencode({
    Version = "2008-10-17"
    Statement = [
      {
        Sid    = "LambdaECRImageRetrievalPolicy"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "ecr:BatchGetImage",
          "ecr:DeleteRepositoryPolicy",
          "ecr:GetDownloadUrlForLayer",
          "ecr:GetRepositoryPolicy",
          "ecr:SetRepositoryPolicy"
        ]
        Condition = {
          StringLike = {
            "aws:sourceArn" = "arn:aws:lambda:${var.region}:${var.account_id}:function:*"
          }
        }
      }
    ]
  })
}
