##
## Regular prometheus configuration
##
global:
  evaluation_interval: 5s
  external_labels:
    source: promxy

##
### Promxy configuration
##
promxy:
  server_groups:
    - static_configs:
        - targets:
            - "victoria-metrics.ap-southeast-2.fingermark.tech"
      scheme: https
      remote_read: false
      path_prefix: /select/0/prometheus

      # Example query parameter to disable caching
      query_params:
        nocache: 1

      http_client:
        dial_timeout: 1s
        tls_config:
          insecure_skip_verify: true
        basic_auth:
          username: querier
          password: "${querier_password}"

    - static_configs:
        - targets:
            - "victoria-metrics.us-east-1.fingermark.tech"
      scheme: https
      remote_read: false
      path_prefix: /select/0/prometheus

      # Example query parameter to disable caching
      query_params:
        nocache: 1

      http_client:
        dial_timeout: 1s
        tls_config:
          insecure_skip_verify: true
        basic_auth:
          username: querier
          password: "${querier_password}"
