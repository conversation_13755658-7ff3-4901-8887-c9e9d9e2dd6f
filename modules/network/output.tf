output "vpc_cidr" {
  description = "VPC CIDR Block"
  value       = aws_vpc.vpc.cidr_block
}

output "vpc_id" {
  description = "VPC id"
  value       = aws_vpc.vpc.id
}

output "vpc_name" {
  value = var.vpc_name
}

output "public_subnet_ids" {
  description = "List of public subnet ids"
  value       = tolist(aws_subnet.public[*].id)
}

output "public_subnet_arns" {
  description = "List of ARNs of public subnets"
  value       = tolist(aws_subnet.public[*].arn)
}

output "public_subnets_cidr_blocks" {
  description = "List of cidr_blocks of public subnets"
  value       = tolist(aws_subnet.public[*].cidr_block)
}

output "public_subnets_ipv6_cidr_blocks" {
  description = "List of IPv6 cidr_blocks of public subnets in an IPv6 enabled VPC"
  value       = tolist(aws_subnet.public[*].ipv6_cidr_block)
}

output "public_route_table_ids" {
  description = "List of IDs of public route tables"
  value       = tolist(aws_route_table.public[*].id)
}

output "private_subnet_ids" {
  description = "List of private subnet ids"
  value       = tolist(aws_subnet.private[*].id)
}

output "private_subnet_arns" {
  description = "List of ARNs of private subnets"
  value       = tolist(aws_subnet.private[*].arn)
}

output "private_subnets_cidr_blocks" {
  description = "List of cidr_blocks of private subnets"
  value       = tolist(aws_subnet.private[*].cidr_block)
}

output "private_subnets_ipv6_cidr_blocks" {
  description = "List of IPv6 cidr_blocks of private subnets in an IPv6 enabled VPC"
  value       = tolist(aws_subnet.private[*].ipv6_cidr_block)
}

output "private_route_table_ids" {
  description = "List of IDs of private route tables"
  value       = tolist(aws_route_table.private[*].id)
}

output "natgw_id" {
  description = "NAT Gateway ID"
  value       = aws_nat_gateway.nat.id
}

output "nat_gateway_public_ip" {
  description = "NAT Gateway Public IP"
  value       = aws_nat_gateway.nat.public_ip
}

output "nat_gateway_private_ip" {
  description = "NAT Gateway Private IP"
  value       = aws_nat_gateway.nat.private_ip
}

output "igw_id" {
  description = "Internet Gateway ID"
  value       = aws_internet_gateway.gateway.id
}

output "azs" {
  description = "A list of availability zones specified as argument to this module"
  value       = var.azs
}

output "havelock_security_group_id" {
  description = "The ID of the security group created by default on VPC"
  value       = var.havelocknorthaccess_sg == "enabled" ? aws_security_group.havelock_north_access[0].id : ""
}
