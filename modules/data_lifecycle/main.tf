resource "aws_iam_role" "dlm_lifecycle_role" {
  name = "${var.data_lifecycle_name}Role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "dlm.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
  tags               = merge(var.default_tags, var.tags)
}

resource "aws_iam_role_policy" "dlm_lifecycle" {
  name = "${var.data_lifecycle_name}Policy"
  role = aws_iam_role.dlm_lifecycle_role.id

  policy = <<EOF
{
   "Version": "2012-10-17",
   "Statement": [
      {
         "Effect": "Allow",
         "Action": [
            "ec2:CreateSnapshot",
            "ec2:CreateSnapshots",
            "ec2:DeleteSnapshot",
            "ec2:DescribeInstances",
            "ec2:DescribeVolumes",
            "ec2:DescribeSnapshots"
         ],
         "Resource": "*"
      },
      {
         "Effect": "Allow",
         "Action": [
            "ec2:CreateTags"
         ],
         "Resource": "arn:aws:ec2:${data.aws_region.current.name}::snapshot/*"
      }
   ]
}
EOF
}

resource "aws_dlm_lifecycle_policy" "backup_snapshot" {
  description        = "DLM lifecycle policy"
  execution_role_arn = aws_iam_role.dlm_lifecycle_role.arn
  state              = var.data_lifecycle_state

  policy_details {
    resource_types = var.data_lifecycle_resource_types

    schedule {
      name = var.data_lifecycle_name

      create_rule {
        interval      = var.data_lifecycle_interval
        interval_unit = upper(var.data_lifecycle_interval_unit)
        times         = var.data_lifecycle_times
      }

      retain_rule {
        count = var.data_lifecycle_retain_rule
      }

      tags_to_add = var.data_lifecycle_tags_to_add

      copy_tags = var.data_lifecycle_copy_tags
    }

    target_tags = var.data_lifecycle_target_tags
  }
  tags = merge(var.default_tags, var.tags)
}
