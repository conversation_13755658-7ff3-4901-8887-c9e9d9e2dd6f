variable "default_tags" {
  type = map(string)
  default = {
    Terraform   = "True"
    Stack       = "Backup"
    Name        = "EBSSnapshotBackups"
    Environment = "Prod"
    Squad       = "Platform"
  }
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "data_lifecycle_state" {
  type    = string
  default = "ENABLED"
}

variable "data_lifecycle_name" {
  description = "Name of the Life Cycle rule"
  type        = string
  default     = "SnaphotBackUp"
}

variable "data_lifecycle_resource_types" {
  type    = list(any)
  default = ["VOLUME"]
}

variable "data_lifecycle_target_tags" {
  type = map(string)
  default = {
    Backup = "True"
    backup = "True"
  }
}

variable "data_lifecycle_interval" {
  type    = number
  default = 24
}

variable "data_lifecycle_interval_unit" {
  type    = string
  default = "HOURS"
}

variable "data_lifecycle_times" {
  type    = list(string)
  default = ["23:45"]
}

variable "data_lifecycle_retain_rule" {
  type    = number
  default = 14
}

variable "data_lifecycle_copy_tags" {
  type    = bool
  default = true
}

variable "data_lifecycle_tags_to_add" {
  type = map(string)
  default = {
    SnapshotCreator = "DLM"
  }
}
