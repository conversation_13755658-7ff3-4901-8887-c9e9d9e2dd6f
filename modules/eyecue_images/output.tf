output "user" {
  value = {
    "name" : module.iam_user.this_iam_user_name,
    "arn" : module.iam_user.this_iam_user_arn,
    "access_key" : module.iam_user.this_iam_access_key_id,
    "encrypted_secret_key" : module.iam_user.this_iam_access_key_encrypted_secret,
    "pgp_key" : module.iam_user.pgp_key,
    "keybase_command" : module.iam_user.keybase_secret_key_decrypt_command
  }
  sensitive = true
}

output "bucket_arn" {
  description = "bucket_arn"
  value       = "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-images"
}

output "bucket_name" {
  description = "bucket_name"
  value       = "eyecue-${var.client_name}-${var.country}-images"
}

output "image_sync_credentials" {
  description = "bucket_name"
  value       = "eyecue-${var.client_name}-${var.country}-images"
}
