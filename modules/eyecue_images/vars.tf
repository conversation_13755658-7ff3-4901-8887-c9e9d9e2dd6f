variable "aws_iam_user" {
  description = "Name of the IAM User who it depends on"
}

variable "aws_account_id" {}

variable "aws_region" {
  description = "AWS Region"
}

variable "country" {}

variable "country_full" {}

variable "client_name" {}

variable "kms_arn" {
  description = "KMS ARN for the camera displacement module"
}

variable "enable_versioning" {
  description = "Defines if the S3 bucket will version the objects"
  default     = false
}

variable "allowed_methods" {
  description = "Which HTTP methods are allowed"
  type        = list(any)
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
