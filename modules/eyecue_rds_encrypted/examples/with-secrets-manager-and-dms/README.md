# RDS Migration Example: Unencrypted to Encrypted with Secrets Manager

This example demonstrates how to:
1. Create an encrypted RDS instance with credentials stored in AWS Secrets Manager
2. Set up AWS DMS (Database Migration Service) to migrate data from an unencrypted to encrypted RDS instance
3. Maintain zero downtime during migration using CDC (Change Data Capture)

## Prerequisites

- Existing unencrypted RDS instance (source)
- VPC with private subnets for the encrypted RDS and DMS
- Appropriate IAM permissions for creating RDS, DMS, and Secrets Manager resources

## Usage

### Step 1: Configure Variables

Create a `terraform.tfvars` file:

```hcl
vpc_id                = "vpc-xxxxxx"
source_rds_identifier = "my-existing-rds-instance"

# Option A: If source credentials are in Secrets Manager
source_credentials_secret_name = "my-source-db-secret"

# Option B: Direct credentials (less secure)
# source_username = "admin"
# source_password = "your-password-here"
```

### Step 2: Deploy Infrastructure

```bash
# Initialize Terraform
terraform init

# Review the plan
terraform plan

# Create the encrypted RDS and DMS resources
terraform apply
```

### Step 3: Validate DMS Endpoints

After deployment, validate that DMS can connect to both databases:

```bash
# Get the output values
terraform output -json > outputs.json

# Test source endpoint
aws dms test-connection \
  --replication-instance-arn $(jq -r '.dms_replication_instance_arn.value' outputs.json) \
  --endpoint-arn $(jq -r '.dms_source_endpoint_arn.value' outputs.json)

# Test target endpoint  
aws dms test-connection \
  --replication-instance-arn $(jq -r '.dms_replication_instance_arn.value' outputs.json) \
  --endpoint-arn $(jq -r '.dms_target_endpoint_arn.value' outputs.json)
```

### Step 4: Start Migration

```bash
# Start the replication task
aws dms start-replication-task \
  --replication-task-arn $(jq -r '.dms_task_arn.value' outputs.json) \
  --start-replication-task-type start-replication
```

### Step 5: Monitor Migration Progress

```bash
# Check task status
aws dms describe-replication-tasks \
  --filters Name=replication-task-arn,Values=$(jq -r '.dms_task_arn.value' outputs.json) \
  --query 'ReplicationTasks[0].Status'

# View table statistics
aws dms describe-table-statistics \
  --replication-task-arn $(jq -r '.dms_task_arn.value' outputs.json)
```

## Migration Phases

1. **Full Load Phase**: Initial data copy from source to target
   - Duration depends on database size
   - Monitor progress via table statistics

2. **CDC Phase**: Ongoing replication of changes
   - Keeps databases in sync
   - Monitor CDC latency metrics

3. **Validation Phase**: Ensure data integrity
   - Compare row counts
   - Run data validation queries
   - Test application with read-only access

4. **Cutover Phase**: Switch to encrypted database
   - Stop writes to source
   - Wait for CDC to catch up (latency ~0)
   - Update application connection strings
   - Resume application with encrypted database

## Security Features

- **Encryption at Rest**: Target RDS uses AWS-managed encryption
- **Secrets Manager**: Database credentials stored securely
- **Private Networking**: All resources in private subnets
- **SSL/TLS**: Enforced for encrypted RDS connections
- **IAM Authentication**: Enabled for additional security

## Cost Optimization

After successful migration:
1. Stop the DMS replication task
2. Delete DMS replication instance
3. Schedule deletion of source unencrypted RDS
4. Consider creating read replicas for the encrypted instance

## Rollback Plan

If issues occur during migration:
1. Stop the DMS task
2. Applications continue using source database
3. Investigate and resolve issues
4. Restart migration when ready

## Troubleshooting

### Connection Issues
- Verify security group rules allow DMS to connect to RDS
- Ensure DMS replication instance is in correct subnets
- Check Secrets Manager permissions

### Performance Issues
- Increase DMS instance size for large databases
- Optimize source database (vacuum, analyze)
- Consider migrating during low-traffic periods

### Data Validation Issues
- Use DMS validation feature
- Run custom validation queries
- Check for unsupported data types

## Clean Up

To destroy all resources:

```bash
terraform destroy
```

**Warning**: This will delete the encrypted RDS instance and all data. Ensure you have backups before destroying.