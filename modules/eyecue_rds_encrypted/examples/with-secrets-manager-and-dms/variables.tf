variable "vpc_id" {
  description = "VPC ID where resources will be created"
  type        = string
}

variable "source_rds_identifier" {
  description = "Identifier of the source (unencrypted) RDS instance"
  type        = string
}

variable "source_credentials_secret_name" {
  description = "Name of Secrets Manager secret containing source database credentials (optional)"
  type        = string
  default     = ""
}

variable "source_username" {
  description = "Username for source database (used if not using Secrets Manager)"
  type        = string
  default     = ""
  sensitive   = true
}

variable "source_password" {
  description = "Password for source database (used if not using Secrets Manager)"
  type        = string
  default     = ""
  sensitive   = true
}