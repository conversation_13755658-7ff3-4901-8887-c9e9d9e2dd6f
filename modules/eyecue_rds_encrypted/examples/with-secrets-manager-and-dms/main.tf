##############################
# Example: RDS with <PERSON> Manager and DMS Migration
# This example shows how to:
# 1. Create an encrypted RDS instance with credentials in Secrets Manager
# 2. Set up DMS for database migration from unencrypted to encrypted
##############################

locals {
  name_prefix = "eyecue"
  environment = "prod"
  aws_region  = "ap-southeast-2"
  
  tags = {
    Project     = "Eyecue"
    Environment = local.environment
    Terraform   = "true"
    Purpose     = "Database Migration to Encrypted"
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_availability_zones" "available" {}

##############################
# Network Configuration (existing)
##############################
data "aws_vpc" "existing" {
  id = var.vpc_id
}

data "aws_subnets" "private" {
  filter {
    name   = "vpc-id"
    values = [var.vpc_id]
  }
  
  tags = {
    Type = "Private"
  }
}

##############################
# Source RDS (Unencrypted - Existing)
##############################
data "aws_db_instance" "source" {
  db_instance_identifier = var.source_rds_identifier
}

# Get source database credentials from Secrets Manager (if stored there)
data "aws_secretsmanager_secret" "source_credentials" {
  count = var.source_credentials_secret_name != "" ? 1 : 0
  name  = var.source_credentials_secret_name
}

data "aws_secretsmanager_secret_version" "source_credentials" {
  count     = var.source_credentials_secret_name != "" ? 1 : 0
  secret_id = data.aws_secretsmanager_secret.source_credentials[0].id
}

##############################
# Target RDS (Encrypted) with Secrets Manager
##############################
module "rds_encrypted" {
  source = "../../"
  
  # Naming
  name_prefix = local.name_prefix
  name_suffix = "encrypted"
  environment = local.environment
  
  # AWS Configuration
  aws_region     = local.aws_region
  aws_account_id = data.aws_caller_identity.current.account_id
  vpc_id         = data.aws_vpc.existing.id
  subnet_ids     = data.aws_subnets.private.ids
  
  # RDS Configuration - Match source database
  rds_engine         = data.aws_db_instance.source.engine
  rds_engine_version = data.aws_db_instance.source.engine_version
  rds_name           = data.aws_db_instance.source.db_name
  rds_username       = "admin_encrypted" # New username to avoid conflicts
  
  # Instance specifications
  rds_master_instance_class = data.aws_db_instance.source.instance_class
  create_replica            = false # Will enable after migration
  
  # Storage - Enable encryption
  rds_allocated_storage     = data.aws_db_instance.source.allocated_storage
  rds_max_allocated_storage = data.aws_db_instance.source.max_allocated_storage
  rds_storage_type          = data.aws_db_instance.source.storage_type
  rds_storage_encrypted     = true  # KEY DIFFERENCE: Encryption enabled
  
  # Backup Configuration
  rds_backup_retention_period = 30  # Increase for compliance
  rds_backup_window           = "03:00-04:00"
  deletion_protection         = true
  rds_skip_final_snapshot     = false
  
  # High Availability
  multi_az = data.aws_db_instance.source.multi_az
  
  # Performance and Monitoring
  rds_performance_insights_enabled = true
  enabled_cloudwatch_logs_exports  = ["postgresql"]
  
  # Security
  db_publicly_accessible = false
  additional_cidr_blocks = [data.aws_vpc.existing.cidr_block]
  
  # Secrets Manager Configuration
  create_secrets_manager_secret = true
  secrets_manager_name          = "${local.name_prefix}-rds-encrypted-master"
  secrets_manager_description   = "Master credentials for encrypted RDS instance"
  enable_secret_rotation        = false # Enable after migration is complete
  
  # Parameter Group for compliance and DMS
  create_db_parameter_group = true
  parameter_group_parameters = [
    {
      name         = "rds.force_ssl"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "shared_preload_libraries"
      value        = "pg_stat_statements,pglogical"
      apply_method = "pending-reboot"
    },
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_replication_slots"
      value        = "10"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_wal_senders"
      value        = "10"
      apply_method = "pending-reboot"
    },
    {
      name  = "log_statement"
      value = "all"
    },
    {
      name  = "log_connections"
      value = "1"
    }
  ]
  
  tags = local.tags
}

##############################
# DMS Resources for Migration
##############################
module "dms" {
  source  = "terraform-aws-modules/dms/aws"
  version = "~> 2.0"
  
  # DMS Subnet Group
  repl_subnet_group_name        = "${local.name_prefix}-dms-migration"
  repl_subnet_group_description = "DMS subnet group for RDS migration"
  repl_subnet_group_subnet_ids  = data.aws_subnets.private.ids
  
  # DMS Replication Instance
  repl_instance_id                           = "${local.name_prefix}-dms-migration"
  repl_instance_allocated_storage            = 100
  repl_instance_auto_minor_version_upgrade   = false
  repl_instance_class                        = "dms.t3.medium"
  repl_instance_engine_version               = "3.5.3"
  repl_instance_multi_az                     = false
  repl_instance_publicly_accessible          = false
  repl_instance_vpc_security_group_ids       = [aws_security_group.dms.id]
  repl_instance_preferred_maintenance_window = "sun:05:00-sun:06:00"
  
  # Endpoints using Secrets Manager
  endpoints = {
    source = {
      endpoint_id   = "${local.name_prefix}-source-postgres"
      endpoint_type = "source"
      engine_name   = "postgres"
      
      # Use Secrets Manager for credentials
      secrets_manager_arn = var.source_credentials_secret_name != "" ? data.aws_secretsmanager_secret.source_credentials[0].arn : null
      
      # Or use direct connection (less secure)
      server_name   = var.source_credentials_secret_name == "" ? data.aws_db_instance.source.address : null
      port          = var.source_credentials_secret_name == "" ? data.aws_db_instance.source.port : null
      database_name = var.source_credentials_secret_name == "" ? data.aws_db_instance.source.db_name : null
      username      = var.source_credentials_secret_name == "" ? var.source_username : null
      password      = var.source_credentials_secret_name == "" ? var.source_password : null
      
      ssl_mode = "none"  # Source doesn't require SSL
      
      extra_connection_attributes = ""
      
      tags = merge(local.tags, { Type = "source" })
    }
    
    target = {
      endpoint_id   = "${local.name_prefix}-target-postgres-encrypted"
      endpoint_type = "target"
      engine_name   = "postgres"
      
      # Use Secrets Manager for credentials (recommended)
      secrets_manager_arn = module.rds_encrypted.secret_arn
      
      ssl_mode = "require"  # Target requires SSL
      
      extra_connection_attributes = ""
      
      tags = merge(local.tags, { Type = "target" })
    }
  }
  
  # Replication task
  replication_tasks = {
    full_load_and_cdc = {
      replication_task_id = "${local.name_prefix}-migration-task"
      migration_type      = "full-load-and-cdc"
      
      source_endpoint_key = "source"
      target_endpoint_key = "target"
      
      # Start task manually after validation
      start_replication_task = false
      
      table_mappings = jsonencode({
        rules = [
          {
            rule-type = "selection"
            rule-id   = "1"
            rule-name = "migrate-all-tables"
            object-locator = {
              schema-name = "%"
              table-name  = "%"
            }
            rule-action = "include"
          }
        ]
      })
      
      replication_task_settings = jsonencode({
        TargetMetadata = {
          SupportLobs          = true
          FullLobMode          = false
          LobChunkSize         = 64
          LimitedSizeLobMode   = true
          LobMaxSize           = 32
          BatchApplyEnabled    = true
        }
        FullLoadSettings = {
          TargetTablePrepMode           = "DROP_AND_CREATE"
          CreatePkAfterFullLoad         = true
          StopTaskCachedChangesApplied  = false
          StopTaskCachedChangesNotApplied = false
          MaxFullLoadSubTasks           = 8
          TransactionConsistencyTimeout = 600
          CommitRate                    = 10000
        }
        Logging = {
          EnableLogging = true
        }
        ControlTablesSettings = {
          HistoryTimeslotInMinutes     = 5
          HistoryTableEnabled          = false
          SuspendedTablesTableEnabled  = false
          StatusTableEnabled           = false
        }
        StreamBufferSettings = {
          StreamBufferCount    = 3
          StreamBufferSizeInMB = 8
        }
        ChangeProcessingTuning = {
          BatchApplyPreserveTransaction = true
          BatchApplyTimeoutMin          = 1
          BatchApplyTimeoutMax          = 30
          BatchApplyMemoryLimit         = 500
          BatchSplitSize                = 0
          MinTransactionSize            = 1000
          CommitTimeout                 = 1
          MemoryLimitTotal              = 1024
          MemoryKeepTime                = 60
          StatementCacheSize            = 50
        }
        ChangeProcessingDdlHandlingPolicy = {
          HandleSourceTableDropped   = true
          HandleSourceTableTruncated = true
          HandleSourceTableAltered   = true
        }
        ErrorBehavior = {
          DataErrorPolicy              = "LOG_ERROR"
          DataTruncationErrorPolicy    = "LOG_ERROR"
          DataErrorEscalationPolicy    = "SUSPEND_TABLE"
          DataErrorEscalationCount     = 50
          TableErrorPolicy             = "SUSPEND_TABLE"
          TableErrorEscalationPolicy   = "STOP_TASK"
          TableErrorEscalationCount    = 50
          RecoverableErrorCount        = 0
          RecoverableErrorInterval     = 5
          RecoverableErrorThrottling   = true
          RecoverableErrorThrottlingMax = 1800
          ApplyErrorDeletePolicy       = "IGNORE_RECORD"
          ApplyErrorInsertPolicy       = "LOG_ERROR"
          ApplyErrorUpdatePolicy       = "LOG_ERROR"
          ApplyErrorEscalationPolicy   = "LOG_ERROR"
          ApplyErrorEscalationCount    = 0
          FullLoadIgnoreConflicts      = true
        }
      })
      
      tags = local.tags
    }
  }
  
  tags = local.tags
}

##############################
# Security Group for DMS
##############################
resource "aws_security_group" "dms" {
  name        = "${local.name_prefix}-dms-sg"
  description = "Security group for DMS replication instance"
  vpc_id      = data.aws_vpc.existing.id
  
  # Allow outbound to RDS instances
  egress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.existing.cidr_block]
    description = "PostgreSQL to RDS instances"
  }
  
  # Allow all outbound for AWS services
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS for AWS services"
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound"
  }
  
  tags = merge(local.tags, {
    Name = "${local.name_prefix}-dms-sg"
  })
}

##############################
# Outputs
##############################
output "encrypted_rds_endpoint" {
  description = "Endpoint of the encrypted RDS instance"
  value       = module.rds_encrypted.master_db_instance_endpoint
}

output "encrypted_rds_secret_arn" {
  description = "ARN of the Secrets Manager secret for encrypted RDS"
  value       = module.rds_encrypted.secret_arn
}

output "dms_replication_instance_arn" {
  description = "ARN of the DMS replication instance"
  value       = module.dms.replication_instance_arn
}

output "dms_task_arn" {
  description = "ARN of the DMS replication task"
  value       = module.dms.replication_tasks["full_load_and_cdc"].replication_task_arn
}

output "migration_instructions" {
  description = "Instructions for completing the migration"
  value = <<-EOT
    ======================================
    DATABASE MIGRATION INSTRUCTIONS
    ======================================
    
    1. VALIDATE ENDPOINTS:
       aws dms test-connection \
         --replication-instance-arn ${module.dms.replication_instance_arn} \
         --endpoint-arn ${module.dms.endpoints["source"].endpoint_arn}
       
       aws dms test-connection \
         --replication-instance-arn ${module.dms.replication_instance_arn} \
         --endpoint-arn ${module.dms.endpoints["target"].endpoint_arn}
    
    2. START MIGRATION:
       aws dms start-replication-task \
         --replication-task-arn ${module.dms.replication_tasks["full_load_and_cdc"].replication_task_arn} \
         --start-replication-task-type start-replication
    
    3. MONITOR PROGRESS:
       aws dms describe-replication-tasks \
         --filters Name=replication-task-arn,Values=${module.dms.replication_tasks["full_load_and_cdc"].replication_task_arn}
    
    4. VALIDATE DATA:
       - Compare row counts between source and target
       - Verify data integrity
       - Check CDC lag metrics
    
    5. CUTOVER:
       - Stop writes to source database
       - Wait for CDC to catch up
       - Update application connection strings to use: ${module.rds_encrypted.master_db_instance_endpoint}
       - Test application with encrypted database
    
    6. CLEANUP:
       - Stop DMS task after successful validation
       - Delete DMS resources if no longer needed
       - Schedule deletion of source unencrypted database
  EOT
}