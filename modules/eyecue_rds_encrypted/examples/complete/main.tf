# Example usage of the eyecue_rds_encrypted module
# This creates a SOC2 compliant encrypted RDS instance

module "rds_encrypted_soc2" {
  source = "../../"
  
  # Naming configuration
  name_prefix = "eyecue"
  name_suffix = "soc2-encrypted"
  environment = "prod"
  
  # AWS Configuration
  aws_region     = "ap-southeast-2"
  aws_account_id = "************"
  vpc_id         = "vpc-xxxxx"
  subnet_ids     = ["subnet-xxx", "subnet-yyy", "subnet-zzz"]
  
  # RDS Configuration
  rds_engine         = "postgres"
  rds_engine_version = "16.3"
  rds_name           = "myapp_db"
  rds_username       = "dbadmin"
  
  # Instance specifications
  rds_master_instance_class = "db.t3.medium"
  create_replica            = true
  rds_replica_instance_class = "db.t3.small"
  
  # Storage - Encrypted by default
  rds_allocated_storage     = 100
  rds_max_allocated_storage = 500
  rds_storage_type          = "gp3"
  rds_storage_encrypted     = true  # SOC2 requirement
  
  # Backup for SOC2 compliance
  rds_backup_retention_period = 30  # 30 days for SOC2
  rds_backup_window           = "03:00-04:00"
  deletion_protection         = true  # Prevent accidental deletion
  
  # High Availability
  multi_az = true  # For production
  
  # Monitoring
  rds_performance_insights_enabled = true
  enabled_cloudwatch_logs_exports  = ["postgresql"]
  
  # Security
  db_publicly_accessible = false  # Private subnets only
  additional_cidr_blocks = ["10.0.0.0/8"]  # Internal network
  
  # IAM Configuration
  create_iam_role   = true
  create_iam_policy = true
  
  # Parameter Group with security settings
  create_db_parameter_group = true
  parameter_group_parameters = [
    {
      name         = "rds.force_ssl"
      value        = "1"  # Force SSL for SOC2
      apply_method = "pending-reboot"
    },
    {
      name  = "log_statement"
      value = "all"  # Audit logging for SOC2
    },
    {
      name  = "log_connections"
      value = "1"  # Connection logging for SOC2
    },
    {
      name  = "log_disconnections"
      value = "1"  # Disconnection logging for SOC2
    }
  ]
  
  # Tags
  tags = {
    Project     = "Eyecue"
    Environment = "Production"
    Compliance  = "SOC2"
    CostCenter  = "Engineering"
    Owner       = "Platform Team"
  }
  
  rds_master_tags = {
    BackupPolicy = "30-days"
    Criticality  = "High"
  }
}

# Example for development environment with minimal configuration
module "rds_encrypted_dev" {
  source = "../../"
  
  # Minimal required configuration
  name_prefix = "eyecue"
  name_suffix = "dev"
  environment = "dev"
  
  aws_region     = "ap-southeast-2"
  aws_account_id = "************"
  vpc_id         = "vpc-xxxxx"
  subnet_ids     = ["subnet-xxx", "subnet-yyy"]
  
  # Dev-friendly settings
  rds_master_instance_class    = "db.t3.micro"
  rds_allocated_storage        = 20
  rds_backup_retention_period  = 7
  deletion_protection          = false
  rds_apply_changes_immediately = true  # For dev environments
  
  # Skip some production features
  multi_az                         = false
  rds_performance_insights_enabled = false
  create_replica                   = false
  
  tags = {
    Environment = "Development"
    AutoShutdown = "true"
  }
}