##############################
# Secrets Manager Integration
##############################

# Create a secret for RDS master credentials
resource "aws_secretsmanager_secret" "rds_master" {
  count = var.create_secrets_manager_secret ? 1 : 0

  name        = var.secrets_manager_name != "" ? var.secrets_manager_name : "${local.name_prefix}-rds-master-${local.name_suffix}"
  description = var.secrets_manager_description != "" ? var.secrets_manager_description : "Master credentials for RDS instance ${local.rds_instance_identifier}"

  kms_key_id = var.secrets_manager_kms_key_id != "" ? var.secrets_manager_kms_key_id : null

  recovery_window_in_days = var.secrets_manager_recovery_window

  tags = merge(
    local.common_tags,
    {
      Name = var.secrets_manager_name != "" ? var.secrets_manager_name : "${local.name_prefix}-rds-master-${local.name_suffix}"
      Type = "rds-credentials"
    },
    var.secrets_manager_tags
  )
}

# Store the RDS credentials in Secrets Manager
resource "aws_secretsmanager_secret_version" "rds_master" {
  count = var.create_secrets_manager_secret ? 1 : 0

  secret_id = aws_secretsmanager_secret.rds_master[0].id

  secret_string = jsonencode({
    username            = var.rds_username
    password            = var.create_random_password ? random_string.master_password[0].result : var.rds_password
    engine              = var.rds_engine
    host                = module.master.db_instance_address
    port                = var.rds_port
    dbname              = var.rds_name
    dbInstanceIdentifier = local.rds_instance_identifier
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

# Enable automatic rotation if requested
resource "aws_secretsmanager_secret_rotation" "rds_master" {
  count = var.create_secrets_manager_secret && var.enable_secret_rotation ? 1 : 0

  secret_id = aws_secretsmanager_secret.rds_master[0].id

  rotation_lambda_arn = var.rotation_lambda_arn != "" ? var.rotation_lambda_arn : aws_lambda_function.rotation[0].arn

  rotation_rules {
    automatically_after_days = var.rotation_days
  }

  depends_on = [
    aws_secretsmanager_secret_version.rds_master
  ]
}

# Create rotation Lambda function (optional)
resource "aws_lambda_function" "rotation" {
  count = var.create_secrets_manager_secret && var.enable_secret_rotation && var.create_rotation_lambda ? 1 : 0

  filename         = var.rotation_lambda_filename
  function_name    = "${local.name_prefix}-rds-rotation-${local.name_suffix}"
  role            = aws_iam_role.rotation_lambda[0].arn
  handler         = "lambda_function.lambda_handler"
  source_code_hash = var.rotation_lambda_source_code_hash
  runtime         = "python3.9"
  timeout         = 30

  environment {
    variables = {
      SECRETS_MANAGER_ENDPOINT = "https://secretsmanager.${var.aws_region}.amazonaws.com"
    }
  }

  vpc_config {
    subnet_ids         = var.subnet_ids
    security_group_ids = [aws_security_group.main_sec_group.id]
  }

  tags = merge(
    local.common_tags,
    {
      Name = "${local.name_prefix}-rds-rotation-${local.name_suffix}"
      Type = "rotation-lambda"
    }
  )
}

# IAM role for rotation Lambda
resource "aws_iam_role" "rotation_lambda" {
  count = var.create_secrets_manager_secret && var.enable_secret_rotation && var.create_rotation_lambda ? 1 : 0

  name = "${local.name_prefix}-rds-rotation-role-${local.name_suffix}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

# IAM policy for rotation Lambda
resource "aws_iam_role_policy" "rotation_lambda" {
  count = var.create_secrets_manager_secret && var.enable_secret_rotation && var.create_rotation_lambda ? 1 : 0

  name = "${local.name_prefix}-rds-rotation-policy-${local.name_suffix}"
  role = aws_iam_role.rotation_lambda[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:DescribeSecret",
          "secretsmanager:GetSecretValue",
          "secretsmanager:PutSecretValue",
          "secretsmanager:UpdateSecretVersionStage"
        ]
        Resource = aws_secretsmanager_secret.rds_master[0].arn
      },
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetRandomPassword"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "rds:ModifyDBInstance",
          "rds:DescribeDBInstances"
        ]
        Resource = "arn:aws:rds:${var.aws_region}:${var.aws_account_id}:db:${local.rds_instance_identifier}"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${var.aws_region}:${var.aws_account_id}:*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DeleteNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DetachNetworkInterface"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach managed policy for VPC access
resource "aws_iam_role_policy_attachment" "rotation_lambda_vpc" {
  count = var.create_secrets_manager_secret && var.enable_secret_rotation && var.create_rotation_lambda ? 1 : 0

  role       = aws_iam_role.rotation_lambda[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

# Data source to read existing secret (if using existing)
data "aws_secretsmanager_secret" "existing" {
  count = var.use_existing_secret ? 1 : 0
  name  = var.existing_secret_name
}

data "aws_secretsmanager_secret_version" "existing" {
  count     = var.use_existing_secret ? 1 : 0
  secret_id = data.aws_secretsmanager_secret.existing[0].id
}