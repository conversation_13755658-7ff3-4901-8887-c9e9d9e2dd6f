# Eyecue RDS Encrypted Module

A Terraform module for creating encrypted RDS instances with best practices for security and compliance (SOC2, HIPAA, etc.).

## Features

- **Encryption at Rest**: Enforced by default with optional KMS key support
- **Dynamic Naming**: Configurable prefix/suffix to avoid resource conflicts
- **SOC2 Compliance Ready**: Built-in security and audit configurations
- **Flexible IAM**: Optional IAM role/policy creation with customizable names
- **High Availability**: Multi-AZ support for production workloads
- **Comprehensive Monitoring**: Performance Insights and CloudWatch Logs
- **Backup Management**: Configurable retention with deletion protection
- **Security Groups**: Dynamic security group creation with customizable rules
- **AWS Secrets Manager Integration**: Optional secure credential management
- **No DNS Management**: This module does not manage DNS records (use Route53 or other DNS solutions separately)

## Usage

### Basic Example

```hcl
module "rds_encrypted" {
  source = "path/to/modules/eyecue_rds_encrypted"
  
  # Required naming configuration
  name_prefix = "myapp"
  name_suffix = "prod"
  environment = "prod"
  
  # Required AWS configuration
  aws_region     = "us-east-1"
  aws_account_id = "************"
  vpc_id         = "vpc-xxxxx"
  subnet_ids     = ["subnet-xxx", "subnet-yyy"]
  
  # Database configuration
  rds_engine         = "postgres"
  rds_engine_version = "16.3"
  rds_name           = "myapp_db"
}
```

### Production Example with SOC2 Compliance

```hcl
module "rds_soc2_compliant" {
  source = "path/to/modules/eyecue_rds_encrypted"
  
  name_prefix = "eyecue"
  name_suffix = "soc2"
  environment = "prod"
  
  # AWS Configuration
  aws_region     = var.aws_region
  aws_account_id = data.aws_caller_identity.current.account_id
  vpc_id         = module.vpc.vpc_id
  subnet_ids     = module.vpc.private_subnet_ids
  
  # RDS Configuration
  rds_engine                = "postgres"
  rds_engine_version        = "16.3"
  rds_master_instance_class = "db.r6g.large"
  
  # Storage Configuration
  rds_storage_encrypted     = true
  rds_allocated_storage     = 100
  rds_max_allocated_storage = 1000
  rds_storage_type          = "gp3"
  
  # High Availability
  multi_az       = true
  create_replica = true
  
  # Backup Configuration
  rds_backup_retention_period = 30
  deletion_protection         = true
  
  # Monitoring
  rds_performance_insights_enabled = true
  enabled_cloudwatch_logs_exports  = ["postgresql"]
  
  # Security
  db_publicly_accessible = false
  
  # Parameter Group for Compliance
  parameter_group_parameters = [
    {
      name  = "rds.force_ssl"
      value = "1"
    },
    {
      name  = "log_statement"
      value = "all"
    },
    {
      name  = "log_connections"
      value = "1"
    }
  ]
  
  tags = {
    Compliance = "SOC2"
    DataClass  = "Confidential"
  }
}
```

### Using Existing IAM Resources

```hcl
module "rds_with_existing_iam" {
  source = "path/to/modules/eyecue_rds_encrypted"
  
  # ... other configuration ...
  
  # Use existing IAM policy
  create_iam_policy       = false
  existing_iam_policy_arn = "arn:aws:iam::************:policy/existing-policy"
  
  # Create new IAM role with custom name
  create_iam_role = true
  iam_role_name   = "custom-rds-role-name"
}
```

### With AWS Secrets Manager

```hcl
module "rds_with_secrets" {
  source = "path/to/modules/eyecue_rds_encrypted"
  
  # ... other configuration ...
  
  # Enable Secrets Manager integration
  create_db_secret = true
  secret_name      = "myapp/rds/credentials"
  
  # Optionally specify KMS key for secret encryption
  secret_kms_key_id = aws_kms_key.database.id
  
  # Tags for the secret
  secret_tags = {
    Environment = "production"
    Application = "myapp"
  }
}
```

## Resource Naming

All resources are named using the pattern: `{name_prefix}-{resource_type}-{name_suffix}`

For example, with `name_prefix = "myapp"` and `name_suffix = "prod"`:
- RDS Instance: `myapp-prod`
- Security Group: `myapp-sg-prod`
- IAM Role: `myapp-rds-admin-prod`
- IAM Policy: `myapp-rds-policy-prod`

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | >= 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.0 |
| random | >= 3.0 |

## Key Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| name_prefix | Prefix for all resource names | string | "" | no |
| name_suffix | Suffix for all resource names | string | "encrypted" | no |
| environment | Environment name (dev/qa/staging/prod) | string | - | yes |
| aws_region | AWS region | string | - | yes |
| aws_account_id | AWS account ID | string | - | yes |
| vpc_id | VPC ID for RDS deployment | string | - | yes |
| subnet_ids | List of subnet IDs | list(string) | - | yes |
| rds_storage_encrypted | Enable storage encryption | bool | true | no |
| rds_backup_retention_period | Backup retention in days | number | 30 | no |
| deletion_protection | Enable deletion protection | bool | true | no |
| multi_az | Enable Multi-AZ deployment | bool | false | no |
| create_iam_role | Create IAM role | bool | true | no |
| create_iam_policy | Create IAM policy | bool | true | no |

## Outputs

| Name | Description |
|------|-------------|
| master_db_instance_id | The RDS instance ID |
| master_db_instance_address | The address of the RDS instance |
| master_db_instance_endpoint | The connection endpoint (use this for connections) |
| master_db_instance_password | The master password (sensitive) |
| replica_db_instance_id | The replica instance ID (if enabled) |
| replica_db_instance_endpoint | The replica connection endpoint (if enabled) |
| security_group_id | The security group ID |
| iam_role_name | The IAM role name (if created) |
| iam_role_arn | The IAM role ARN (if created) |
| iam_policy_arn | The IAM policy ARN (if created) |
| secrets_manager_secret_arn | The ARN of the secret in Secrets Manager (if enabled) |

## Security Considerations

1. **Encryption**: Storage encryption is enabled by default
2. **Network**: Instances are not publicly accessible by default
3. **SSL/TLS**: Can be enforced via parameter groups
4. **IAM Auth**: IAM database authentication is enabled by default
5. **Backup**: 30-day retention by default with deletion protection
6. **Monitoring**: Performance Insights can be enabled for security monitoring

## SOC2 Compliance Features

This module includes features to support SOC2 compliance:

- Encryption at rest
- Encryption in transit (SSL/TLS)
- Audit logging
- Access controls via IAM
- Backup and recovery procedures
- Monitoring and alerting capabilities
- Network isolation (private subnets)

## Migration from Legacy Module

If migrating from the original `eyecue_rds` module:

1. Use different `name_suffix` to avoid conflicts
2. Set `create_iam_role = true` with unique `iam_role_name`
3. Set `create_iam_policy = true` with unique `iam_policy_name`
4. Import existing resources if needed using `terraform import`
5. DNS records are not managed - configure DNS separately if needed

## DNS Configuration

This module does not include DNS management (no Cloudflare or Route53 integration). 
To set up DNS for your RDS instances:

1. Use the `master_db_instance_endpoint` output for the master database connection
2. Use the `replica_db_instance_endpoint` output for read replica connections (if enabled)
3. Configure your DNS records separately using Route53, Cloudflare, or your preferred DNS provider

Example with Route53:
```hcl
resource "aws_route53_record" "rds_master" {
  zone_id = var.route53_zone_id
  name    = "db.example.com"
  type    = "CNAME"
  ttl     = "300"
  records = [module.rds_encrypted.master_db_instance_address]
}
```

## License

Apache 2.0