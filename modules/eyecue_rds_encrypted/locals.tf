locals {
  # Construct prefix and suffix for resource naming
  name_prefix = var.name_prefix != "" ? var.name_prefix : "${var.environment}-${var.rds_engine}"
  name_suffix = var.name_suffix
  
  # Construct resource names dynamically
  rds_instance_identifier = var.rds_instance_identifier != "" ? var.rds_instance_identifier : "${local.name_prefix}-${local.name_suffix}"
  
  # Security group name
  security_group_name = var.vpc_security_group_name != "" ? var.vpc_security_group_name : "${local.name_prefix}-sg-${local.name_suffix}"
  
  # IAM resource names
  iam_role_name   = var.iam_role_name != "" ? var.iam_role_name : "${local.name_prefix}-rds-admin-${local.name_suffix}"
  iam_policy_name = var.iam_policy_name != "" ? var.iam_policy_name : "${local.name_prefix}-rds-policy-${local.name_suffix}"
  
  # RDS access role names
  rds_access_role_name   = var.rds_access_role_name != "" ? var.rds_access_role_name : "${local.name_prefix}-rds-read-${local.name_suffix}"
  rds_access_policy_name = var.rds_access_policy_name != "" ? var.rds_access_policy_name : "${local.name_prefix}-rds-lambda-${local.name_suffix}"
  
  # Parameter group name
  parameter_group_name = var.parameter_group_name != "" ? var.parameter_group_name : "${local.name_prefix}-${var.rds_engine}${replace(var.rds_engine_version, ".", "")}-params-${local.name_suffix}"
  
  # Subnet group name
  subnet_group_name = var.db_subnet_group_name != "" ? var.db_subnet_group_name : "${local.name_prefix}-subnet-${local.name_suffix}"
  
  # Default tags to merge with user-provided tags
  default_tags = {
    Module      = "eyecue_rds_encrypted"
    Environment = var.environment
    Engine      = var.rds_engine
    Encrypted   = "true"
    ManagedBy   = "Terraform"
  }
  
  # Merge all tags
  common_tags = merge(local.default_tags, var.tags)
  
  # Tags for master instance
  master_tags = merge(
    local.common_tags,
    {
      Name = local.rds_instance_identifier
      Type = "master"
    },
    var.rds_master_tags
  )
  
  # Tags for replica instance
  replica_tags = merge(
    local.common_tags,
    {
      Name = "${local.rds_instance_identifier}-replica"
      Type = "replica"
    },
    var.rds_replica_tags
  )
  
  # AWS IP configuration for prefix list (if enabled)
  current_region = var.aws_region
  
  # Corporate/Office IPs that should always have access
  corporate_cidrs = [
    "*************/32", # Fingermark Office
    "**************/32" # Wireguard VPN
  ]
  
  # Parse the AWS IP ranges JSON (empty if prefix list is disabled)
  aws_ip_data = var.enable_prefix_list ? jsondecode(data.http.aws_ip_ranges[0].response_body) : { prefixes = [] }
  
  # Get EC2 ranges for current region + us-east-1 (global services)
  # us-east-1 is required for global AWS services that interact with RDS
  aws_ec2_ranges = distinct(concat(
    # Current region EC2 ranges
    [for prefix in local.aws_ip_data.prefixes :
      prefix.ip_prefix
      if prefix.region == local.current_region && prefix.service == "EC2"
    ],
    # us-east-1 EC2 ranges (for global services)
    [for prefix in local.aws_ip_data.prefixes :
      prefix.ip_prefix
      if prefix.region == "us-east-1" && prefix.service == "EC2"
    ]
  ))
  
  # Combine EC2 ranges with corporate IPs for the prefix list
  # This keeps all allowed IPs in one prefix list
  all_allowed_cidrs = distinct(concat(
    local.aws_ec2_ranges,
    local.corporate_cidrs
  ))
  
  # Count total IPs for validation
  total_ip_count = length(local.all_allowed_cidrs)
  
  # AWS Managed Prefix List limits:
  # - Default max entries: 1000 (can be increased)
  # - Each prefix list reference in SG counts as 1 rule
  prefix_list_max_entries = 1000
  
  # Check if we need multiple prefix lists (unlikely for single region)
  needs_multiple_prefix_lists = local.total_ip_count > local.prefix_list_max_entries
  
  # Split into chunks if needed - AWS limit is 100 entries per creation request
  # But the prefix list can have up to 1000 entries total (added in batches)
  prefix_list_chunks = local.total_ip_count > 100 ? chunklist(local.all_allowed_cidrs, 100) : [local.all_allowed_cidrs]
}