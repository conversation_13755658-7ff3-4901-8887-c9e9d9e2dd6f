##############################
# Naming Convention Variables
##############################
variable "name_prefix" {
  description = "Prefix to be used for all resource names to ensure uniqueness"
  type        = string
  default     = ""
}

variable "name_suffix" {
  description = "Suffix to be used for all resource names (e.g., 'encrypted', 'soc2')"
  type        = string
  default     = "encrypted"
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "qa", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, qa, staging, prod"
  }
}

##############################
# General AWS Configuration
##############################
variable "aws_region" {
  description = "AWS region where resources will be created"
  type        = string
}

variable "aws_account_id" {
  description = "AWS account ID"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where RDS will be deployed"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for RDS deployment"
  type        = list(string)
}

##############################
# RDS Instance Configuration
##############################
variable "rds_instance_identifier" {
  description = "Identifier for the RDS instance. If not provided, will use {name_prefix}-{engine}-{name_suffix}"
  type        = string
  default     = ""
}

variable "rds_name" {
  description = "Name of the database to create"
  type        = string
  default     = "eyeq"
}

variable "rds_username" {
  description = "Master username for the database"
  type        = string
  default     = "dbadmin"
  sensitive   = true
}

variable "rds_engine" {
  description = "Database engine"
  type        = string
  default     = "postgres"
  validation {
    condition     = contains(["postgres", "mysql", "mariadb"], var.rds_engine)
    error_message = "Engine must be one of: postgres, mysql, mariadb"
  }
}

variable "rds_engine_version" {
  description = "Database engine version"
  type        = string
  default     = "16.3"
}

variable "rds_port" {
  description = "Database port"
  type        = number
  default     = 5432
}

##############################
# Instance Specifications
##############################
variable "rds_master_instance_class" {
  description = "Instance class for the master database"
  type        = string
  default     = "db.t3.medium"
}

variable "rds_replica_instance_class" {
  description = "Instance class for read replicas"
  type        = string
  default     = "db.t3.micro"
}

variable "create_replica" {
  description = "Whether to create a read replica"
  type        = bool
  default     = false
}

##############################
# Storage Configuration
##############################
variable "rds_allocated_storage" {
  description = "Allocated storage in GB"
  type        = number
  default     = 100
}

variable "rds_max_allocated_storage" {
  description = "Maximum allocated storage for autoscaling in GB"
  type        = number
  default     = 500
}

variable "rds_storage_type" {
  description = "Storage type (gp2, gp3, io1)"
  type        = string
  default     = "gp3"
  validation {
    condition     = contains(["gp2", "gp3", "io1"], var.rds_storage_type)
    error_message = "Storage type must be one of: gp2, gp3, io1"
  }
}

variable "rds_storage_encrypted" {
  description = "Enable storage encryption"
  type        = bool
  default     = true
}

variable "rds_kms_key_id" {
  description = "KMS key ID for storage encryption. If not provided, AWS managed key will be used"
  type        = string
  default     = ""
}

##############################
# Backup and Maintenance
##############################
variable "rds_backup_retention_period" {
  description = "Number of days to retain backups"
  type        = number
  default     = 30
  validation {
    condition     = var.rds_backup_retention_period >= 0 && var.rds_backup_retention_period <= 35
    error_message = "Backup retention period must be between 0 and 35 days"
  }
}

variable "rds_backup_window" {
  description = "Preferred backup window"
  type        = string
  default     = "03:00-04:00"
}

variable "rds_maintenance_window" {
  description = "Preferred maintenance window"
  type        = string
  default     = "sun:04:00-sun:05:00"
}

variable "rds_skip_final_snapshot" {
  description = "Skip final snapshot when destroying"
  type        = bool
  default     = false
}

variable "deletion_protection" {
  description = "Enable deletion protection"
  type        = bool
  default     = true
}

##############################
# Performance and Monitoring
##############################
variable "rds_performance_insights_enabled" {
  description = "Enable Performance Insights"
  type        = bool
  default     = true
}

variable "rds_performance_insights_retention_period" {
  description = "Performance Insights retention period in days"
  type        = number
  default     = 7
  validation {
    condition     = contains([7, 31, 62, 93, 124, 155, 186, 217, 248, 279, 310, 341, 372, 403, 434, 465, 496, 527, 558, 589, 620, 651, 682, 713, 731], var.rds_performance_insights_retention_period)
    error_message = "Must be one of the valid retention periods for Performance Insights"
  }
}

variable "enabled_cloudwatch_logs_exports" {
  description = "List of log types to export to CloudWatch"
  type        = list(string)
  default     = ["postgresql"]
}

##############################
# High Availability
##############################
variable "multi_az" {
  description = "Enable Multi-AZ deployment"
  type        = bool
  default     = false
}

variable "rds_apply_changes_immediately" {
  description = "Apply changes immediately instead of during maintenance window"
  type        = bool
  default     = false
}

##############################
# Security Configuration
##############################
variable "db_publicly_accessible" {
  description = "Whether the database should be publicly accessible"
  type        = bool
  default     = false
}

variable "vpc_security_group_ids" {
  description = "Additional security group IDs to attach"
  type        = list(string)
  default     = []
}

variable "vpc_security_group_name" {
  description = "Name for the security group. If not provided, will use {name_prefix}-rds-sg-{name_suffix}"
  type        = string
  default     = ""
}

variable "vpc_security_group_description" {
  description = "Description for the security group"
  type        = string
  default     = "Security group for RDS instance"
}

variable "additional_cidr_blocks" {
  description = "Additional CIDR blocks to allow access"
  type        = list(string)
  default     = []
}

##############################
# IAM Configuration
##############################
variable "create_iam_role" {
  description = "Whether to create IAM role for RDS access"
  type        = bool
  default     = true
}

variable "iam_role_name" {
  description = "Name for the IAM role. If not provided, will use {name_prefix}-rds-admin-{name_suffix}"
  type        = string
  default     = ""
}

variable "create_iam_policy" {
  description = "Whether to create IAM policy for RDS access"
  type        = bool
  default     = true
}

variable "iam_policy_name" {
  description = "Name for the IAM policy. If not provided, will use {name_prefix}-rds-policy-{name_suffix}"
  type        = string
  default     = ""
}

variable "existing_iam_policy_arn" {
  description = "ARN of existing IAM policy to use when create_iam_policy is false"
  type        = string
  default     = ""
}

variable "iam_database_authentication_enabled" {
  description = "Enable IAM database authentication"
  type        = bool
  default     = true
}

##############################
# Parameter Group Configuration
##############################
variable "create_db_parameter_group" {
  description = "Whether to create a parameter group"
  type        = bool
  default     = true
}

variable "parameter_group_name" {
  description = "Name of the parameter group. If not provided, will use {name_prefix}-{engine}-params-{name_suffix}"
  type        = string
  default     = ""
}

variable "parameter_group_family" {
  description = "Database parameter group family"
  type        = string
  default     = "postgres16"
}

variable "parameter_group_parameters" {
  description = "List of parameters to apply to the parameter group"
  type = list(object({
    name         = string
    value        = string
    apply_method = optional(string, "immediate")
  }))
  default = []
}

##############################
# Password Configuration
##############################
variable "create_random_password" {
  description = "Whether to create a random password"
  type        = bool
  default     = true
}

variable "special_password" {
  description = "Include special characters in generated password"
  type        = bool
  default     = false
}

variable "rds_create_random_password" {
  description = "Let RDS module create random password"
  type        = bool
  default     = false
}

##############################
# RDS Access Role Module
##############################
variable "create_rds_access_role" {
  description = "Whether to create RDS access role module"
  type        = bool
  default     = true
}

variable "rds_access_role_name" {
  description = "Name for RDS access role. If not provided, will use {name_prefix}-rds-read-{name_suffix}"
  type        = string
  default     = ""
}

variable "rds_access_policy_name" {
  description = "Name for RDS access policy. If not provided, will use {name_prefix}-rds-lambda-{name_suffix}"
  type        = string
  default     = ""
}

variable "eyecue_rds_roles_allowed_to_read" {
  description = "List of IAM roles allowed to assume the RDS read role"
  type        = list(string)
  default     = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}

##############################
# DNS Configuration (Optional)
##############################
variable "create_dns_records" {
  description = "Whether to create DNS records"
  type        = bool
  default     = false
}

variable "dns_zone_id" {
  description = "Route53 zone ID for DNS records"
  type        = string
  default     = ""
}

variable "dns_record_prefix" {
  description = "Prefix for DNS record names"
  type        = string
  default     = ""
}

##############################
# Tags
##############################
variable "tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "rds_master_tags" {
  description = "Additional tags for master RDS instance"
  type        = map(string)
  default     = {}
}

variable "rds_replica_tags" {
  description = "Additional tags for replica RDS instances"
  type        = map(string)
  default     = {}
}

##############################
# Advanced Configuration
##############################
variable "rds_ca_cert_identifier" {
  description = "CA certificate identifier"
  type        = string
  default     = "rds-ca-rsa2048-g1"
}

variable "allow_major_version_upgrade" {
  description = "Allow major version upgrades"
  type        = bool
  default     = false
}

variable "rds_timeout" {
  description = "Timeout configuration for RDS operations"
  type = object({
    create = optional(string, "40m")
    update = optional(string, "80m")
    delete = optional(string, "40m")
  })
  default = {}
}

variable "db_subnet_group_name" {
  description = "Name of DB subnet group. If not provided, will be created"
  type        = string
  default     = ""
}

variable "rds_create_db_subnet_group" {
  description = "Whether to create DB subnet group"
  type        = bool
  default     = true
}

variable "db_subnet_group_use_name_prefix" {
  description = "Use name prefix for DB subnet group"
  type        = bool
  default     = false
}

variable "parameter_group_use_name_prefix" {
  description = "Use name prefix for parameter group"
  type        = bool
  default     = false
}

variable "parameter_group_description" {
  description = "Description for parameter group"
  type        = string
  default     = ""
}

##############################
# IOPS Configuration
##############################
variable "rds_master_iops" {
  description = "IOPS for master instance (required for io1 storage type)"
  type        = number
  default     = null
}

variable "rds_replica_iops" {
  description = "IOPS for replica instances"
  type        = number
  default     = null
}

##############################
# AWS IP Prefix List Configuration
##############################
variable "enable_prefix_list" {
  description = "Enable AWS prefix list for security group rules"
  type        = bool
  default     = false
}

##############################
# Secrets Manager Configuration
##############################
variable "create_secrets_manager_secret" {
  description = "Whether to create a Secrets Manager secret for RDS credentials"
  type        = bool
  default     = true
}

variable "secrets_manager_name" {
  description = "Name for the Secrets Manager secret. If not provided, will use {name_prefix}-rds-master-{name_suffix}"
  type        = string
  default     = ""
}

variable "secrets_manager_description" {
  description = "Description for the Secrets Manager secret"
  type        = string
  default     = ""
}

variable "secrets_manager_kms_key_id" {
  description = "KMS key ID for encrypting the secret. If not provided, uses AWS managed key"
  type        = string
  default     = ""
}

variable "secrets_manager_recovery_window" {
  description = "Number of days AWS Secrets Manager waits before deleting the secret"
  type        = number
  default     = 30
}

variable "secrets_manager_tags" {
  description = "Additional tags for Secrets Manager secret"
  type        = map(string)
  default     = {}
}

variable "enable_secret_rotation" {
  description = "Enable automatic rotation of the secret"
  type        = bool
  default     = false
}

variable "rotation_days" {
  description = "Number of days between automatic rotations"
  type        = number
  default     = 30
}

variable "create_rotation_lambda" {
  description = "Whether to create a Lambda function for secret rotation"
  type        = bool
  default     = false
}

variable "rotation_lambda_arn" {
  description = "ARN of existing Lambda function for rotation. If not provided and create_rotation_lambda is false, rotation will not be configured"
  type        = string
  default     = ""
}

variable "rotation_lambda_filename" {
  description = "Path to the Lambda function code for rotation"
  type        = string
  default     = ""
}

variable "rotation_lambda_source_code_hash" {
  description = "Source code hash for the rotation Lambda function"
  type        = string
  default     = ""
}

variable "use_existing_secret" {
  description = "Whether to use an existing Secrets Manager secret"
  type        = bool
  default     = false
}

variable "existing_secret_name" {
  description = "Name of existing Secrets Manager secret to use"
  type        = string
  default     = ""
}

variable "rds_password" {
  description = "Master password for RDS (used when not generating random password)"
  type        = string
  default     = ""
  sensitive   = true
}