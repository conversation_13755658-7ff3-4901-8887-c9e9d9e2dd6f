data "aws_caller_identity" "current" {}

# Fetch AWS IP ranges JSON directly from AWS (only when prefix list is enabled)
data "http" "aws_ip_ranges" {
  count = var.enable_prefix_list ? 1 : 0
  url   = "https://ip-ranges.amazonaws.com/ip-ranges.json"
}

# All locals are defined in locals.tf to avoid duplication

# Create Managed Prefix List for EC2 ranges + Corporate IPs
# Includes current region + us-east-1 for global services
# Only created when enable_prefix_list is true
resource "aws_ec2_managed_prefix_list" "allowed_ips" {
  count          = var.enable_prefix_list ? 1 : 0
  name           = "${var.vpc_security_group_name}-allowed-ips-${local.current_region}"
  address_family = "IPv4"
  max_entries    = local.total_ip_count + 50 # Total capacity with buffer for growth

  # Initial entries - limited to first 100 for creation
  dynamic "entry" {
    for_each = slice(local.all_allowed_cidrs, 0, min(100, length(local.all_allowed_cidrs)))
    content {
      cidr        = entry.value
      description = contains(local.corporate_cidrs, entry.value) ? "Corporate: ${entry.value}" : "AWS EC2"
    }
  }

  tags = merge(var.tags, {
    Name           = "${var.vpc_security_group_name}-allowed-ips"
    Description    = "EC2 ranges (${local.current_region} + us-east-1) + Corporate IPs"
    Purpose        = "RDS access control"
    Region         = local.current_region
    IPCount        = local.total_ip_count
    EC2Ranges      = length(local.aws_ec2_ranges)
    CorpIPs        = length(local.corporate_cidrs)
    IncludesGlobal = "true"
  })
}

# Generate random password for master instance

resource "random_string" "master_password" {
  count   = var.create_random_password ? 1 : 0
  length  = 32
  special = var.special_password
}

module "master" {
  # https://github.com/terraform-aws-modules/terraform-aws-rds
  source  = "terraform-aws-modules/rds/aws"
  version = "5.2.0"

  identifier     = local.rds_instance_identifier
  engine         = var.rds_engine
  engine_version = var.rds_engine_version

  instance_class        = var.rds_master_instance_class
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_type          = var.rds_storage_type
  storage_encrypted     = var.rds_storage_encrypted
  kms_key_id            = var.rds_kms_key_id != "" ? var.rds_kms_key_id : null
  iops                  = var.rds_master_iops

  db_name                = var.rds_name
  username               = var.rds_username
  password               = var.create_random_password ? random_string.master_password[0].result : null
  port                   = var.rds_port
  create_random_password = var.rds_create_random_password

  publicly_accessible    = var.db_publicly_accessible
  vpc_security_group_ids = concat(var.vpc_security_group_ids, [aws_security_group.main_sec_group.id])
  subnet_ids             = var.subnet_ids

  maintenance_window      = var.rds_maintenance_window
  backup_window           = var.rds_backup_window
  backup_retention_period = var.rds_backup_retention_period
  skip_final_snapshot     = var.rds_skip_final_snapshot
  deletion_protection     = var.deletion_protection
  multi_az                = var.multi_az
  apply_immediately       = var.rds_apply_changes_immediately

  create_db_option_group          = false
  create_db_subnet_group          = var.rds_create_db_subnet_group
  db_subnet_group_name            = var.db_subnet_group_name
  db_subnet_group_use_name_prefix = var.db_subnet_group_use_name_prefix
  timeouts                        = var.rds_timeout

  create_db_parameter_group       = var.create_db_parameter_group
  parameter_group_description     = var.parameter_group_description
  parameter_group_name            = var.parameter_group_name
  parameter_group_use_name_prefix = var.parameter_group_use_name_prefix
  family                          = var.parameter_group_family
  parameters                      = var.parameter_group_parameters

  performance_insights_enabled        = var.rds_performance_insights_enabled
  allow_major_version_upgrade         = var.allow_major_version_upgrade
  iam_database_authentication_enabled = true

  ca_cert_identifier = var.rds_ca_cert_identifier
  tags               = var.rds_master_tags
}

module "replica" {
  create_db_instance = var.create_replica
  source             = "terraform-aws-modules/rds/aws"
  version            = "5.2.0"
  identifier         = "${var.rds_instance_identifier}-replica"

  ### Source database. For cross-region use this_db_instance_arn
  replicate_source_db = module.master.db_instance_id

  engine                = var.rds_engine
  engine_version        = var.rds_engine_version
  instance_class        = var.rds_replica_instance_class
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_encrypted     = true
  publicly_accessible   = var.db_publicly_accessible
  storage_type          = var.rds_storage_type
  iops                  = var.rds_replica_iops
  skip_final_snapshot   = true
  ### Username and password must not be set for replicas
  port     = var.rds_port

  iam_database_authentication_enabled = true

  vpc_security_group_ids = concat(var.vpc_security_group_ids, [aws_security_group.main_sec_group.id])
  maintenance_window     = "Tue:00:00-Tue:03:00"
  backup_window          = "03:00-06:00"
  apply_immediately      = var.rds_apply_changes_immediately
  ### disable backups to create DB faster

  backup_retention_period = 0
  ### Not allowed to specify a subnet group for replicas in the same region
  create_db_subnet_group    = false
  create_db_option_group    = false
  create_db_parameter_group = false

  ca_cert_identifier = var.rds_ca_cert_identifier
  tags               = var.rds_replica_tags
}

resource "aws_iam_role" "eyecue_admin_role" {
  count = var.create_iam_role ? 1 : 0
  name  = local.iam_role_name
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Condition = {
        StringLike = {
          "api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc:sub" = "*"
        }
      },
      Action = "sts:AssumeRoleWithWebIdentity",
      Principal = {
        Federated = "arn:aws:iam::${var.aws_account_id}:oidc-provider/api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc"
      }
    }]
  })
  tags = var.tags
}

data "aws_iam_policy_document" "eyecue_admin_policy" {
  statement {
    actions = [
      "rds-db:connect",
    ]
    resources = ["arn:aws:rds-db:${var.aws_region}:${var.aws_account_id}:dbuser:*/eyecue_admin"]
  }
}

resource "aws_iam_policy" "eyecue_admin_policy" {
  count       = var.create_iam_policy ? 1 : 0
  name        = local.iam_policy_name
  description = "Custom policy for EyecueAdmin to manage RDS instances"
  policy      = data.aws_iam_policy_document.eyecue_admin_policy.json
  tags        = var.tags
}

resource "aws_iam_role_policy_attachment" "eyecue_admin_policy_attachment" {
  count      = var.create_iam_role ? 1 : 0
  role       = aws_iam_role.eyecue_admin_role[0].name
  policy_arn = var.create_iam_policy ? aws_iam_policy.eyecue_admin_policy[0].arn : var.existing_iam_policy_arn
}

### Create IAM Policy and Role for IAM authentication

module "rds_access_role" {
  roles_allowed_to_assume = var.eyecue_rds_roles_allowed_to_read
  source                  = "../rds_access_role_encrypted"
  aws_account_id          = var.aws_account_id
  aws_region              = var.aws_region
  instance_id             = var.create_replica ? module.replica.db_instance_resource_id : module.master.db_instance_resource_id
}


# Security Groups
resource "aws_security_group" "main_sec_group" {
  vpc_id      = var.vpc_id
  name        = local.security_group_name
  description = var.vpc_security_group_description
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Conditional ingress rules based on enable_prefix_list flag

  # When prefix list is enabled: Use AWS IP ranges via prefix list
  dynamic "ingress" {
    for_each = var.enable_prefix_list ? [1] : []
    content {
      from_port       = var.rds_port
      to_port         = var.rds_port
      protocol        = "tcp"
      prefix_list_ids = [aws_ec2_managed_prefix_list.allowed_ips[0].id]
      description     = "Allow EC2/Lambda (${local.current_region} + us-east-1) and Corporate IPs via prefix list"
    }
  }

  # When prefix list is disabled: Use 0.0.0.0/0 (temporary until SG limits are increased)
  dynamic "ingress" {
    for_each = var.enable_prefix_list ? [] : [1]
    content {
      from_port   = var.rds_port
      to_port     = var.rds_port
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
      description = "Allow all traffic (temporary - prefix list disabled due to SG rule limits)"
    }
  }

  # Optional: Add specific additional CIDRs if needed
  dynamic "ingress" {
    for_each = var.additional_cidr_blocks
    content {
      from_port   = var.rds_port
      to_port     = var.rds_port
      protocol    = "tcp"
      cidr_blocks = [ingress.value]
      description = "Additional CIDR: ${ingress.value}"
    }
  }

  tags = var.tags
}

# Cloudflare DNS modules removed - not needed for encrypted module
