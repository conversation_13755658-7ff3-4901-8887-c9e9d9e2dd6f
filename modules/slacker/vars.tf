variable "default_tags" {
  type = map(string)
  default = {
    Terraform   = "True"
    Stack       = "Monitoring"
    Product     = "Eyecue"
    Squad       = "Platform"
    Environment = "Prod"
  }
}

variable "tags" {
  type = map(any)
  default = {
    Product     = "Eyecue"
    Squad       = "Platform"
    Environment = "Prod"
  }
}

variable "stage" {
  description = "Application stage: prod, dev, etc"
  type        = string
  default     = "dev"
}

variable "application_name" {
  type    = string
  default = "Slacker"
}

variable "slack_web_hook" {
  type    = string
  default = ""
}


variable "aws_iam_user" {
  type    = string
  default = "slacker"
}

variable "keybase" {
  type    = string
  default = "keybase:fingermark"
}

variable "slacker_image_name" {
  type    = string
  default = "infra-slacker"
}

variable "slacker_image_tag" {
  type    = string
  default = "0.0.1"
}