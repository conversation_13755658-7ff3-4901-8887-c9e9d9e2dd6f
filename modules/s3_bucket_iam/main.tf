module "iam_user_write" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = "${var.aws_iam_user}-write"
  create_iam_access_key         = false
  create_iam_user_login_profile = false
  force_destroy                 = false
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

resource "aws_iam_access_key" "iam_user_write_access_key" {
  lifecycle {
    ignore_changes = [
      pgp_key,
    ]
  }
  user    = "${var.aws_iam_user}-write"
  pgp_key = var.keybase
}


module "iam_user_read" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = "${var.aws_iam_user}-read"
  create_iam_access_key         = false
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

resource "aws_iam_access_key" "iam_user_read_access_key" {
  lifecycle {
    ignore_changes = [
      pgp_key,
    ]
  }
  user    = "${var.aws_iam_user}-read"
  pgp_key = var.keybase
}

resource "aws_iam_policy" "policy_write" {
  name        = "${var.iam_policy_prefix_name}PolicyWrite"
  depends_on  = [module.iam_user_write]
  path        = "/"
  description = ""
  policy      = data.aws_iam_policy_document.policy_write.json
}

resource "aws_iam_user_policy_attachment" "s3_policy_attachment_write" {
  user       = "${var.aws_iam_user}-write"
  policy_arn = aws_iam_policy.policy_write.arn
}

resource "aws_iam_policy" "policy_read" {
  name        = "${var.iam_policy_prefix_name}PolicyRead"
  depends_on  = [module.iam_user_read]
  path        = "/"
  description = ""
  policy      = data.aws_iam_policy_document.policy_read.json
}

resource "aws_iam_user_policy_attachment" "s3_policy_attachment_read" {
  user       = "${var.aws_iam_user}-read"
  policy_arn = aws_iam_policy.policy_read.arn
}

resource "aws_secretsmanager_secret" "credentials_read" {
  name = "${var.aws_iam_user}-read-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials_read" {
  secret_id = aws_secretsmanager_secret.credentials_read.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user_read.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user_read.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user_read.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user_read.keybase_secret_key_decrypt_command
  })
}

resource "aws_secretsmanager_secret" "credentials_write" {
  name = "${var.aws_iam_user}-write-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials_write" {
  secret_id = aws_secretsmanager_secret.credentials_write.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user_write.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user_write.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user_write.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user_write.keybase_secret_key_decrypt_command
  })
}
