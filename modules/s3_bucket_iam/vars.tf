variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "bucket_resources_arn_list" {
  type = list(string)
}

variable "aws_iam_user" {
  description = "Name of the IAM User who it depends on"
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "iam_policy_prefix_name" {
  default = ""
}
