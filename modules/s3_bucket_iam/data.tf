data "aws_iam_policy_document" "policy_write" {
  statement {
    sid = "${var.iam_policy_prefix_name}PolicyWrite"
    actions = [
      "s3:GetObject",
      "s3:GetObjectAcl",
      "s3:ListBucket",
      "s3:DeleteObject",
      "s3:PutObject",
    ]
    resources = var.bucket_resources_arn_list
  }
}

data "aws_iam_policy_document" "policy_read" {
  statement {
    sid = "${var.iam_policy_prefix_name}PolicyRead"
    actions = [
      "s3:GetObject",
      "s3:GetObjectAcl",
      "s3:ListBucket",
    ]
    resources = var.bucket_resources_arn_list
  }
}