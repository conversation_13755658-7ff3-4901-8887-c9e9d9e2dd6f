resource "aws_iam_policy" "ICXeedAccessPolicy" {
  name        = "ICXeedAccessPolicy"
  description = "Provides controlled access to AWS services for ICXeed team"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          // AWS Support - specific permissions for case management only
          "support:AddAttachmentsToSet",
          "support:AddCommunicationToCase",
          "support:CreateCase",
          "support:Describe*",
          "support:ResolveCase",
          "support:SearchForCases",

          // Lambda - controlled access
          "lambda:List*",
          "lambda:Get*",
          "lambda:InvokeFunction",
          "lambda:CreateFunction",
          "lambda:UpdateFunctionCode",
          "lambda:UpdateFunctionConfiguration",
          "lambda:PublishVersion",
          "lambda:CreateAlias",
          "lambda:UpdateAlias",
          "lambda:DeleteAlias",
          "lambda:TagResource",
          "lambda:UntagResource",

          // CloudWatch - full read and partial write
          "cloudwatch:Describe*",
          "cloudwatch:Get*",
          "cloudwatch:List*",
          "cloudwatch:PutMetricData",
          "cloudwatch:PutMetricAlarm",
          "cloudwatch:DeleteAlarms",
          "logs:Describe*",
          "logs:Get*",
          "logs:List*",
          "logs:StartQuery",
          "logs:StopQuery",
          "logs:FilterLogEvents",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",

          // Polly - full access
          "polly:*",

          // Lex - full access
          "lex:*",

          // S3 - controlled access 
          "s3:List*",
          "s3:Get*",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:CreateBucket",
          "s3:PutBucketTagging",
          "s3:PutBucketPolicy",
          "s3:PutEncryptionConfiguration",
          "s3:PutBucketPublicAccessBlock",
          "s3:PutBucketAcl",
          "s3:PutObjectAcl",

          // DynamoDB - controlled access
          "dynamodb:List*",
          "dynamodb:Describe*",
          "dynamodb:Get*",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchGetItem",
          "dynamodb:CreateTable",
          "dynamodb:UpdateTable",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:TagResource",
          "dynamodb:UntagResource",

          // Kinesis - controlled access
          "kinesis:List*",
          "kinesis:Describe*",
          "kinesis:Get*",
          "kinesis:CreateStream",
          "kinesis:PutRecord",
          "kinesis:PutRecords",
          "kinesis:GetRecords",
          "kinesis:GetShardIterator",

          // IAM - limited read-only access
          "iam:List*",
          "iam:Get*",
          "iam:GenerateCredentialReport",
          "iam:GenerateServiceLastAccessedDetails",

          // CloudFormation - limited access
          "cloudformation:Describe*",
          "cloudformation:List*",
          "cloudformation:Get*",
          "cloudformation:ValidateTemplate",
          "cloudformation:CreateStack",
          "cloudformation:UpdateStack",
          "cloudformation:DeleteStack",
          "cloudformation:TagResource",
          "cloudformation:UntagResource",

          // Connect - full access
          "connect:*",

          // SES - full access
          "ses:*"
        ],
        Resource = "*"
      },
      {
        // Explicitly deny dangerous IAM permissions
        Effect = "Deny",
        Action = [
          // Explicitly deny permissions to modify IAM users, roles, and policies
          "iam:CreateUser",
          "iam:DeleteUser",
          "iam:CreateRole",
          "iam:DeleteRole",
          "iam:AttachRolePolicy",
          "iam:DetachRolePolicy",
          "iam:AttachUserPolicy",
          "iam:DetachUserPolicy",
          "iam:CreatePolicy",
          "iam:DeletePolicy",
          "iam:DeleteRolePermissionsBoundary",

          // Explicitly deny permissions to modify the support plan
          "support:CreateAccount",
          "support:ModifyDefaultSupportPlan",
          "support:PurchaseSupportPlan"
        ],
        Resource = "*"
      }
    ]
  })
}
