resource "aws_iam_policy" "SupportAccessPolicy" {
  name        = "SupportAccessPolicy"
  description = "Provides read-only access to AWS resources and case management without support plan modification"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          // AWS Support - specific permissions for case management only
          "support:AddAttachmentsToSet",
          "support:AddCommunicationToCase",
          "support:CreateCase",
          "support:Describe*",
          "support:ResolveCase",
          "support:SearchForCases",

          // CloudWatch & Logs - read-only
          "cloudwatch:Describe*",
          "cloudwatch:Get*",
          "cloudwatch:List*",
          "logs:Describe*",
          "logs:Get*",
          "logs:List*",
          "logs:StartQuery",
          "logs:StopQuery",
          "logs:TestMetricFilter",
          "logs:FilterLogEvents",

          // EC2 & Related Services - read-only
          "ec2:Describe*",
          "elasticloadbalancing:Describe*",

          // Network & DNS - read-only
          "route53:List*",
          "route53:Get*",

          // S3 - read-only
          "s3:GetBucketLocation",
          "s3:GetBucketTagging",
          "s3:ListAllMyBuckets",
          "s3:ListBucket",

          // AWS Connect - read-only for research purposes
          "connect:Describe*",
          "connect:Get*",
          "connect:List*",
          "connect:Search*",
          "connect:BatchGetAttachedContactReference",
          "connect:BatchGetFlowAssociation",
          "connect:GetMetricData",
          "connect:GetCurrentMetricData",
          "connect:GetCurrentUserData",
          "connect:GetContactAttributes",

          // AWS Polly - expanded permissions for interaction and creation
          "polly:Describe*",
          "polly:List*",
          "polly:Get*",
          "polly:SynthesizeSpeech",
          "polly:StartSpeechSynthesisTask",
          "polly:GetSpeechSynthesisTask",
          "polly:PutLexicon",
          "polly:DeleteLexicon",
          "polly:CreateLexicon",
          "polly:LexiconAttributes",
          "polly:UpdateLexicon"
        ],
        Resource = "*"
      },
      {
        // Explicitly deny permissions to modify the support plan
        Effect = "Deny",
        Action = [
          "support:CreateAccount",
          "support:ModifyDefaultSupportPlan",
          "support:PurchaseSupportPlan"
        ],
        Resource = "*"
      }
    ]
  })
}
