resource "aws_iam_policy" "DataScientist" {
  name        = "DataScientistPolicy"
  description = "Provides access to DBA to process and analyze data"
  policy      = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": [
                "autoscaling:*",
                "athena:*",
                "cloudwatch:*",
                "cloudformation:CreateStack",
                "cloudformation:ListStacks",
                "cloudformation:DescribeStackEvents",
                "datapipeline:Describe*",
                "datapipeline:ListPipelines",
                "datapipeline:GetPipelineDefinition",
                "datapipeline:QueryObjects",
                "dynamodb:*",
                "ec2:CancelSpotInstanceRequests",
                "ec2:CancelSpotFleetRequests",
                "ec2:CreateTags",
                "ec2:DeleteTags",
                "ec2:Describe*",
                "ec2:ModifyImageAttribute",
                "ec2:ModifyInstanceAttribute",
                "ec2:ModifySpotFleetRequest",
                "ec2:RequestSpotInstances",
                "ec2:RequestSpotFleet",
                "elasticfilesystem:*",
                "elasticmapreduce:*",
                "es:*",
                "firehose:*",
                "fsx:DescribeFileSystems",
                "glue:*",
                "iam:GetInstanceProfile",
                "iam:GetRole",
                "iam:GetPolicy",
                "iam:GetPolicyVersion",
                "iam:ListRoles",
                "kinesis:*",
                "kms:*",
                "lambda:Create*",
                "lambda:Delete*",
                "lambda:Get*",
                "lambda:InvokeFunction",
                "lambda:PublishVersion",
                "lambda:Update*",
                "lambda:List*",
                "machinelearning:*",
                "sdb:*",
                "sqlworkbench:*",
                "rds:*",
                "redshift-serverless:List*",
                "tag:*",
                "logs:DescribeLogStreams",
                "logs:GetLogEvents",
                "redshift:*",
                "s3:CreateBucket",
                "sns:CreateTopic",
                "sns:Get*",
                "sns:List*"
            ],
            "Effect": "Allow",
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:Abort*",
                "s3:DeleteObject",
                "s3:Get*",
                "s3:List*",
                "s3:PutAccelerateConfiguration",
                "s3:PutBucketCors",
                "s3:PutBucketLogging",
                "s3:PutBucketNotification",
                "s3:PutBucketTagging",
                "s3:PutObject",
                "s3:Replicate*",
                "s3:RestoreObject"
            ],
            "Resource": [
                "*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "ec2:RunInstances",
                "ec2:TerminateInstances"
            ],
            "Resource": [
                "*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "redshift-data:GetStatementResult",
                "redshift-data:DescribeStatement",
                "redshift-data:ExecuteStatement"
            ],
            "Resource": [
                "*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:PassRole"
            ],
            "Resource": [
                "arn:aws:iam::*:role/DataPipelineDefaultRole",
                "arn:aws:iam::*:role/DataPipelineDefaultResourceRole",
                "arn:aws:iam::*:role/EMR_EC2_DefaultRole",
                "arn:aws:iam::*:role/EMR_DefaultRole",
                "arn:aws:iam::*:role/kinesis-*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "*",
            "Condition": {
                "StringEquals": {
                    "iam:PassedToService": "sagemaker.amazonaws.com"
                }
            }
        },
        {
            "Effect": "Allow",
            "Action": [
                "sagemaker:*"
            ],
            "NotResource": [
                "arn:aws:sagemaker:*:*:domain/*",
                "arn:aws:sagemaker:*:*:user-profile/*",
                "arn:aws:sagemaker:*:*:app/*",
                "arn:aws:sagemaker:*:*:flow-definition/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "sagemaker:CreatePresignedDomainUrl",
                "sagemaker:DescribeDomain",
                "sagemaker:ListDomains",
                "sagemaker:DescribeUserProfile",
                "sagemaker:ListUserProfiles",
                "sagemaker:*App",
                "sagemaker:ListApps"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "sagemaker:*FlowDefinition",
                "sagemaker:*FlowDefinitions"
            ],
            "Resource": "*",
            "Condition": {
                "StringEqualsIfExists": {
                    "sagemaker:WorkteamType": [
                        "private-crowd",
                        "vendor-crowd"
                    ]
                }
            }
        },
        {
            "Effect": "Allow",
            "Action": [
                "quicksight:*"
            ],
            "Resource": [
                "*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "iot:Connect",
                "iot:Subscribe",
                "iot:Receive"
            ],
            "Resource": [
                "*"
            ]
        }
    ]
}
  EOF
}
