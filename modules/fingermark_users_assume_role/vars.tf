variable "roles" {
  type = list(string)
  default = [
    "AdminAccess"
  ]
}

variable "trusted_aws_account_id" {
  default = "************"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "cloudcraft_access" {
  description = "It creates an IAM role to grant access from Cloudcraft (https://app.cloudcraft.co/)"
  type        = bool
  default     = false
}

variable "fm_assume_role_session_duration" {
  type    = number
  default = 43200
}

variable "enhanced_readonly_security_access" {
  description = "Enhances ReadOnlyAccess role with SecurityAudit and Control Tower read access"
  type        = bool
  default     = true
}
