resource "aws_iam_policy" "PowerAccessPolicy" {
  name        = "PowerAccessPolicy"
  description = "Provides full to AWS except for IAM"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "NotAction" : [
            "iam:*",
            "organizations:*",
            "account:*"
          ],
          "Resource" : "*"
        },
        {
          "Action" : [
            "iam:Get*",
            "iam:List*",
            "iam:Tag*",
            "iam:PutRolePolicy",
            "iam:PassRole",
            "organizations:DescribeOrganization",
            "account:ListRegions"
          ],
          "Effect" : "Allow",
          "Resource" : "*"
        },
        {
          "Action" : [
            "iam:Create*",
            "iam:Delete*",
            "iam:Untag*",
            "iam:AttachRolePolicy",
            "iam:DetachRolePolicy",
            "iam:PutRolePolicy",
            "iam:UpdateAssumeRolePolicy"
          ],
          "Condition" : {
            "StringEquals" : {
              "aws:resourcetag/environment" : "Development"
            },
            "StringLike" : {
              "aws:resourcetag/squad" : [
                "*"
              ]
            }
          },
          "Effect" : "Allow",
          "Resource" : "*"
        },
        {
          "Action" : [
            "iam:Delete*",
            "iam:Untag*"
          ],
          "Condition" : {
            "StringEquals" : {
              "aws:resourcetag/environment" : "Production"
            }
          },
          "Effect" : "Deny",
          "Resource" : "*"
        }
      ],
      "Version" : "2012-10-17"

  })
}