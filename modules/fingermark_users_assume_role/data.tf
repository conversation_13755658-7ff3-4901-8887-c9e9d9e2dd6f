data "aws_caller_identity" "current" {}

data "aws_iam_policy_document" "instance_assume_role_deployer_policy" {
  statement {
    sid = "1"
    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }
  statement {
    sid = "2"
    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    condition {
      test     = "StringLike"
      variable = "api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc:sub"
      values   = ["*"]
    }
    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc"]
    }

  }
}

data "aws_iam_policy_document" "instance_assume_role_policy" {
  statement {
    sid = "1"

    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }
}

data "aws_iam_policy_document" "instance_assume_role_policy_admin_access" {
  statement {
    sid = "1"

    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }

  statement {
    sid = "2"

    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/kubeiam-atlantis"]
    }
  }
}

data "aws_iam_policy_document" "instance_assume_role_policy_kommisjon_access" {
  statement {
    sid = "1"

    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }

  # This statement allows the KommisjonAccess role to be assumed by the Eyecue Helm Parser Lambda roles
  # https://bitbucket.org/fingermarkltd/eyecue-helm-parser/src/master/
  statement {
    sid = "2"

    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:PrincipalOrgID"
      values   = ["o-aydhjv9alg"]
    }

    condition {
      test     = "ForAnyValue:StringLike"
      variable = "aws:PrincipalOrgPaths"
      values = [
        "o-aydhjv9alg/r-dfjw/ou-dfjw-szfmig20/ou-dfjw-uc0q21po/*" # Eyecue OU
      ]
    }

    # only allow the role to be assumed by the Eyecue Helm Parser Lambda roles
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::*:role/eyecue-helm-parser-*-lambdaRole"]
    }
  }
}
