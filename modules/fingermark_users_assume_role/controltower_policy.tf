resource "aws_iam_policy" "control_tower_readonly_policy" {
  name        = "ControlTowerReadOnlyPolicy"
  description = "Policy to allow read-only access to AWS Control Tower resources"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "controltower:Get*",
          "controltower:List*",
          "controltower:Describe*"
        ]
        Resource = "*"
      }
    ]
  })
}
