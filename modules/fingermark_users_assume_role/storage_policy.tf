resource "aws_iam_policy" "StorageAccessPolicy" {
  name        = "StorageAccessPolicy"
  description = "Provides S3 read/put access"
  policy      = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:ListBucket",
                "s3:ListAllMyBuckets",
                "s3:PutObject"

            ],
            "Resource": "*"
        }
    ]
}
  EOF
}
