resource "aws_iam_policy" "FinanceAccessPolicy" {
  name        = "FinanceAccessPolicy"
  description = "Provides billing access AWS"
  policy      = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
        "Sid": "ThesePermissionsWillHaveNoEffectTillEndOfMigration",
        "Effect": "Allow",
        "Action": [
            "ce:Get*",
            "ce:Describe*",
            "ce:List*",
            "account:GetAccountInformation",
            "billing:Get*",
            "billing:Put*",
            "payments:List*",
            "payments:Get*",
            "payments:Put*",
            "tax:List*",
            "tax:Get*",
            "consolidatedbilling:Get*",
            "consolidatedbilling:List*",
            "invoicing:List*",
            "invoicing:Get*",
            "cur:Get*",
            "cur:Validate*",
            "freetier:Get*"
        ],
        "Resource": "*"
    }
  ]
}
EOF
}
