resource "aws_iam_policy" "security_access_policy" {
  name        = "SecurityAccessPolicy"
  description = "Policy to allow access to AWS Artifact reports"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "artifact:DownloadReport",
          "artifact:Get*",
          "artifact:List*"
        ]
        Resource = "*"
      }
    ]
  })
}