resource "aws_iam_policy" "quicksight_admin" {
  name        = "QuicksightAdminAccessPolicy"
  description = "Provides full access to AWS Quicksight"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Sid" : "Stmt1681273652409",
          "Effect" : "Allow",
          "Action" : [
            "quicksight:*"
          ],
          "Resource" : "*"
        },
        {
          "Sid" : "Stmt1681273652410",
          "Effect" : "Allow",
          "Action" : [
            "s3:ListBucket",
            "s3:GetObject"
          ],
          "Resource" : "*"
        },
      ]
    }
  )
}