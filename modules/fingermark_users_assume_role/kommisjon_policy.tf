resource "aws_iam_policy" "KommisjonAccess" {
  name        = "KommisjonAccessPolicy"
  description = "Provides access to DBA to process and analyze data"
  policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Sid : "EC2",
        Effect : "Allow",
        Action : [
          "ec2:CreateTags",
          "ec2:DeleteTags",
          "ec2:DescribeInstances",
          "ec2:DescribeTags"
        ],
        Resource : "*"
      },
      {
        Effect : "Allow",
        Action : [
          "ssm:PutParameter",
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:DescribeParameters",
          "ssm:GetParameterHistory",
          "ssm:GetParametersByPath",
          "ssm:DescribeDocumentParameters",
          "ssm:DescribeInstancePatchStatesForPatchGroup",
          "ssm:DescribeSessions",
          "ssm:ResumeSession",
          "ssm:StartSession",
          "ssm:GetDocument",
          "ssm:SendCommand",
          "ssm:TerminateSession",
          "ssm:DescribeInstancePatchStatesForPatchGroup",
          "ssm:DescribeInstancePatchStatesForPatchGroup",
          "ssm:DescribeInstancePatchStatesForPatchGroup",
          "ssm:AddTagsToResource",
          "ssm:DescribeInstanceAssociationsStatus",
          "ssm:DescribeInstancePatches",
          "ssm:DescribeInstancePatchStates",
          "ssm:DescribeInstanceProperties",
          "ssm:DescribeInstanceInformation",
          "ssm:GetCommandInvocation",
          "ssm:GetServiceSetting",
          "ssm:GetInventorySchema",
          "ssm:ListComplianceItems",
          "ssm:ListInventoryEntries",
          "ssm:ListTagsForResource",
          "ssm:ListCommandInvocations",
          "ssm:ListAssociations",
          "ssm:RemoveTagsFromResource"
        ],
        Resource : ["*"]
      }
    ]
  })
}

resource "aws_iam_policy" "KommisjonPortForwardAccess" {
  name        = "KommisjonPortForwardAccessPolicy"
  description = "Policy for Kommisjon Access Port Forwarding"
  policy = jsonencode(
    {
      Version : "2012-10-17",
      Statement : [
        {
          # Required for fm-scripts to lookup instance information
          Effect = "Allow",
          Action = [
            "ssm:DescribeInstanceInformation",
          ],
          Resource = "*",
        },
        {
          Effect : "Allow",
          Action : [
            "ssm:StartSession",
            "ssm:DescribeSessions",
            "ssm:TerminateSession",
          ],
          Resource : [
            "arn:aws:ssm:*:*:document/AWS-StartPortForwardingSession",
            "arn:aws:ssm:*:*:document/AWS-StartPortForwardingSessionToRemoteHost",
            "arn:aws:ssm:*:*:managed-instance/*",
          ]
        },
      ]
    }
  )
}
