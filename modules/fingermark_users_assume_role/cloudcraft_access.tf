resource "aws_iam_role" "cloudcraft_access" {
  count                = var.cloudcraft_access ? 1 : 0
  name                 = "CloudcraftAccess"
  path                 = "/"
  max_session_duration = 3600
  assume_role_policy   = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::968898580625:root"
      },
      "Action": "sts:AssumeRole",
      "Condition": {"StringEquals": {"sts:ExternalId": "51771392-8981-4113-8019-b75b83e3aa4d"}}
    }
  ]
}
POLICY
  tags                 = var.tags
}

resource "aws_iam_role_policy_attachment" "cloudcraft_access_readonly" {
  count      = var.cloudcraft_access ? 1 : 0
  role       = aws_iam_role.cloudcraft_access[0].name
  policy_arn = "arn:aws:iam::aws:policy/ReadOnlyAccess"
}
