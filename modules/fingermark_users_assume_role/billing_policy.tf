resource "aws_iam_policy" "reading_access" {
  name        = "BillingAccessPolicy"
  description = "Provides extra reading access that is not covered by ReadOnlyAccess"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "billing:ListBillingViews",
            "billing:GetBillingData",
            "billing:GetBillingDetails",
            "billing:GetBillingNotifications",
            "billing:GetBillingPreferences",
            "billing:GetContractInformation",
            "billing:GetCredits",
            "ce:*",
            "iot:Connect",
            "iot:Receive",
            "iot:Subscribe"
          ],
          "Resource" : "*"
        }
      ]
    }
  )
}
