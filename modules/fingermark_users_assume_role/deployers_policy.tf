resource "aws_iam_policy" "DeployerAccessPolicy" {
  name        = "DeployerAccessPolicy"
  description = "It provides FULL access to Lambda, IAM, S3, Cloudfront, API Gateway, IoT Events, WAF, Xray, Route53 and Cloud Formation"
  policy      = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "DeployersPolicy",
            "Effect": "Allow",
            "Action": [
                "apigateway:*",
                "acm:ListCertificates",
                "chatbot:CreateSlackChannelConfiguration",
                "chatbot:TagResource",
                "chatbot:UpdateSlackChannelConfiguration",
                "cloudformation:DescribeChangeSet",
                "cloudformation:DescribeStackResources",
                "cloudformation:DescribeStacks",
                "cloudformation:GetTemplate",
                "cloudformation:ListStackResources",
                "cloudformation:*",

                "cloudfront:*",

                "cloudwatch:*",

                "cognito-identity:ListIdentityPools",
                "cognito-sync:GetCognitoEvents",
                "cognito-sync:SetCognitoEvents",

                "ds:CreateComputer",
                "ds:DescribeDirectories",

                "dynamodb:*",

                "backup:*",
                "backup-storage:*",

                "ec2:Describe*",

                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:GetRepositoryPolicy",
                "ecr:DescribeRepositories",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:BatchGetImage",
                "ecr:GetLifecyclePolicy",
                "ecr:GetLifecyclePolicyPreview",
                "ecr:ListTagsForResource",
                "ecr:DescribeImageScanFindings",
                "ecr:CompleteLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:InitiateLayerUpload",
                "ecr:PutImage",
                "ecr:CreateRepository",
                "ecr:PutLifecyclePolicy",
                "ecr:SetRepositoryPolicy",
                "ecr:TagResource",

                "elasticloadbalancing:SetWebACL",
                "elasticloadbalancing:DescribeLoadBalancers",

                "elasticbeanstalk:DescribeEnvironments",

                "events:*",

                "iam:CreateRole",
                "iam:DeleteRole",
                "iam:GetPolicy",
                "iam:GetPolicyVersion",
                "iam:GetRole",
                "iam:GetRolePolicy",
                "iam:ListAttachedRolePolicies",
                "iam:ListRolePolicies",
                "iam:ListRoles",
                "iam:PassRole",
                "iam:TagRole",
                "iam:UntagRole",
                "iam:ListServerCertificates",
                "iam:ListRoles",
                "iam:DeleteServiceLinkedRole",
                "iam:GetServiceLinkedRoleDeletionStatus",
                "iam:CreateServiceLinkedRole",
                "iam:PutRolePolicy",
                "iam:DeleteRolePolicy",
                "iam:AttachRolePolicy",
                "iam:UpdateAssumeRolePolicy",

                "iot:AttachPrincipalPolicy",
                "iot:AttachThingPrincipal",
                "iot:CreateKeysAndCertificate",
                "iot:CreatePolicy",
                "iot:CreateThing",
                "iot:CreateTopicRule",
                "iot:CreateTopicRuleDestination",
                "iot:DescribeEndpoint",
                "iot:GetTopicRule",
                "iot:ListPolicies",
                "iot:ListThings",
                "iot:ListTopicRules",
                "iot:ReplaceTopicRule",
                "iot:ListTagsForResource",
                "iot:Publish",
                "iot:TagResource",
                "iot:UntagResource",
                "iot:CreateTopicRuleDestination",
                "iot:UpdateTopicRuleDestination",
                "iot:EnableTopicRule",
                "iot:ReplaceTopicRule",
                "iot:UpdateAuthorizer",
                "iot:CreateAuthorizer",
                "iot:DeleteAuthorizer",
                "iot:ListAuthorizers",

                "kinesis:DescribeStream",
                "kinesis:DescribeStreamSummary",
                "kinesis:AddTagsToStream",
                "kinesis:CreateStream",
                "kinesis:ListStreams",
                "kinesis:PutRecord",
                "kinesis:ListStreams",
                "kinesis:IncreaseStreamRetentionPeriod",

                "kms:CreateGrant",
                "kms:DescribeKey",
                "kms:Decrypt",
                "kms:Encrypt",
                "kms:GenerateDataKey",
                "kms:ListAliases",

                "lambda:*",
                "logs:CreateLogDelivery",
                "logs:UpdateLogDelivery",
                "logs:GetLogDelivery",
                "logs:CreateLogGroup",
                "logs:DeleteLogGroup",
                "logs:Put*",
                "logs:Describe*",
                "logs:Get*",
                "logs:ListTagsLogGroup",
                "logs:ListTagsForResource",
                "logs:ListLogDeliveries",
                "logs:TagLogGroup",
                "logs:TagResource",
                "logs:UntagResource",
                "logs:DeleteRetentionPolicy",
                "rds:DescribeDBClusterParameterGroups",
                "rds:DescribeDBClusterParameters",
                "rds:DescribeDBClusterSnapshots",
                "rds:DescribeDBClusters",
                "rds:DescribeDBInstances",
                "rds:DescribeDBParameterGroups",
                "rds:DescribeDBParameters",
                "rds:DescribeDBSnapshots",
                "rds:DescribeGlobalClusters",

                "redshift:DescribeClusters",

                "s3:*",

                "secretsmanager:ListSecrets",
                "secretsmanager:CreateSecret",
                "secretsmanager:DescribeSecret",
                "secretsmanager:GetSecretValue",
                "secretsmanager:PutSecretValue",
                "secretsmanager:TagResource",

                "sns:CreateTopic",
                "sns:GetTopicAttributes",
                "sns:ListSubscriptions",
                "sns:ListSubscriptionsByTopic",
                "sns:ListTopics",
                "sns:Publish",
                "sns:Subscribe",
                "sns:Unsubscribe",
                "sns:ListTopics",
                "sns:ListSubscriptionsByTopic",
                "sns:TagResource",

                "ssm:GetParameter",
                "ssm:DescribeParameters",
                "ssm:PutParameter",
                "ssm:GetParameters",

                "ssmmessages:CreateControlChannel",
                "ssmmessages:CreateDataChannel",
                "ssmmessages:OpenControlChannel",
                "ssmmessages:OpenDataChannel",

                "sqs:GetQueueAttributes",
                "sqs:ListQueues",
                "sqs:SendMessage",
                "sqs:CreateQueue",
                "sqs:SetQueueAttributes",
                "sqs:TagQueue",

                "tag:GetResources",

                "waf:ListWebACLs",
                "waf:GetWebACL",

                "wafv2:ListWebACLs",
                "wafv2:GetWebACL",

                "xray:PutTelemetryRecords",
                "xray:PutTraceSegments"
            ],
            "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": "iam:CreateServiceLinkedRole",
                "Resource": "*",
                "Condition" : {"StringEquals": { "iam:AWSServiceName": "apigateway.amazonaws.com" }}
            },
            {
                "Effect": "Allow",
                "Action": "iam:CreateServiceLinkedRole",
                "Resource": "*",
                "Condition" :{"StringEquals": { "iam:AWSServiceName": "ops.apigateway.amazonaws.com" }}
            }
    ]
}
EOF
}
