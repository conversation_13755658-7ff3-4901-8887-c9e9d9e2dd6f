variable "domain_name" {
  description = "The domain for which to issue the certificate (e.g. dev-platform.monitoring.fingermark.tech)."
  type        = string
}

variable "subject_alternative_names" {
  description = "Any optional alternative domain names."
  type        = list(string)
  default     = []
}

variable "cloudflare_zone_id" {
  description = "The Cloudflare zone ID where the domain is hosted."
  type        = string
}

variable "cloudflare_record_ttl" {
  description = "TTL for the Cloudflare DNS records used in certificate validation."
  type        = number
  default     = 300
}

variable "tags" {
  description = "A map of tags to apply to the ACM certificate."
  type        = map(string)
  default     = {}
}

variable "cloudflare_api_email" {
  type    = string
  default = "<EMAIL>"
}

variable "cloudflare_api_key" {
  type    = string
  default = "c1498c0176647d89cbcc55f1b51b6cce77678"
}
