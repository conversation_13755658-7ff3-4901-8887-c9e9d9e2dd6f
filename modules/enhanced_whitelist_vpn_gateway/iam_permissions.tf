# IAM policy for S3 access (keys and client configs)
resource "aws_iam_policy" "vpn_s3_policy" {
  name        = "${local.base_name}-vpn-s3-policy"
  description = "Policy for VPN instances to access S3 for key storage and client configs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ],
        Effect = "Allow"
        Resource = [
          "arn:aws:s3:::${var.key_s3_bucket}",
          "arn:aws:s3:::${var.key_s3_bucket}/*"
        ]
      },
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ],
        Effect = "Allow"
        Resource = [
          "arn:aws:s3:::${var.client_config_s3_bucket}",
          "arn:aws:s3:::${var.client_config_s3_bucket}/*"
        ]
      }
    ]
  })
}

# IAM policy for ELB access (to get NLB DNS name)
resource "aws_iam_policy" "vpn_elb_policy" {
  name        = "${local.base_name}-vpn-elb-policy"
  description = "Policy for VPN instances to access ELB information for client config generation"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "elasticloadbalancing:DescribeLoadBalancers",
          "elasticloadbalancing:DescribeTargetGroups",
          "elasticloadbalancing:DescribeTargetHealth"
        ],
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Attach S3 policy to VPN instance role
resource "aws_iam_role_policy_attachment" "vpn_s3_attachment" {
  role       = aws_iam_role.vpn_instance_role.name
  policy_arn = aws_iam_policy.vpn_s3_policy.arn
}

# Attach ELB policy to VPN instance role
resource "aws_iam_role_policy_attachment" "vpn_elb_attachment" {
  role       = aws_iam_role.vpn_instance_role.name
  policy_arn = aws_iam_policy.vpn_elb_policy.arn
}
