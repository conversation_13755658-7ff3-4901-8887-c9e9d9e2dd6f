# S3 bucket for OpenVPN key materials (conditional)
resource "aws_s3_bucket" "vpn_keys" {
  count = var.create_key_bucket ? 1 : 0

  bucket = var.key_s3_bucket

  tags = merge(var.tags, {
    Name = "${local.base_name}-vpn-keys"
  })
}

# Block public access to key bucket
resource "aws_s3_bucket_public_access_block" "vpn_keys" {
  count = var.create_key_bucket ? 1 : 0

  bucket                  = aws_s3_bucket.vpn_keys[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable encryption for key bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "vpn_keys" {
  count = var.create_key_bucket ? 1 : 0

  bucket = aws_s3_bucket.vpn_keys[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 bucket for client configurations (conditional)
resource "aws_s3_bucket" "client_configs" {
  count = var.create_client_config_bucket ? 1 : 0

  bucket = var.client_config_s3_bucket

  tags = merge(var.tags, {
    Name = "${local.base_name}-client-configs"
  })
}

# Block public access to client config bucket
resource "aws_s3_bucket_public_access_block" "client_configs" {
  count = var.create_client_config_bucket ? 1 : 0

  bucket                  = aws_s3_bucket.client_configs[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable encryption for client config bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "client_configs" {
  count = var.create_client_config_bucket ? 1 : 0

  bucket = aws_s3_bucket.client_configs[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Cross-account bucket policy for Eyecue servers to access S3 bucket on infra accounts
resource "aws_s3_bucket_policy" "client_configs_cross_account" {
  count = var.create_client_config_bucket && length(var.on_premise_account_ids) > 0 ? 1 : 0

  bucket = aws_s3_bucket.client_configs[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowCrossAccountVPNConfigAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            for account_id in var.on_premise_account_ids :
            "arn:aws:iam::${account_id}:role/AmazonEC2RunCommandRoleForManagedInstances"
          ]
        }
        Action = [
          "s3:GetObject"
        ]
        Resource = "${aws_s3_bucket.client_configs[0].arn}/clients/*.ovpn"
      },
      {
        Sid    = "AllowCrossAccountListBucket"
        Effect = "Allow"
        Principal = {
          AWS = [
            for account_id in var.on_premise_account_ids :
            "arn:aws:iam::${account_id}:role/AmazonEC2RunCommandRoleForManagedInstances"
          ]
        }
        Action = [
          "s3:ListBucket"
        ]
        Resource = aws_s3_bucket.client_configs[0].arn
        Condition = {
          StringLike = {
            "s3:prefix" = "clients/*"
          }
        }
      }
    ]
  })
}
