# OpenVPN Server Testing Guide

Simple steps to verify your OpenVPN server is configured correctly.

## Quick Validation Checklist

### 1. OpenVPN Service Status ✅
Connect to your VPN instance via SSM and check:
```bash
# Check OpenVPN is running
sudo systemctl status openvpn@server

# Verify OpenVPN is listening on correct port
sudo netstat -ulnp | grep 1194

# Check OpenVPN logs for errors
sudo journalctl -u openvpn@server -f
```

**Expected:** Service active, port 1194 listening, no error logs

### 2. Client Configuration Available ✅
```bash
# Check client configs are generated in S3
aws s3 ls s3://your-client-config-bucket/

# Download a test config
aws s3 cp s3://your-client-config-bucket/client-config.ovpn ./test-client.ovpn
```

**Expected:** `.ovpn` files present in S3 bucket

## Connection Testing

### 3. Test VPN Connection ✅
On a test client machine:
```bash
# Install OpenVPN client
sudo apt update && sudo apt install -y openvpn

# Test connection with downloaded config
sudo openvpn --config test-client.ovpn --daemon

# Verify VPN interface is up
ip addr show tun0

# Check you're routing through VPN
curl ifconfig.me  # Should show VPN server's public IP
```

**Expected:** `tun0` interface exists, external IP matches VPN server

### 4. Internet Connectivity ✅
```bash
# Test basic connectivity through VPN
ping -c 3 *******
curl -I https://www.google.com

# Verify DNS resolution
nslookup google.com
```

**Expected:** All tests succeed, traffic flows through VPN

## Troubleshooting

### Connection Issues
- **Can't connect:** Check security groups allow UDP 1194
- **No internet:** Verify NAT Gateway and route tables
- **DNS issues:** Check `/etc/resolv.conf` on VPN server

### Quick Diagnostics
```bash
# On VPN server - check active connections
sudo cat /var/log/openvpn/status.log

# On client - test with verbose logging
sudo openvpn --config test-client.ovpn --verb 4
```

### Health Check Endpoint
```bash
# Test the health check endpoint (if configured)
curl -k https://your-vpn-server:443/health
```

**Expected:** Returns HTTP 200 OK

---

**✅ Success:** Your OpenVPN server is correctly configured when all tests pass and clients can connect with internet access through the VPN tunnel.
