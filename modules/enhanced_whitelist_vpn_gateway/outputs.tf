# --- Networking Outputs ---
output "vpc_id" {
  description = "VPC ID where the VPN gateway is deployed"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the dedicated VPC."
  value       = aws_vpc.main.cidr_block
}

output "public_subnet_ids" {
  description = "List of public subnet IDs."
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "List of private subnet IDs."
  value       = aws_subnet.private[*].id
}

output "private_route_table_ids" {
  description = "List of private route table IDs (one per AZ)."
  value       = aws_route_table.private[*].id
}

output "nat_gateway_public_ips" {
  description = "List of public IPs for the NAT Gateways (one per AZ, if enabled)."
  value       = var.enable_nat_gateway ? aws_eip.nat[*].public_ip : []
}

# --- VPN Instance Outputs ---
output "vpn_gateway_instance_id" {
  description = "ID of the VPN Gateway EC2 instance (only if not using Auto Scaling and enable_single_instance_vpn is true)."
  value       = var.enable_auto_scaling == false && var.enable_single_instance_vpn == true && length(aws_instance.single_vpn_instance) > 0 ? aws_instance.single_vpn_instance[0].id : null
}

output "vpn_gateway_instance_private_ip" {
  description = "Private IP of the VPN Gateway EC2 instance (only if not using Auto Scaling and enable_single_instance_vpn is true)."
  value       = var.enable_auto_scaling == false && var.enable_single_instance_vpn == true && length(aws_instance.single_vpn_instance) > 0 ? aws_instance.single_vpn_instance[0].private_ip : null
}

output "vpn_gateway_public_ip" {
  description = "Public IP of the single VPN Gateway EC2 instance (only if not using Auto Scaling and enable_single_instance_vpn is true)."
  value       = var.enable_auto_scaling == false && var.enable_single_instance_vpn == true && length(aws_eip.single_vpn_instance_eip) > 0 ? aws_eip.single_vpn_instance_eip[0].public_ip : null
}

output "vpn_gateway_security_group_id" {
  description = "ID of the VPN Gateway security group."
  value       = aws_security_group.vpn_gateway.id
}

# --- Auto Scaling Group Outputs ---
output "autoscaling_group_name" {
  description = "Name of the Auto Scaling Group (if enabled)"
  value       = var.enable_auto_scaling ? aws_autoscaling_group.vpn_asg[0].name : null
}

output "autoscaling_group_arn" {
  description = "ARN of the Auto Scaling Group."
  value       = var.enable_auto_scaling ? aws_autoscaling_group.vpn_asg[0].arn : null
}

output "launch_template_id" {
  description = "ID of the Launch Template (if Auto Scaling is enabled)."
  value       = var.enable_auto_scaling && length(aws_launch_template.vpn_launch_template) > 0 ? aws_launch_template.vpn_launch_template.id : null
}

output "launch_template_latest_version" {
  description = "Latest version of the Launch Template (if Auto Scaling is enabled)."
  value       = var.enable_auto_scaling && length(aws_launch_template.vpn_launch_template) > 0 ? aws_launch_template.vpn_launch_template.latest_version : null
}

# --- Load Balancer Outputs ---
output "load_balancer_dns_name" {
  description = "DNS name of the Network Load Balancer (if enabled)"
  value       = var.enable_load_balancer ? aws_lb.vpn_nlb[0].dns_name : null
}

output "load_balancer_arn" {
  description = "ARN of the Network Load Balancer (if enabled)"
  value       = var.enable_load_balancer ? aws_lb.vpn_nlb[0].arn : null
}

output "load_balancer_zone_id" {
  description = "Zone ID of the Network Load Balancer for Route 53."
  value       = var.enable_load_balancer ? aws_lb.vpn_nlb[0].zone_id : null
}

output "target_group_arn" {
  description = "ARN of the target group (if enabled)"
  value       = var.enable_load_balancer ? aws_lb_target_group.vpn_target_group[0].arn : null
}

# --- IAM Outputs ---
output "instance_role_name" {
  description = "Name of the IAM role for VPN instances."
  value       = aws_iam_role.vpn_instance_role.name
}

output "instance_profile_name" {
  description = "Name of the IAM instance profile for VPN instances."
  value       = aws_iam_instance_profile.vpn_instance_profile.name
}

output "vpn_instance_role_name" {
  description = "Name of the IAM role attached to the VPN instance"
  value       = aws_iam_role.vpn_instance_role.name
}

# --- CloudWatch Outputs ---
output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for VPN logs."
  value       = var.enable_enhanced_monitoring ? aws_cloudwatch_log_group.vpn_logs[0].name : null
}

output "alarm_sns_topic_arn" {
  description = "ARN of the SNS topic for CloudWatch alarms."
  value       = var.create_cloudwatch_alarms && var.alarm_notification_email != "" ? aws_sns_topic.vpn_alarms[0].arn : null
}

output "security_group_id" {
  description = "Security group ID attached to the VPN gateway instance(s)"
  value       = aws_security_group.vpn_gateway.id
}

output "vpn_instance_id" {
  description = "EC2 instance ID of the VPN gateway (deprecated, use vpn_gateway_instance_id for single instance or refer to ASG for multiple instances)"
  value       = var.enable_auto_scaling == false && var.enable_single_instance_vpn == true && length(aws_instance.single_vpn_instance) > 0 ? aws_instance.single_vpn_instance[0].id : "N/A (ASG Enabled or Single Instance Disabled)" # Updated to reflect new logic
}

output "vpn_client_cidr_block" {
  description = "CIDR block used for VPN client IP addresses"
  value       = var.vpn_client_cidr_block
}

output "vpn_public_ip" {
  description = "Public IP address of the VPN gateway instance (deprecated, use vpn_gateway_public_ip for single instance or NLB DNS for ASG)"
  value       = var.enable_auto_scaling == false && var.enable_single_instance_vpn == true && length(aws_eip.single_vpn_instance_eip) > 0 ? aws_eip.single_vpn_instance_eip[0].public_ip : "N/A (ASG Enabled or Single Instance Disabled)" # Updated
}

output "vpn_private_ip" {
  description = "Private IP address of the VPN gateway instance (deprecated, use vpn_gateway_instance_private_ip for single instance)"
  value       = var.enable_auto_scaling == false && var.enable_single_instance_vpn == true && length(aws_instance.single_vpn_instance) > 0 ? aws_instance.single_vpn_instance[0].private_ip : "N/A (ASG Enabled or Single Instance Disabled)" # Updated
}

# --- OpenVPN Community Edition Outputs ---
output "key_s3_bucket" {
  description = "S3 bucket for OpenVPN key material"
  value       = var.key_s3_bucket
}

output "client_config_s3_bucket" {
  description = "S3 bucket for client configurations"
  value       = var.client_config_s3_bucket
}

output "config_s3_bucket_name" {
  description = "Name of the S3 bucket where client .ovpn configuration files are stored."
  value       = var.client_config_s3_bucket
}

output "config_s3_bucket_arn" {
  description = "ARN of the S3 bucket for client configuration files."
  value       = var.create_client_config_bucket ? aws_s3_bucket.client_configs[0].arn : "arn:aws:s3:::${var.client_config_s3_bucket}"
}
