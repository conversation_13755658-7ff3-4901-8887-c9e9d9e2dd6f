resource "aws_sns_topic" "topics" {
  for_each = var.sns_topics

  name         = each.value.topic_name
  display_name = each.value.display_name
  tags         = each.value.tags
}

resource "aws_sns_topic_subscription" "subscriptions" {
  for_each = {
    for topic_key, topic_value in var.sns_topics :
    topic_key => topic_value
    if topic_value.subscriptions != null
  }

  topic_arn = aws_sns_topic.topics[each.key].arn
  protocol  = each.value.subscriptions.protocol
  endpoint  = each.value.subscriptions.endpoint
}
