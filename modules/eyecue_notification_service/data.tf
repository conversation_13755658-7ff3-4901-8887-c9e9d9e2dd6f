data "aws_iam_policy_document" "eyecue_notification_service_assume_role_policy_prod" {
  statement {
    sid = "1"
    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::613615422941:role/eyecue-notification-service-prod-ap-southeast-2-lambdaRole"]
    }
  }
}

data "aws_iam_policy_document" "eyecue_notification_service_assume_role_policy_dev_qa" {
  statement {
    sid = "1"
    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::028579512773:role/eyecue-notification-service-dev-ap-southeast-2-lambdaRole"]
    }
  }
}

data "aws_iam_policy_document" "eyecue_notification_service_assume_role_policy_stg" {
  statement {
    sid = "1"
    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::949131794549:role/eyecue-notification-service-stg-ap-southeast-2-lambdaRole"]
    }
  }
}