variable "aws_region" {
  description = "AWS Region of The iot Topic the user will be created in"
}

variable "aws_account_id" {
  description = "The AWS Account ID of the user"
}

variable "environment" {
  description = "Which environment the user is being created in"
}

variable "tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}