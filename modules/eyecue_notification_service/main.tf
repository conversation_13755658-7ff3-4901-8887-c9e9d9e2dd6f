
locals {
  role_policies = {
    "prod"    = data.aws_iam_policy_document.eyecue_notification_service_assume_role_policy_prod.json
    "stg"     = data.aws_iam_policy_document.eyecue_notification_service_assume_role_policy_stg.json
    "default" = data.aws_iam_policy_document.eyecue_notification_service_assume_role_policy_dev_qa.json
  }
}
resource "aws_iam_role" "eyecue_notification_service_role" {
  name               = "eyecue_notification_service_role"
  assume_role_policy = coalesce(lookup(local.role_policies, var.environment, null), local.role_policies["default"])
  tags               = var.tags
}

resource "aws_iam_policy" "eyecue_notification_service_policy" {
  name        = "eyecue_notification_service_policy"
  description = "Policy for Eyecue Notification Service to publish to IoT topics"
  tags        = var.tags
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "iot:Publish",
        ],
        "Resource" : "arn:aws:iot:${var.aws_region}:${var.aws_account_id}:topic/*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eyecue_notification_service_policy_attachment" {
  role       = aws_iam_role.eyecue_notification_service_role.name
  policy_arn = aws_iam_policy.eyecue_notification_service_policy.arn
}