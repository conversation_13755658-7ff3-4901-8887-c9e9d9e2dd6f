variable "default_tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
    Stack       = "Monitoring"
    Application = "Icinga2"
  }
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "icinga2_satellite_customer_id" {
  type    = string
  default = ""
}


variable "icinga2_satellite_api_tcp_port" {
  description = "The Icinga2 API port. Default 5665"
  type        = number
  default     = 5665
}

variable "icinga2_satellite_vpc_id" {
  description = "The VPC ID where the satellite is deployed"
  type        = string
}

variable "icinga2_satellite_security_group_name" {
  type    = string
  default = "Icinga2SatelliteSecGroup"
}

variable "icinga2_satellite_ec2_ami_id" {
  description = "The AMI ID, correspoding to the region"
  type        = string
  default     = null
}

variable "icinga2_satellite_ec2_name" {
  description = "The EC2 instance name"
  type        = string
  default     = "icinga2-satellite"
}

variable "icinga2_satellite_ec2_subnet_id" {
  type = string
}

variable "icinga2_satellite_ec2_extra_security_group_ids" {
  type    = list(any)
  default = []
}

variable "icinga2_satellite_ec2_instance_type" {
  type    = string
  default = "t3.small"
}

variable "icinga2_satellite_ec2_ssh_key_name" {
  type    = string
  default = "infra-team"
}

variable "icinga2_satellite_cloudflare_api_email" {
  type    = string
  default = "<EMAIL>"
}

variable "icinga2_satellite_cloudflare_api_key" {
  type    = string
  default = ""
}

variable "icinga2_satellite_ebs_optimized" {
  type    = bool
  default = false
}
