module "icinga2_satellite_sec_group" {
  # https://registry.terraform.io/modules/terraform-aws-modules/security-group/aws/latest  
  source      = "terraform-aws-modules/security-group/aws"
  name        = var.icinga2_satellite_security_group_name
  description = "Security group for Icinga2 Satellite Instance"
  vpc_id      = var.icinga2_satellite_vpc_id

  ingress_with_cidr_blocks = [
    {
      from_port   = var.icinga2_satellite_api_tcp_port
      to_port     = var.icinga2_satellite_api_tcp_port
      protocol    = "tcp"
      description = "Icinga2 API"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  tags = merge(var.default_tags, var.tags, { "Name" = var.icinga2_satellite_security_group_name })

}

module "ec2_instance" {
  # When the instance is running, run the Ansible script to install and configure the Icinga2 satellite
  # https://registry.terraform.io/modules/terraform-aws-modules/ec2-instance/aws/latest  
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "~> 2.0"

  name           = var.icinga2_satellite_ec2_name
  instance_count = 1

  ami                    = var.icinga2_satellite_ec2_ami_id != null ? var.icinga2_satellite_ec2_ami_id : data.aws_ami.default.id
  instance_type          = var.icinga2_satellite_ec2_instance_type
  key_name               = var.icinga2_satellite_ec2_ssh_key_name
  monitoring             = true
  vpc_security_group_ids = concat([module.icinga2_satellite_sec_group.security_group_id], var.icinga2_satellite_ec2_extra_security_group_ids)
  subnet_id              = var.icinga2_satellite_ec2_subnet_id
  ebs_optimized          = var.icinga2_satellite_ebs_optimized
  tags                   = merge(var.default_tags, var.tags)
}

moved {
  from = module.icinga_satellite
  to   = module.ec2_instance
}

module "dns_record" {
  source                  = "../cloudflare"
  cloudflare_record_name  = "icinga2-satellite.${var.icinga2_satellite_customer_id}.infra"
  cloudflare_record_value = module.ec2_instance.public_ip[0]
  cloudflare_api_key      = var.icinga2_satellite_cloudflare_api_key
}
