# DynamoDB Monitoring <PERSON><PERSON>le

Automatically discovers DynamoDB tables and creates CloudWatch alarms for capacity monitoring to ensure SOC2 compliance.

## What it does

- Discovers all DynamoDB tables and GSIs in your AWS account
- Creates CloudWatch alarms for read/write capacity consumption
- Only creates alarms if they don't already exist
- Runs on a schedule via EventBridge

## Usage

```hcl
module "dynamodb_monitoring" {
  source = "./modules/dynamodb_monitoring"

  lambda_function_name = "dynamodb-monitoring-remediation"
  sns_topic_arn       = "arn:aws:sns:us-east-1:************:cloudwatch-alarms"

  tags = {
    Environment = "production"
    Compliance  = "SOC2"
  }
}
```
