# ==========================================
# DynamoDB Monitoring Module Variables
# ==========================================

# ==========================================
# Alarm Configuration
# ==========================================

variable "alarm_name_prefix_rcu" {
  description = "Prefix for table RCU CloudWatch alarm names"
  type        = string
  default     = "ddb-dynamic-table-consumed-rcu"
}

variable "alarm_name_prefix_wcu" {
  description = "Prefix for table WCU CloudWatch alarm names"
  type        = string
  default     = "ddb-dynamic-table-consumed-wcu"
}

variable "alarm_name_prefix_gsi_rcu" {
  description = "Prefix for GSI RCU CloudWatch alarm names"
  type        = string
  default     = "ddb-dynamic-gsi-consumed-rcu"
}

variable "alarm_name_prefix_gsi_wcu" {
  description = "Prefix for GSI WCU CloudWatch alarm names"
  type        = string
  default     = "ddb-dynamic-gsi-consumed-wcu"
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 3
}

variable "alarm_period" {
  description = "Period in seconds for the alarm evaluation"
  type        = number
  default     = 60
}

variable "alarm_statistic" {
  description = "Statistic to use for the alarm"
  type        = string
  default     = "Sum"
  validation {
    condition     = contains(["Average", "Maximum", "Minimum", "Sum", "SampleCount"], var.alarm_statistic)
    error_message = "Alarm statistic must be one of: Average, Maximum, Minimum, Sum, SampleCount."
  }
}

variable "alarm_comparison_operator" {
  description = "Comparison operator for the alarm"
  type        = string
  default     = "GreaterThanThreshold"
  validation {
    condition = contains([
      "GreaterThanOrEqualToThreshold",
      "GreaterThanThreshold",
      "LessThanThreshold",
      "LessThanOrEqualToThreshold"
    ], var.alarm_comparison_operator)
    error_message = "Comparison operator must be a valid CloudWatch alarm comparison operator."
  }
}

variable "treat_missing_data" {
  description = "How to treat missing data for CloudWatch alarms"
  type        = string
  default     = "missing"
  validation {
    condition     = contains(["breaching", "notBreaching", "ignore", "missing"], var.treat_missing_data)
    error_message = "treat_missing_data must be one of: breaching, notBreaching, ignore, missing."
  }
}

# ==========================================
# Threshold Configuration
# ==========================================

variable "default_rcu_threshold" {
  description = "Default RCU threshold for on-demand tables (RCU * period)"
  type        = number
  default     = 600
}

variable "default_wcu_threshold" {
  description = "Default WCU threshold for on-demand tables (WCU * period)"
  type        = number
  default     = 300
}

variable "threshold_percentage_rcu" {
  description = "Percentage of provisioned RCU to use as threshold"
  type        = number
  default     = 80
  validation {
    condition     = var.threshold_percentage_rcu > 0 && var.threshold_percentage_rcu <= 100
    error_message = "Threshold percentage must be between 1 and 100."
  }
}

variable "threshold_percentage_wcu" {
  description = "Percentage of provisioned WCU to use as threshold"
  type        = number
  default     = 80
  validation {
    condition     = var.threshold_percentage_wcu > 0 && var.threshold_percentage_wcu <= 100
    error_message = "Threshold percentage must be between 1 and 100."
  }
}

# ==========================================
# Notification Configuration
# ==========================================

variable "sns_topic_arn" {
  description = "SNS topic ARN for alarm notifications"
  type        = string
  default     = null
}

# ==========================================
# Lambda Configuration
# ==========================================

variable "lambda_function_name" {
  description = "Name of the Lambda function"
  type        = string
  default     = "dynamodb-monitoring-remediation"
}

variable "lambda_timeout" {
  description = "Timeout for the Lambda function in seconds"
  type        = number
  default     = 300
}

variable "lambda_memory_size" {
  description = "Memory size for the Lambda function in MB"
  type        = number
  default     = 512
}

variable "enable_lambda_function" {
  description = "Whether to create the Lambda function and related resources"
  type        = bool
  default     = true
}



# ==========================================
# Scheduling Configuration
# ==========================================

variable "schedule_expression" {
  description = "EventBridge schedule expression for triggering the Lambda"
  type        = string
  default     = "cron(0 2 * * ? *)" # Daily at 2 AM UTC
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 365
}

# ==========================================
# Filtering Configuration
# ==========================================

variable "table_name_filter" {
  description = "Optional regex pattern to filter table names (empty string means no filter)"
  type        = string
  default     = ""
}

variable "excluded_table_names" {
  description = "List of table names to exclude from monitoring"
  type        = list(string)
  default     = []
}

# ==========================================
# Tagging Configuration
# ==========================================

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "default_tags" {
  description = "Default tags to apply to resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Module      = "dynamodb_monitoring"
    Purpose     = "SOC2Compliance"
    Environment = "unknown"
  }
}
