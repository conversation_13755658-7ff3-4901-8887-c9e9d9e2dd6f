"""
DynamoDB Monitoring Lambda Function

This Lambda function automatically discovers all DynamoDB tables and GSIs in an AWS account
and creates CloudWatch alarms for ConsumedReadCapacityUnits and ConsumedWriteCapacityUnits
metrics if they don't already exist. This ensures compliance with SOC2 monitoring requirements.

"""

import json
import logging
import os
import re
from typing import Dict, List

import boto3
from botocore.exceptions import ClientError

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
dynamodb_client = boto3.client('dynamodb')
cloudwatch_client = boto3.client('cloudwatch')

def get_env_var(name: str, default: str = "") -> str:
    """Get environment variable with default value."""
    return os.environ.get(name, default)

def get_env_int(name: str, default: int) -> int:
    """Get environment variable as integer with default value."""
    try:
        return int(os.environ.get(name, str(default)))
    except ValueError:
        logger.warning(f"Invalid integer value for {name}, using default: {default}")
        return default

def get_env_float(name: str, default: float) -> float:
    """Get environment variable as float with default value."""
    try:
        return float(os.environ.get(name, str(default)))
    except ValueError:
        logger.warning(f"Invalid float value for {name}, using default: {default}")
        return default

def get_env_list(name: str, default: List[str] = None) -> List[str]:
    """Get environment variable as a list (comma-separated)."""
    value = os.environ.get(name, "")
    if not value:
        return default or []
    return [item.strip() for item in value.split(",") if item.strip()]

def list_all_dynamodb_tables() -> List[Dict]:
    """List all DynamoDB tables in the account with their details."""
    try:
        tables = []
        paginator = dynamodb_client.get_paginator('list_tables')

        for page in paginator.paginate():
            for table_name in page.get('TableNames', []):
                try:
                    # Get table details
                    response = dynamodb_client.describe_table(TableName=table_name)
                    table_info = response['Table']

                    # Extract table details
                    table_data = {
                        'table_name': table_name,
                        'billing_mode': table_info.get('BillingModeSummary', {}).get('BillingMode', 'PROVISIONED'),
                        'read_capacity': table_info.get('ProvisionedThroughput', {}).get('ReadCapacityUnits', 0),
                        'write_capacity': table_info.get('ProvisionedThroughput', {}).get('WriteCapacityUnits', 0),
                        'gsis': []
                    }

                    # Extract GSI information
                    for gsi in table_info.get('GlobalSecondaryIndexes', []):
                        gsi_data = {
                            'index_name': gsi['IndexName'],
                            'read_capacity': gsi.get('ProvisionedThroughput', {}).get('ReadCapacityUnits', 0),
                            'write_capacity': gsi.get('ProvisionedThroughput', {}).get('WriteCapacityUnits', 0)
                        }
                        table_data['gsis'].append(gsi_data)

                    tables.append(table_data)

                except ClientError as e:
                    logger.warning(f"Error describing table {table_name}: {e}")
                    continue

        logger.info(f"Found {len(tables)} DynamoDB tables")
        return tables

    except ClientError as e:
        logger.exception(f"Error listing DynamoDB tables: {e}")
        raise

def should_monitor_table(table_name: str, name_filter: str, excluded_names: List[str]) -> bool:
    """Determine if a table should be monitored based on filters."""
    # Check if table is in exclusion list
    if table_name in excluded_names:
        logger.info(f"Skipping excluded table: {table_name}")
        return False

    # Check name filter if provided
    if name_filter:
        try:
            if not re.match(name_filter, table_name):
                logger.info(f"Table {table_name} doesn't match filter pattern: {name_filter}")
                return False
        except re.error as e:
            logger.warning(f"Invalid regex pattern '{name_filter}': {e}")
            raise ValueError(f"Invalid TABLE_NAME_FILTER regex pattern '{name_filter}': {e}")

    return True

def alarm_exists(alarm_name: str) -> bool:
    """Check if a CloudWatch alarm already exists."""
    try:
        response = cloudwatch_client.describe_alarms(AlarmNames=[alarm_name])
        return len(response['MetricAlarms']) > 0
    except ClientError as e:
        logger.warning(f"Error checking alarm existence for {alarm_name}: {e}")
        return False

def calculate_threshold(capacity_units: int, threshold_percentage: float, period: int, default_threshold: float) -> float:
    """Calculate alarm threshold based on capacity and percentage."""
    if capacity_units > 0:
        # Provisioned capacity: use percentage of capacity * period
        return capacity_units * threshold_percentage / 100 * period
    else:
        # On-demand: use default threshold
        return default_threshold

def create_table_alarm(table_name: str, metric_name: str, alarm_name: str, threshold: float, config: Dict) -> bool:
    """Create CloudWatch alarm for a DynamoDB table."""
    try:
        alarm_params = {
            'AlarmName': alarm_name,
            'ComparisonOperator': config['comparison_operator'],
            'EvaluationPeriods': config['evaluation_periods'],
            'MetricName': metric_name,
            'Namespace': 'AWS/DynamoDB',
            'Period': config['period'],
            'Statistic': config['statistic'],
            'Threshold': threshold,
            'ActionsEnabled': True,
            'AlarmDescription': f'Monitors {metric_name} for DynamoDB table {table_name} - SOC2 compliance',
            'Dimensions': [
                {
                    'Name': 'TableName',
                    'Value': table_name
                }
            ],
            'Unit': 'Count',
            'TreatMissingData': config['treat_missing_data'],
            'Tags': [
                {'Key': 'Purpose', 'Value': 'SOC2Compliance'},
                {'Key': 'CreatedBy', 'Value': 'DynamoDBMonitoringLambda'},
                {'Key': 'TableName', 'Value': table_name},
                {'Key': 'MetricType', 'Value': metric_name}
            ]
        }

        # Add SNS notifications if configured
        if config['sns_topic_arn']:
            alarm_params['AlarmActions'] = [config['sns_topic_arn']]
            alarm_params['OKActions'] = [config['sns_topic_arn']]

        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created alarm: {alarm_name}")
        return True

    except ClientError as e:
        logger.exception(f"Error creating alarm {alarm_name}: {e}")
        return False

def create_gsi_alarm(table_name: str, index_name: str, metric_name: str, alarm_name: str, threshold: float, config: Dict) -> bool:
    """Create CloudWatch alarm for a DynamoDB GSI."""
    try:
        alarm_params = {
            'AlarmName': alarm_name,
            'ComparisonOperator': config['comparison_operator'],
            'EvaluationPeriods': config['evaluation_periods'],
            'MetricName': metric_name,
            'Namespace': 'AWS/DynamoDB',
            'Period': config['period'],
            'Statistic': config['statistic'],
            'Threshold': threshold,
            'ActionsEnabled': True,
            'AlarmDescription': f'Monitors {metric_name} for DynamoDB table {table_name} GSI {index_name} - SOC2 compliance',
            'Dimensions': [
                {
                    'Name': 'TableName',
                    'Value': table_name
                },
                {
                    'Name': 'GlobalSecondaryIndexName',
                    'Value': index_name
                }
            ],
            'Unit': 'Count',
            'TreatMissingData': config['treat_missing_data'],
            'Tags': [
                {'Key': 'Purpose', 'Value': 'SOC2Compliance'},
                {'Key': 'CreatedBy', 'Value': 'DynamoDBMonitoringLambda'},
                {'Key': 'TableName', 'Value': table_name},
                {'Key': 'IndexName', 'Value': index_name},
                {'Key': 'MetricType', 'Value': metric_name}
            ]
        }

        # Add SNS notifications if configured
        if config['sns_topic_arn']:
            alarm_params['AlarmActions'] = [config['sns_topic_arn']]
            alarm_params['OKActions'] = [config['sns_topic_arn']]

        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created GSI alarm: {alarm_name}")
        return True

    except ClientError as e:
        logger.exception(f"Error creating GSI alarm {alarm_name}: {e}")
        return False

def lambda_handler(event, context):
    """Main Lambda handler function."""
    logger.info("Starting DynamoDB monitoring remediation")

    try:
        # Load configuration from environment variables
        config = {
            'alarm_name_prefix_rcu': get_env_var('ALARM_NAME_PREFIX_RCU', 'ddb-dynamic-table-consumed-rcu'),
            'alarm_name_prefix_wcu': get_env_var('ALARM_NAME_PREFIX_WCU', 'ddb-dynamic-table-consumed-wcu'),
            'alarm_name_prefix_gsi_rcu': get_env_var('ALARM_NAME_PREFIX_GSI_RCU', 'ddb-dynamic-gsi-consumed-rcu'),
            'alarm_name_prefix_gsi_wcu': get_env_var('ALARM_NAME_PREFIX_GSI_WCU', 'ddb-dynamic-gsi-consumed-wcu'),
            'evaluation_periods': get_env_int('ALARM_EVALUATION_PERIODS', 3),
            'period': get_env_int('ALARM_PERIOD', 60),
            'statistic': get_env_var('ALARM_STATISTIC', 'Sum'),
            'comparison_operator': get_env_var('ALARM_COMPARISON_OPERATOR', 'GreaterThanThreshold'),
            'sns_topic_arn': get_env_var('SNS_TOPIC_ARN'),
            'treat_missing_data': get_env_var('TREAT_MISSING_DATA', 'missing'),
            'table_name_filter': get_env_var('TABLE_NAME_FILTER', ''),
            'excluded_table_names': get_env_list('EXCLUDED_TABLE_NAMES'),
            'default_rcu_threshold': get_env_float('DEFAULT_RCU_THRESHOLD', 600),
            'default_wcu_threshold': get_env_float('DEFAULT_WCU_THRESHOLD', 300),
            'threshold_percentage_rcu': get_env_float('THRESHOLD_PERCENTAGE_RCU', 80),
            'threshold_percentage_wcu': get_env_float('THRESHOLD_PERCENTAGE_WCU', 80)
        }

        logger.info(f"Configuration loaded: {json.dumps({k: v for k, v in config.items() if 'arn' not in k.lower()})}")

        # Get all DynamoDB tables
        tables = list_all_dynamodb_tables()

        if not tables:
            logger.info("No DynamoDB tables found")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'No DynamoDB tables found',
                    'total_tables': 0,
                    'tables_processed': 0,
                    'alarms_created': 0
                })
            }

        # Process each table
        tables_processed = 0
        alarms_created = 0

        for table in tables:
            table_name = table['table_name']

            # Apply filters
            if not should_monitor_table(table_name, config['table_name_filter'], config['excluded_table_names']):
                continue

            tables_processed += 1

            # Create table-level alarms
            # RCU Alarm
            rcu_threshold = calculate_threshold(
                table['read_capacity'],
                config['threshold_percentage_rcu'],
                config['period'],
                config['default_rcu_threshold']
            )
            rcu_alarm_name = f"{config['alarm_name_prefix_rcu']}-{table_name}"

            if not alarm_exists(rcu_alarm_name):
                if create_table_alarm(table_name, 'ConsumedReadCapacityUnits', rcu_alarm_name, rcu_threshold, config):
                    alarms_created += 1

            # WCU Alarm
            wcu_threshold = calculate_threshold(
                table['write_capacity'],
                config['threshold_percentage_wcu'],
                config['period'],
                config['default_wcu_threshold']
            )
            wcu_alarm_name = f"{config['alarm_name_prefix_wcu']}-{table_name}"

            if not alarm_exists(wcu_alarm_name):
                if create_table_alarm(table_name, 'ConsumedWriteCapacityUnits', wcu_alarm_name, wcu_threshold, config):
                    alarms_created += 1

            # Create GSI alarms
            for gsi in table['gsis']:
                index_name = gsi['index_name']

                # GSI RCU Alarm
                gsi_rcu_threshold = calculate_threshold(
                    gsi['read_capacity'],
                    config['threshold_percentage_rcu'],
                    config['period'],
                    config['default_rcu_threshold']
                )
                gsi_rcu_alarm_name = f"{config['alarm_name_prefix_gsi_rcu']}-{table_name}-{index_name}"

                if not alarm_exists(gsi_rcu_alarm_name):
                    if create_gsi_alarm(table_name, index_name, 'ConsumedReadCapacityUnits', gsi_rcu_alarm_name, gsi_rcu_threshold, config):
                        alarms_created += 1

                # GSI WCU Alarm
                gsi_wcu_threshold = calculate_threshold(
                    gsi['write_capacity'],
                    config['threshold_percentage_wcu'],
                    config['period'],
                    config['default_wcu_threshold']
                )
                gsi_wcu_alarm_name = f"{config['alarm_name_prefix_gsi_wcu']}-{table_name}-{index_name}"

                if not alarm_exists(gsi_wcu_alarm_name):
                    if create_gsi_alarm(table_name, index_name, 'ConsumedWriteCapacityUnits', gsi_wcu_alarm_name, gsi_wcu_threshold, config):
                        alarms_created += 1

        result = {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'DynamoDB monitoring remediation completed successfully',
                'total_tables': len(tables),
                'tables_processed': tables_processed,
                'alarms_created': alarms_created
            })
        }

        logger.info(f"Remediation completed: {result['body']}")
        return result

    except Exception as e:
        logger.exception(f"Error in DynamoDB monitoring remediation: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f'Error in DynamoDB monitoring remediation: {str(e)}'
            })
        }
