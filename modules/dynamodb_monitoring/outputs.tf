# ==========================================
# DynamoDB Monitoring Module Outputs
# ==========================================

output "lambda_function_arn" {
  description = "ARN of the DynamoDB monitoring Lambda function"
  value       = var.enable_lambda_function ? aws_lambda_function.dynamodb_monitoring[0].arn : null
}

output "lambda_function_name" {
  description = "Name of the DynamoDB monitoring Lambda function"
  value       = var.enable_lambda_function ? aws_lambda_function.dynamodb_monitoring[0].function_name : null
}

output "lambda_execution_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = var.enable_lambda_function ? aws_iam_role.lambda_execution_role[0].arn : null
}

output "lambda_execution_role_name" {
  description = "Name of the Lambda execution role"
  value       = var.enable_lambda_function ? aws_iam_role.lambda_execution_role[0].name : null
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for Lambda logs"
  value       = var.enable_lambda_function ? aws_cloudwatch_log_group.lambda_logs[0].name : null
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for Lambda logs"
  value       = var.enable_lambda_function ? aws_cloudwatch_log_group.lambda_logs[0].arn : null
}

output "eventbridge_rule_arn" {
  description = "ARN of the EventBridge rule for scheduling"
  value       = var.enable_lambda_function ? aws_cloudwatch_event_rule.dynamodb_monitoring_schedule[0].arn : null
}

output "eventbridge_rule_name" {
  description = "Name of the EventBridge rule for scheduling"
  value       = var.enable_lambda_function ? aws_cloudwatch_event_rule.dynamodb_monitoring_schedule[0].name : null
}



output "module_configuration" {
  description = "Configuration summary of the module"
  value = {
    alarm_name_prefixes = {
      table_rcu = var.alarm_name_prefix_rcu
      table_wcu = var.alarm_name_prefix_wcu
      gsi_rcu   = var.alarm_name_prefix_gsi_rcu
      gsi_wcu   = var.alarm_name_prefix_gsi_wcu
    }
    thresholds = {
      default_rcu_threshold    = var.default_rcu_threshold
      default_wcu_threshold    = var.default_wcu_threshold
      threshold_percentage_rcu = var.threshold_percentage_rcu
      threshold_percentage_wcu = var.threshold_percentage_wcu
    }
    alarm_settings = {
      evaluation_periods  = var.alarm_evaluation_periods
      period              = var.alarm_period
      statistic           = var.alarm_statistic
      comparison_operator = var.alarm_comparison_operator
      treat_missing_data  = var.treat_missing_data
    }
    scheduling = {
      schedule_expression = var.schedule_expression
      enabled             = var.enable_lambda_function
    }
    filtering = {
      table_name_filter    = var.table_name_filter
      excluded_table_names = var.excluded_table_names
    }
  }
}
