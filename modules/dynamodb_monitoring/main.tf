# ==========================================
# DynamoDB Monitoring Module - Main Configuration
# ==========================================

# Consolidated tags
locals {
  tags = merge(var.default_tags, var.tags)
}

# Data source for current AWS region and account
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# ==========================================
# Lambda Function Package
# ==========================================

# Create ZIP file for Lambda function
data "archive_file" "lambda_zip" {
  count       = var.enable_lambda_function ? 1 : 0
  type        = "zip"
  output_path = "${path.module}/dynamodb_monitoring_lambda.zip"

  source {
    content  = file("${path.module}/lambda_function.py")
    filename = "lambda_function.py"
  }
}

# ==========================================
# IAM Role and Policies
# ==========================================

# IAM role for Lambda execution
resource "aws_iam_role" "lambda_execution_role" {
  count = var.enable_lambda_function ? 1 : 0
  name  = "${var.lambda_function_name}-execution-role"
  tags  = local.tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# Policy for Lambda to access DynamoDB and CloudWatch
resource "aws_iam_policy" "lambda_dynamodb_cloudwatch_policy" {
  count       = var.enable_lambda_function ? 1 : 0
  name        = "${var.lambda_function_name}-dynamodb-cloudwatch-policy"
  description = "Policy for DynamoDB monitoring Lambda to access DynamoDB and CloudWatch"
  tags        = local.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:ListTables",
          "dynamodb:DescribeTable"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:DescribeAlarms",
          "cloudwatch:PutMetricAlarm",
          "cloudwatch:TagResource"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.lambda_function_name}*"
      }
    ]
  })
}

# Optional SNS publish policy if SNS topic is provided
resource "aws_iam_policy" "lambda_sns_policy" {
  count       = var.enable_lambda_function && var.sns_topic_arn != null ? 1 : 0
  name        = "${var.lambda_function_name}-sns-policy"
  description = "Policy for DynamoDB monitoring Lambda to publish to SNS"
  tags        = local.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = var.sns_topic_arn
      }
    ]
  })
}

# Attach basic execution role policy
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  count      = var.enable_lambda_function ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Attach DynamoDB and CloudWatch policy
resource "aws_iam_role_policy_attachment" "lambda_dynamodb_cloudwatch" {
  count      = var.enable_lambda_function ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = aws_iam_policy.lambda_dynamodb_cloudwatch_policy[0].arn
}

# Attach SNS policy if SNS topic is provided
resource "aws_iam_role_policy_attachment" "lambda_sns" {
  count      = var.enable_lambda_function && var.sns_topic_arn != null ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = aws_iam_policy.lambda_sns_policy[0].arn
}

# ==========================================
# CloudWatch Log Group
# ==========================================

resource "aws_cloudwatch_log_group" "lambda_logs" {
  count             = var.enable_lambda_function ? 1 : 0
  name              = "/aws/lambda/${var.lambda_function_name}"
  retention_in_days = var.log_retention_days
  tags              = local.tags
}

# ==========================================
# Lambda Function
# ==========================================

resource "aws_lambda_function" "dynamodb_monitoring" {
  count            = var.enable_lambda_function ? 1 : 0
  filename         = data.archive_file.lambda_zip[0].output_path
  function_name    = var.lambda_function_name
  role             = aws_iam_role.lambda_execution_role[0].arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.12"
  timeout          = var.lambda_timeout
  memory_size      = var.lambda_memory_size
  source_code_hash = data.archive_file.lambda_zip[0].output_base64sha256
  tags             = local.tags

  environment {
    variables = {
      ALARM_NAME_PREFIX_RCU     = var.alarm_name_prefix_rcu
      ALARM_NAME_PREFIX_WCU     = var.alarm_name_prefix_wcu
      ALARM_NAME_PREFIX_GSI_RCU = var.alarm_name_prefix_gsi_rcu
      ALARM_NAME_PREFIX_GSI_WCU = var.alarm_name_prefix_gsi_wcu
      ALARM_EVALUATION_PERIODS  = var.alarm_evaluation_periods
      ALARM_PERIOD              = var.alarm_period
      ALARM_STATISTIC           = var.alarm_statistic
      ALARM_COMPARISON_OPERATOR = var.alarm_comparison_operator
      SNS_TOPIC_ARN             = var.sns_topic_arn
      TREAT_MISSING_DATA        = var.treat_missing_data
      TABLE_NAME_FILTER         = var.table_name_filter
      EXCLUDED_TABLE_NAMES      = join(",", var.excluded_table_names)
      DEFAULT_RCU_THRESHOLD     = var.default_rcu_threshold
      DEFAULT_WCU_THRESHOLD     = var.default_wcu_threshold
      THRESHOLD_PERCENTAGE_RCU  = var.threshold_percentage_rcu
      THRESHOLD_PERCENTAGE_WCU  = var.threshold_percentage_wcu
    }
  }

  depends_on = [
    aws_cloudwatch_log_group.lambda_logs,
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_iam_role_policy_attachment.lambda_dynamodb_cloudwatch
  ]
}

# ==========================================
# EventBridge Rule for Scheduling
# ==========================================

resource "aws_cloudwatch_event_rule" "dynamodb_monitoring_schedule" {
  count               = var.enable_lambda_function ? 1 : 0
  name                = "${var.lambda_function_name}-schedule"
  description         = "Trigger DynamoDB monitoring Lambda on schedule"
  schedule_expression = var.schedule_expression
  tags                = local.tags
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  count     = var.enable_lambda_function ? 1 : 0
  rule      = aws_cloudwatch_event_rule.dynamodb_monitoring_schedule[0].name
  target_id = "DynamoDBMonitoringLambdaTarget"
  arn       = aws_lambda_function.dynamodb_monitoring[0].arn
}

resource "aws_lambda_permission" "allow_eventbridge" {
  count         = var.enable_lambda_function ? 1 : 0
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.dynamodb_monitoring[0].function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.dynamodb_monitoring_schedule[0].arn
}


