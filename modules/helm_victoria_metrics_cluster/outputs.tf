output "release_name" {
  description = "The release name of the VictoriaMetrics Cluster deployment."
  value       = helm_release.victoria_metrics_cluster.name
}

output "namespace" {
  description = "The Kubernetes namespace where VictoriaMetrics Cluster is deployed."
  value       = helm_release.victoria_metrics_cluster.namespace
}

output "status" {
  description = "The status of the Helm release."
  value       = helm_release.victoria_metrics_cluster.status
}

# TODO: Move this out of the module to avoid requiring kubernetes provider for a helm module
output "alb_endpoint" {
  value = data.kubernetes_ingress_v1.vmauth.status[0].load_balancer[0].ingress[0].hostname
}
