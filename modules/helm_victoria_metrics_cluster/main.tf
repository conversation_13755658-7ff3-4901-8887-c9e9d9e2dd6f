locals {
  vmauth_auth_content = templatefile(
    "${path.module}/templates/auth.yml.tpl",
    {
      ingester_password                   = var.vmauth_ingester_user_password,
      querier_password                    = var.vmauth_querier_user_password,
      camera_displacement_password        = var.camera_displacement_password,
      chromebox_heartbeat_password        = var.chromebox_heartbeat_password,
      server_connectivity_alerts_password = var.server_connectivity_alerts_password
    }
  )
}

resource "kubernetes_secret" "vmauth_auth" {
  metadata {
    name      = "vm-cluster-victoria-metrics-cluster-vmauth"
    namespace = var.namespace
  }

  data = {
    "auth.yml" = local.vmauth_auth_content
  }

  type = "Opaque"
}

# ConfigMap for CloudWatch agent configuration
resource "kubernetes_config_map" "cloudwatch_agent_config" {
  provider = kubernetes

  metadata {
    name      = "cwagentconfig"
    namespace = var.namespace
  }

  data = {
    "config.json" = file("${path.module}/templates/cwagentconfig.json")
  }
}

resource "kubernetes_storage_class" "vmstorage_gp3_csi" {
  metadata {
    name = "${var.release_name}-gp3-csi"
  }

  storage_provisioner    = "ebs.csi.aws.com"
  reclaim_policy         = "Delete"
  volume_binding_mode    = "WaitForFirstConsumer"
  allow_volume_expansion = true

  parameters = {
    type      = "gp3"
    encrypted = "true"
  }
}


# ==================================================================

resource "helm_release" "victoria_metrics_cluster" {
  name             = var.release_name
  repository       = "https://victoriametrics.github.io/helm-charts/"
  chart            = "victoria-metrics-cluster"
  version          = var.chart_version
  namespace        = var.namespace
  create_namespace = var.create_namespace


  # ----------- vm auth config -----------
  set {
    name  = "vmauth.enabled"
    value = var.vmauth_enabled ? "true" : "false"
  }

  set {
    name  = "vmauth.replicaCount"
    value = tostring(var.vmauth_replica_count)
  }

  set {
    name  = "vmauth.ingress.enabled"
    value = "true"
  }

  set {
    name  = "vmauth.ingress.ingressClassName"
    value = "alb"
  }

  set {
    name  = "vmauth.ingress.annotations.kubernetes\\.io/ingress\\.class"
    value = "alb"
  }

  set {
    name  = "vmauth.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/scheme"
    value = "internet-facing"
  }

  set {
    name  = "vmauth.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/target-type"
    value = "ip"
  }

  set {
    name  = "vmauth.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/certificate-arn"
    value = var.vmauth_ingress_cert_arn
  }

  set {
    name  = "vmauth.ingress.hosts[0].name"
    value = var.vmauth_ingress_host
  }

  set {
    name  = "vmauth.ingress.hosts[0].path[0]"
    value = "/insert"
  }

  set {
    name  = "vmauth.ingress.hosts[0].path[1]"
    value = "/select"
  }

  set {
    name  = "vmauth.ingress.hosts[0].port"
    value = "http"
  }

  set {
    name  = "vmauth.configSecretName"
    value = kubernetes_secret.vmauth_auth.metadata[0].name
  }

  # ----------- vm insert config -----------

  set {
    name  = "vminsert.enabled"
    value = var.vminsert_enabled ? "true" : "false"
  }

  set {
    name  = "vminsert.replicaCount"
    value = tostring(var.vminsert_replica_count)
  }

  # ---------- vm select config -----------

  set {
    name  = "vmselect.enabled"
    value = var.vmselect_enabled ? "true" : "false"
  }

  set {
    name  = "vmselect.replicaCount"
    value = tostring(var.vmselect_replica_count)
  }

  # ----------- vm storage config -----------

  set {
    name  = "vmstorage.enabled"
    value = var.vmstorage_enabled ? "true" : "false"
  }

  set {
    name  = "vmstorage.replicaCount"
    value = tostring(var.vmstorage_replica_count)
  }

  set {
    name  = "vmstorage.persistentVolume.enabled"
    value = var.vmstorage_pv_enabled ? "true" : "false"
  }

  set {
    name  = "vmstorage.persistentVolume.size"
    value = var.vmstorage_pv_size
  }

  set {
    name  = "vmstorage.persistentVolume.storageClassName"
    value = kubernetes_storage_class.vmstorage_gp3_csi.metadata[0].name
  }

  set {
    name  = "vmstorage.retentionPeriod"
    value = var.vmstorage_retention_period
  }

  #  ===== cloudwatch agent (extra container) =====
  set {
    name  = "vmstorage.extraContainers[0].name"
    value = "cloudwatch-agent"
  }

  set {
    name  = "vmstorage.extraContainers[0].image"
    value = "amazon/cloudwatch-agent:latest"
  }

  set {
    name  = "vmstorage.extraContainers[0].imagePullPolicy"
    value = "Always"
  }

  set {
    name  = "vmstorage.extraContainers[0].volumeMounts[0].name"
    value = "vmstorage-volume"
  }

  set {
    name  = "vmstorage.extraContainers[0].volumeMounts[0].mountPath"
    value = "/storage"
  }

  set {
    name  = "vmstorage.extraContainers[0].volumeMounts[1].name"
    value = "cwagentconfig"
  }

  set {
    name  = "vmstorage.extraContainers[0].volumeMounts[1].mountPath"
    value = "/etc/cwagentconfig"
  }

  set {
    name  = "vmstorage.extraContainers[0].env[0].name"
    value = "POD_NAME"
  }

  set {
    name  = "vmstorage.extraContainers[0].env[0].valueFrom.fieldRef.fieldPath"
    value = "metadata.name"
  }

  set {
    name  = "vmstorage.extraContainers[0].env[1].name"
    value = "POD_NAMESPACE"
  }

  set {
    name  = "vmstorage.extraContainers[0].env[1].valueFrom.fieldRef.fieldPath"
    value = "metadata.namespace"
  }

  set {
    name  = "vmstorage.extraVolumes[0].name"
    value = "cwagentconfig"
  }

  set {
    name  = "vmstorage.extraVolumes[0].configMap.name"
    value = "cwagentconfig"
  }

  set {
    name  = "vmstorage.serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = var.cloudwatch_iam_role_arn
  }
}
