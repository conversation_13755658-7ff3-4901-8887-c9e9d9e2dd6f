variable "cluster_name" {
  description = "Name of the Cluster"
  type        = string
  default     = ""
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "Target VPC ID"
  type        = string
  default     = ""
}

variable "account_id" {
  description = "AWS Account ID"
  type        = string
  default     = ""
}
variable "eks_version" {
  description = "Cluster Version"
  type        = string
  default     = ""
}
variable "bottlerocket_version" {
  description = "Bottlerocket version to use for AMI ID"
  type        = string
  default     = ""
}

variable "eks_cluster_log_types" {
  description = "EKS Cluster Log Types Enabled"
  type        = list(any)
  default     = ["audit"]
}

variable "eks_cluster_public_access" {
  description = "EKS Cluster Public Access ACL"
  type        = list(any)
  default     = []
}

variable "eks_node_instance_type" {
  description = "Instance Type of the EKS Cluster Workers"
  type        = string
  default     = ""
}

variable "eks_worker_key_pair" {
  description = "KeyPair Name of the EKS Cluster Worker Nodes"
  type        = string
  default     = ""
}

variable "eks_worker_general_policy" {
  description = "test"
  type        = string
  default     = "eks-worker-general-policy"
}

variable "cluster_max_capacity" {
  description = "Maximum Number of EKS Cluster Worker Nodes AutoScaling Group"
  type        = string
  default     = ""
}

variable "cluster_min_capacity" {
  description = "Minimum Number of EKS Cluster Worker Nodes AutoScaling Group"
  type        = string
  default     = ""
}

variable "on_demand_base_capacity" {
  description = "Minimum Number of On Demand EKS Cluster Worker"
  type        = string
  default     = ""
}

variable "on_demand_percentage_above_base_capacity" {
  description = "Percentage of on demand above base capacity"
  type        = string
  default     = ""
}


variable "private_vpc_subnets_ids" {
  description = "List of Private VPC Subnet IDs"
  type        = list(any)
  default     = []
}

variable "additional_sg_ids" {
  description = "List of Additional Security Groups to be attached"
  type        = list(any)
  default     = []
}

variable "ecr_region" {
  description = "AWS Region that will be used for the AWS ECR Account"
  type        = string
  default     = ""
}

variable "ecr_account" {
  description = "AWS ECR Account Number"
  type        = string
  default     = ""
}

variable "iam_cluster_role" {
  description = "EKS Cluster IAM Role Name"
  type        = string
  default     = "eks-cluster-role"
}

variable "iam_node_role" {
  description = "EKS Node IAM Role Name"
  type        = string
  default     = "eks-node-role"
}

variable "iam_node_dns_policy" {
  description = "DNS policy for EKS Node"
  type        = string
  default     = "iam-eks-node-dns-policy"
}
variable "iam_eks_node_instance_profile" {
  description = "DNS policy for EKS Node"
  type        = string
  default     = "eks-node-instance-profile"
}

variable "eks_ecr_policy" {
  description = "EKS Policy for ECR"
  type        = string
  default     = "fingermark-eks-ecr-policy"
}

variable "eks_asg_policy" {
  description = "EKS Policy for ASG"
  type        = string
  default     = "fingermark-eks-asg-policy"
}

variable "eks_elb_policy" {
  description = "EKS Policy for ELB"
  type        = string
  default     = "fingermark-eks-elb-access"
}


variable "tags" {
  description = "Infra tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}
