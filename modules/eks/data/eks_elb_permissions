{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["acm:DescribeCertificate", "acm:ListCertificates", "acm:GetCertificate"], "Resource": "*"}, {"Effect": "Allow", "Action": ["ec2:DescribeAccountAttributes", "ec2:DescribeInternetGateways", "iam:CreateServiceLinkedRole", "ec2:DescribeSecurityGroups"], "Resource": "*"}, {"Effect": "Allow", "Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:CreateSecurityGroup", "ec2:CreateTags", "ec2:DeleteTags", "ec2:DeleteSecurityGroup", "ec2:DescribeInstances", "ec2:DescribeInstanceStatus", "ec2:DescribeSecurityGroups", "ec2:DescribeSubnets", "ec2:DescribeTags", "ec2:DescribeVpcs", "ec2:ModifyInstanceAttribute", "ec2:ModifyNetworkInterfaceAttribute", "ec2:RevokeSecurityGroupIngress"], "Resource": "*"}, {"Effect": "Allow", "Action": ["elasticloadbalancing:AddTags", "elasticloadbalancing:CreateListener", "elasticloadbalancing:CreateLoadBalancer", "elasticloadbalancing:CreateRule", "elasticloadbalancing:CreateTargetGroup", "elasticloadbalancing:DeleteListener", "elasticloadbalancing:DeleteLoadBalancer", "elasticloadbalancing:DeleteRule", "elasticloadbalancing:DeleteTargetGroup", "elasticloadbalancing:DeregisterTargets", "elasticloadbalancing:DescribeListeners", "elasticloadbalancing:DescribeLoadBalancers", "elasticloadbalancing:DescribeLoadBalancerAttributes", "elasticloadbalancing:DescribeRules", "elasticloadbalancing:DescribeSSLPolicies", "elasticloadbalancing:DescribeTags", "elasticloadbalancing:DescribeTargetGroups", "elasticloadbalancing:DescribeTargetGroupAttributes", "elasticloadbalancing:DescribeTargetHealth", "elasticloadbalancing:ModifyListener", "elasticloadbalancing:ModifyLoadBalancerAttributes", "elasticloadbalancing:ModifyRule", "elasticloadbalancing:ModifyTargetGroup", "elasticloadbalancing:ModifyTargetGroupAttributes", "elasticloadbalancing:RegisterTargets", "elasticloadbalancing:RemoveTags", "elasticloadbalancing:SetIpAddressType", "elasticloadbalancing:SetSecurityGroups", "elasticloadbalancing:SetSubnets", "elasticloadbalancing:SetWebACL", "elasticloadbalancing:DescribeListenerCertificates", "elasticloadbalancing:AddListenerCertificates", "elasticloadbalancing:AddTags"], "Resource": "*"}, {"Effect": "Allow", "Action": ["iam:GetServerCertificate", "iam:ListServerCertificates"], "Resource": "*"}, {"Effect": "Allow", "Action": ["waf-regional:GetWebACLForResource", "waf-regional:GetWebACL", "waf-regional:AssociateWebACL", "waf-regional:DisassociateWebACL"], "Resource": "*"}, {"Effect": "Allow", "Action": ["tag:GetResources", "tag:TagResources"], "Resource": "*"}, {"Effect": "Allow", "Action": ["waf:GetWebACL"], "Resource": "*"}]}