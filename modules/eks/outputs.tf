output "config_map_aws_auth" {
  value = templatefile("${path.module}/data/config_map_aws_auth", { EKS_NODE_ROLE = aws_iam_role.node.arn })
}

output "kubeconfig" {
  value = templatefile("${path.module}/data/kubeconfig", { CLUSTER_NAME = var.cluster_name, EKS_ENDPOINT = aws_eks_cluster.this.endpoint, EKS_CERT = aws_eks_cluster.this.certificate_authority.0.data })
}

output "worker_node_role_arn" {
  value = aws_iam_role.node.arn
}

output "worker_node_asg" {
  value = aws_autoscaling_group.private_asg.name
}
