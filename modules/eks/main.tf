
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# This resource creates an EKS cluster 
resource "aws_eks_cluster" "this" {
  name                      = var.cluster_name
  version                   = var.eks_version
  role_arn                  = aws_iam_role.cluster.arn
  enabled_cluster_log_types = var.eks_cluster_log_types

  depends_on = [
    aws_iam_role_policy_attachment.cluster-AmazonEKSClusterPolicy,
    aws_iam_role_policy_attachment.cluster-AmazonEKSServicePolicy
  ]

  vpc_config {
    security_group_ids      = [aws_security_group.cluster.id]
    subnet_ids              = var.private_vpc_subnets_ids
    endpoint_public_access  = true
    endpoint_private_access = true
    public_access_cidrs     = var.eks_cluster_public_access
  }

}


# This security group is the main security group that will be used by the EKS service/master plane
resource "aws_security_group" "cluster" {
  name        = "eks-cluster-sg"
  description = "Cluster communication with worker nodes"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    { Name = "eks-cluster-sg" },
    var.tags
  )
}

#This security group  rule allows the worker nodes to talk to EKS API through HTTPS
resource "aws_security_group_rule" "eks_cluster_ingress_node_https" {
  description              = "Allow pods to communicate with the cluster API Server"
  from_port                = 443
  protocol                 = "tcp"
  security_group_id        = aws_security_group.cluster.id
  source_security_group_id = aws_security_group.node.id
  to_port                  = 443
  type                     = "ingress"
}

#This security group is the main security group for the worker nodes.
resource "aws_security_group" "node" {
  name        = "eks-worker-node-main-sg"
  description = "Security group for all nodes in the cluster"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.tags,
    { "Name" = "eks-worker-node-main-sg"
    "kubernetes.io/cluster/${var.cluster_name}" = "owned" }
  )
}

#This security group rule allows the nodes to communicate with each other on specific ports
resource "aws_security_group_rule" "node_ingress_self_https" {
  description              = "Allow HTTPS communication between worker nodes"
  from_port                = 443
  protocol                 = "tcp"
  security_group_id        = aws_security_group.node.id
  source_security_group_id = aws_security_group.node.id
  to_port                  = 443
  type                     = "ingress"
}

resource "aws_security_group_rule" "node_ingress_self_kubelet" {
  description              = "Allow Kubelet API communication between worker nodes"
  from_port                = 10250
  protocol                 = "tcp"
  security_group_id        = aws_security_group.node.id
  source_security_group_id = aws_security_group.node.id
  to_port                  = 10250
  type                     = "ingress"
}

resource "aws_security_group_rule" "node_ingress_self_atlantis" {
  description              = "Allow Custom TCP on port 4141 between worker nodes for Atlantis"
  from_port                = 4141
  protocol                 = "tcp"
  security_group_id        = aws_security_group.node.id
  source_security_group_id = aws_security_group.node.id
  to_port                  = 4141
  type                     = "ingress"
}

resource "aws_security_group_rule" "node_ingress_self_nodeports" {
  description              = "Allow NodePort services communication between worker nodes"
  from_port                = 30000
  protocol                 = "tcp"
  security_group_id        = aws_security_group.node.id
  source_security_group_id = aws_security_group.node.id
  to_port                  = 32767
  type                     = "ingress"
}
#This security group rule allows the eks master plane to reach the worker nodes from ports 1025-65535
resource "aws_security_group_rule" "node_ingress_cluster" {
  description              = "Allow worker Kubelets and pods to receive communication from the cluster control plane"
  from_port                = 1025
  protocol                 = "tcp"
  security_group_id        = aws_security_group.node.id
  source_security_group_id = aws_security_group.cluster.id
  to_port                  = 65535
  type                     = "ingress"
}



###########################

#This is the launch template that is used to set up the worker nodes
resource "aws_launch_template" "private_launchconfiguration" {
  iam_instance_profile {
    name = aws_iam_instance_profile.node.name
  }
  image_id      = data.aws_ssm_parameter.ami.value
  instance_type = var.eks_node_instance_type
  key_name      = var.eks_worker_key_pair
  name_prefix   = var.cluster_name
  network_interfaces {
    associate_public_ip_address = false
    security_groups             = concat([aws_security_group.node.id], var.additional_sg_ids)

  }

  user_data = base64encode(templatefile("${path.module}/data/user-data.toml", { EKS_ENDPOINT = aws_eks_cluster.this.endpoint, EKS_CERT = aws_eks_cluster.this.certificate_authority.0.data, CLUSTER_NAME = var.cluster_name }))

  lifecycle {
    create_before_destroy = true
  }
  monitoring {
    enabled = true
  }

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "true"
      iops                  = 0
      volume_size           = 50
      volume_type           = "gp2"
    }
  }
}

#This is autoscaling group that defines how small or big our cluster will be. It also supports mixed instances 
resource "aws_autoscaling_group" "private_asg" {
  desired_capacity_type = "units"
  max_size              = var.cluster_max_capacity
  min_size              = var.cluster_min_capacity
  name                  = "${var.cluster_name}-worker-node-asg"
  vpc_zone_identifier   = var.private_vpc_subnets_ids

  tag {
    key                 = "Name"
    value               = "${var.cluster_name}-worker-node"
    propagate_at_launch = true
  }

  tag {
    key                 = "kubernetes.io/cluster/${var.cluster_name}"
    value               = "owned"
    propagate_at_launch = true
  }

  tag {
    key                 = "k8s.io/cluster-autoscaler/${var.cluster_name}"
    value               = "owned"
    propagate_at_launch = true
  }

  tag {
    key                 = "k8s.io/cluster-autoscaler/enabled"
    value               = "true"
    propagate_at_launch = true
  }


  lifecycle {
    create_before_destroy = true
  }

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = var.on_demand_base_capacity
      on_demand_percentage_above_base_capacity = var.on_demand_percentage_above_base_capacity
      spot_allocation_strategy                 = "lowest-price"
      spot_instance_pools                      = 10
    }
    #name_prefix               = "${var.cluster_name}-mixed-demand-spot"
    #protect_from_scale_in     = false
    launch_template {
      launch_template_specification {
        launch_template_id = aws_launch_template.private_launchconfiguration.id
        version            = aws_launch_template.private_launchconfiguration.latest_version

      }
      override {
        instance_type = "t3.medium"
      }
      override {
        instance_type = "t3.large"
      }
      override {
        instance_type = "t2.large"
      }

    }
  }
}
