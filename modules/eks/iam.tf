# EKS CLUSTER
resource "aws_iam_role" "cluster" {
  name = var.iam_cluster_role

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "eks.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
POLICY
}

resource "aws_iam_role_policy_attachment" "cluster-AmazonEKSClusterPolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.cluster.name
}

resource "aws_iam_role_policy_attachment" "cluster-AmazonEKSServicePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSServicePolicy"
  role       = aws_iam_role.cluster.name
}

# EKS NODES

resource "aws_iam_role_policy_attachment" "node-AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.node.name
}

resource "aws_iam_role_policy_attachment" "node-AWSWAFReadOnlyAccess" {
  policy_arn = "arn:aws:iam::aws:policy/AWSWAFReadOnlyAccess"
  role       = aws_iam_role.node.name
}

resource "aws_iam_role_policy_attachment" "node-AmazonEKS_CNI_Policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.node.name
}
resource "aws_iam_role_policy_attachment" "node-AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.node.name
}


resource "aws_iam_instance_profile" "node" {
  name = var.iam_eks_node_instance_profile
  role = aws_iam_role.node.name
}

resource "aws_iam_role" "node" {
  name = var.iam_node_role

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
POLICY

  tags = {
    Terraform = "true"
  }
}

resource "aws_iam_policy" "eks_node_dns_policy" {
  name   = var.iam_node_dns_policy
  policy = templatefile("${path.module}/data/eks_node_dns_policy", {})
}


# ELK ECR ACCESS
resource "aws_iam_policy" "eks_ecr_policy" {
  name   = var.eks_ecr_policy
  policy = templatefile("${path.module}/data/eks_ecr_permissions", { REGION = var.ecr_region, ACCOUNT = var.ecr_account })
}

# EKS Autoscaling access
resource "aws_iam_policy" "eks_asg_policy" {
  name   = var.eks_asg_policy
  policy = templatefile("${path.module}/data/eks_asg_permissions", {})
}


resource "aws_iam_role_policy_attachment" "eks-ecr-workernode-cluster-policy-attachments" {
  policy_arn = aws_iam_policy.eks_ecr_policy.arn
  role       = aws_iam_role.node.name
}

# EKS NODE GENERAL POLICY ACCESS


resource "aws_iam_policy" "eks_worker_general_policy" {
  name   = var.eks_worker_general_policy
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [

        {
            "Sid": "",
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Resource": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*"
        }

    ]
}
EOF
}


resource "aws_iam_role_policy_attachment" "eks_worker_general" {
  policy_arn = aws_iam_policy.eks_worker_general_policy.arn
  role       = aws_iam_role.node.name
}

## ELK ELB ACCESS


resource "aws_iam_policy" "eks_elb_access" {
  name   = var.eks_elb_policy
  policy = templatefile("${path.module}/data/eks_elb_permissions", {})
}

resource "aws_iam_role_policy_attachment" "eks_worker_elb_access" {
  policy_arn = aws_iam_policy.eks_elb_access.arn
  role       = aws_iam_role.node.name
}
