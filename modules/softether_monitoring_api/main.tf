module "softether_monitoring_api" {
  source                 = "terraform-aws-modules/ec2-instance/aws"
  version                = "3.5.0"
  name                   = "softether-monitoring-api"
  availability_zone      = "ap-southeast-2c"
  user_data              = var.user_data
  ami                    = "ami-0567f647e75c7bc05"
  instance_type          = "t3.micro"
  key_name               = "matias"
  monitoring             = true
  vpc_security_group_ids = ["sg-02fea1b93c76be7ac", "sg-c6e19cb2"]
  subnet_id              = "subnet-82a0c2da"

  tags = merge(var.default_tags, var.tags)
}
