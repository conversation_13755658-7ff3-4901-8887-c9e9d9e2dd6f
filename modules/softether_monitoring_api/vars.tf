variable "default_tags" {
  type = map(string)
  default = {
    Terraform = "true"
    Backup    = "true"
    Stack     = "monitoring"
    Monitored = "true"
    Product   = "Eyecue"
    Squad     = "Platform"
  }
}

variable "tags" {
  type = map(string)
  default = {
    Product = "Eyecue"
    Squad   = "Platform"
  }
}

# variable "user_data_template" {
#   type        = string
#   default     = "user_data/user-data.sh"
#   description = "User Data template to use for provisioning EC2"
# }
variable "user_data" {
  type    = string
  default = <<-EOT
    #!/bin/bash

    apt update && apt upgrade -y
    apt install -y python3-pip gcc make wget unzip
    python3 -m pip install fastapi==0.68.1 uvicorn==0.15.0 loguru==0.5.3

    wget https://github.com/SoftEtherVPN/SoftEtherVPN_Stable/releases/download/v4.38-9760-rtm/softether-vpnclient-v4.38-9760-rtm-2021.08.17-linux-x64-64bit.tar.gz -P /tmp

    cd /usr/local

    tar -xvf /tmp/softether-vpnclient-v4.38-9760-rtm-2021.08.17-linux-x64-64bit.tar.gz

    cd vpnclient/
    make
    hostnamectl set-hostname softether-monitoring-api
    EOT
}