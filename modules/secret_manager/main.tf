resource "aws_secretsmanager_secret" "rds_lambdas" {
  name = var.eyecue_postgres_lambdas_secret_name
  lifecycle {
    prevent_destroy = true
  }
  tags = var.tags
}

# resource "aws_secretsmanager_secret_version" "rds_lambdas" {
#   secret_id = aws_secretsmanager_secret.rds_lambdas.id
#   secret_string = jsonencode(var.eyecue_postgres_lambdas)
# }

resource "aws_secretsmanager_secret" "rds_dashboard" {
  name = var.eyecue_dashboard_data_secret_name
  lifecycle {
    prevent_destroy = true
  }
  tags = var.tags
}

# resource "aws_secretsmanager_secret_version" "rds_dashboard" {
#   secret_id = aws_secretsmanager_secret.rds_dashboard.id
#   secret_string = jsonencode(var.eyecue_dashboard_data)
# }

  