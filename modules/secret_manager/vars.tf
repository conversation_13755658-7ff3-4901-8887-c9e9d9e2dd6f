variable "eyecue_postgres_lambdas_secret_name" {

}

# variable "eyecue_postgres_lambdas" {
#   type = map(string)  
# }

variable "eyecue_dashboard_data_secret_name" {

}

# variable "eyecue_dashboard_data" {
#   type = map(string)  
# }
variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}