variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "eks_version" {
  description = "Kubernetes version for the cluster"
  type        = string
  default     = "1.30"
}

variable "subnet_ids" {
  description = "List of subnet IDs for the EKS cluster (typically private subnets)"
  type        = list(string)
}

variable "cluster_security_group_ids" {
  description = "List of security group IDs to attach to the EKS cluster"
  type        = list(string)
  default     = []
}

variable "endpoint_public_access" {
  description = "Whether the EKS cluster API endpoint is publicly accessible"
  type        = bool
  default     = true
}

variable "endpoint_private_access" {
  description = "Whether the EKS cluster API endpoint is accessible from within the VPC"
  type        = bool
  default     = true
}

variable "public_access_cidrs" {
  description = "List of CIDR blocks allowed to access the EKS control plane endpoint"
  type        = list(string)
  default = [
    "*************/32", # Fingermark office IP
    "************/32",  # Bitbucket runner in Infra Dev account
    "*************/32", # <PERSON>'s home IP
  ]
}

variable "cluster_log_types" {
  description = "List of cluster log types to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "tags" {
  description = "Tags to apply to all resources in this module"
  type        = map(string)
  default     = {}
}

# Node group configuration
variable "desired_size" {
  description = "Desired number of worker nodes"
  type        = number
  default     = 2
}

variable "min_size" {
  description = "Minimum number of worker nodes"
  type        = number
  default     = 1
}

variable "max_size" {
  description = "Maximum number of worker nodes"
  type        = number
  default     = 4
}

variable "instance_types" {
  description = "List of instance types to use in the node group"
  type        = list(string)
  default     = ["m5.xlarge"]
}

variable "ami_type" {
  description = "AMI type for the node group. Options: AL2_x86_64, AL2_x86_64_GPU, BOTTLEROCKET_x86_64, BOTTLEROCKET_ARM_64"
  type        = string
  default     = "BOTTLEROCKET_x86_64"
}

variable "capacity_type" {
  description = "Capacity type for the node group (ON_DEMAND or SPOT)"
  type        = string
  default     = "ON_DEMAND"
}
