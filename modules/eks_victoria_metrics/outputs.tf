output "cluster_name" {
  description = "Name of the created EKS cluster"
  value       = aws_eks_cluster.eks_cluster.name
}

output "cluster_endpoint" {
  description = "Endpoint for the EKS cluster"
  value       = data.aws_eks_cluster.cluster.endpoint
}

output "cluster_token" {
  description = "Token for the EKS cluster"
  value       = data.aws_eks_cluster_auth.cluster_auth.token
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate authority data for the EKS cluster"
  value       = data.aws_eks_cluster.cluster.certificate_authority[0].data
}

output "node_group_name" {
  description = "Name of the created node group"
  value       = aws_eks_node_group.node_group.node_group_name
}

output "lb_controller_irsa_role_arn" {
  description = "ARN of the IAM role for the AWS Load Balancer Controller"
  value       = module.lb_controller_irsa.iam_role_arn
}

output "oidc_provider_arn" {
  description = "ARN of the OIDC provider for the EKS cluster"
  value       = local.oidc_provider_arn
}