variable "customer_acronym" {
  description = "Customer acronym"
}

variable "workspace" {
  default     = "fingermarkltd"
  description = "Bitbucket workspace"
}

variable "project_key" {
  default     = "EYECUE_AIML"
  description = "Bitbucket project key"
}

variable "bitbucket_access_key_name" {
  default     = "eyecue-server-setup"
  description = "Bitbucket access key name"
}

variable "bitbucket_access_key" {
  default     = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDdIU6+AS4SPqlyH3SLzZcdBjDcjHexAXPnD0yVExkj4eSsoriikLFOPsGfW/Qjb1fdosrEHLVU0YOGAJpHr/KZ22OOikFGNVDM6zckJswfHPuqI+N1nEa2zn3qF6MuE1STZp+jEn/L6cCbhaag79eYsfdOnJl+FfTtDYPWkZVimNUTJQ4CgSTYGzXi8bvLhKc0blxXSSH23uNKHZAaBackcPJ1nYTDyNjVvzd38efQ/aucYZXrgNhGyHj1nvC3/w6IfxnWOsWm85dmN4lVL/pkSnEj/HL0520FudXrzNXV1LT6WyZuFxbSQcyrk5c7af9RDmezb/8c+F1lJX/Q7SuF"
  description = "Bitbucket access key required for ArgoCD to access the helm repository"
}
