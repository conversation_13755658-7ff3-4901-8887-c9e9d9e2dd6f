resource "bitbucket_repository" "helm_values" {
  workspace   = var.workspace
  name        = "eyecue-${var.customer_acronym}-helm"
  project_key = var.project_key
}

resource "bitbucket_deploy_key" "helm_values" {
  depends_on = [bitbucket_repository.helm_values]
  workspace  = var.workspace
  repository = "eyecue-${var.customer_acronym}-helm"
  label      = var.bitbucket_access_key_name
  key        = var.bitbucket_access_key
}

resource "bitbucket_branch_restriction" "helm_values" {
  depends_on = [bitbucket_repository.helm_values]
  workspace  = var.workspace
  repository = "eyecue-${var.customer_acronym}-helm"
  pattern    = "master"
  kind       = "push"
  users = [
    "eyecue-deployer" # required for the eyecue helm parser to push to master
  ]
}
