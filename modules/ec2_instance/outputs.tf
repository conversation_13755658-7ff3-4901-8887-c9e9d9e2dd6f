output "instance_id" {
  description = "The ID of the EC2 instance"
  value       = aws_instance.aws_ec2_instance.id
}

output "public_ip" {
  description = "The public IP address of the EC2 instance"
  value       = aws_instance.aws_ec2_instance.public_ip
}

output "private_ip" {
  description = "The private IP address of the EC2 instance"
  value       = aws_instance.aws_ec2_instance.private_ip
}

output "ssm_connect_command" {
  description = "Command to connect to the instance via SSM"
  value       = var.enable_ssm ? "aws ssm start-session --target ${aws_instance.aws_ec2_instance.id}" : "SSM not enabled"
}

output "iam_role_arn" {
  description = "ARN of the IAM role attached to the instance"
  value       = var.enable_ssm ? aws_iam_role.ec2_ssm_role[0].arn : null
}
