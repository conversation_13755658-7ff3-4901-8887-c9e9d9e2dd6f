# IAM role for SSM access
resource "aws_iam_role" "ec2_ssm_role" {
  count = var.enable_ssm ? 1 : 0
  name  = "${var.instance_name}-ssm-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Attach AWS managed policy for SSM
resource "aws_iam_role_policy_attachment" "ssm_managed_instance_core" {
  count      = var.enable_ssm ? 1 : 0
  role       = aws_iam_role.ec2_ssm_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# Instance profile for the role
resource "aws_iam_instance_profile" "ec2_profile" {
  count = var.enable_ssm ? 1 : 0
  name  = "${var.instance_name}-ssm-profile"
  role  = aws_iam_role.ec2_ssm_role[0].name

  tags = var.tags
}

resource "aws_instance" "aws_ec2_instance" {
  ami                    = var.ami
  instance_type          = var.instance_type
  subnet_id              = var.subnet_id
  key_name               = var.key_name
  ebs_optimized          = true
  vpc_security_group_ids = var.security_group_ids
  iam_instance_profile   = var.enable_ssm ? aws_iam_instance_profile.ec2_profile[0].name : null

  root_block_device {
    encrypted             = true
    volume_size           = var.ebs_volume_size
    volume_type           = var.ebs_volume_type
    delete_on_termination = true
  }

  metadata_options {
    http_tokens   = "required" # Required to enforce IMDSv2
    http_endpoint = "enabled"
  }

  tags = var.tags
}