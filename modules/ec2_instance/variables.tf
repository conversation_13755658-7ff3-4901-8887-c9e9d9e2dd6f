variable "ami" {
  description = "The AMI ID to use for the instance"
  type        = string
}

variable "instance_type" {
  description = "The type of instance to start"
  type        = string
  default     = "t2.micro"
}

variable "subnet_id" {
  description = "The VPC subnet ID to launch the instance in"
  type        = string
}

variable "key_name" {
  description = "The key to use for the instance"
  type        = string
  default     = null
}

variable "ebs_volume_size" {
  description = "Size of the EBS root volume in GB"
  type        = number
  default     = 20
}

variable "security_group_ids" {
  description = "List of security group IDs to associate with the instance"
  type        = list(string)
}

variable "ebs_volume_type" {
  description = "Type of EBS volume to use"
  type        = string
  default     = "gp3"
}

variable "tags" {
  description = "EC2 instance tags"
  type        = map(string)
  default     = {}
}

variable "enable_ssm" {
  description = "Enable AWS Systems Manager (SSM) access for the instance"
  type        = bool
  default     = true
}

variable "instance_name" {
  description = "Name for the instance (used for IAM role naming)"
  type        = string
  default     = "ec2-instance"
}