variable "magicinfo_server_name" {
  description = "Magicinfo server's name"
  type        = string
  default     = "fm-dmbo-magicinfo"
}


variable "magicinfo_server_admin_password" {
  description = "Magicinfo server's admin password"
  type        = string
  default     = ""
}

variable "default_tags" {
  description = "A map of default tags to tag resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Environment = "Prod"
    Squad       = "Hardware Product"
    Product     = "Eyecue Dashboards"
  }
}

variable "tags" {
  description = "A map of tags to tag resources"
  type        = map(string)
  default     = {}
}

variable "magicinfo_server_ami_id" {
  description = "Magicinfo server's AMI ID"
  type        = string
  default     = "ami-0177a8353c3b60b90"
}

variable "magicinfo_server_additional_nic_security_groups" {
  description = "Magicinfo server's security groups"
  type        = list(string)
  default     = []
}

variable "magicinfo_server_instance_type" {
  description = "Magicinfo server's instance type"
  type        = string
  default     = "t3.medium"
}

variable "magicinfo_vpc_id" {
  description = "Magicinfo server's VPC ID"
  type        = string
  default     = ""
}

variable "magicinfo_rdp_access_cidr_blocks" {
  description = "Magicinfo server's RDP access cidr blocks"
  type        = list(string)
  default     = []
}

variable "magicinfo_public_subnet_id" {
  description = "Magicinfo server's VPC public Subnet ID"
  type        = string
  default     = ""
}

variable "magicinfo_nic_ip" {
  description = "Magicinfo server's Network Interface IP address"
  type        = string
  default     = ""
}

variable "magicinfo_server_securitygroup_name" {
  description = "Magicinfo server's Security Group ID"
  type        = string
  default     = ""
}

variable "magicinfo_server_alb_name" {
  description = "Magicinfo server's ALB name"
  type        = string
  default     = ""
}


variable "magicinfo_server_s3_bucket_name" {
  description = "Magicinfo server's s3 bucket name for logs"
  type        = string
  default     = ""
}

variable "magicinfo_alb_subnet_ids" {
  description = "Magicinfo server's alb subnets"
  type        = list(string)
  default     = []
}

variable "cloudflare_record_name" {
  description = "Cloudflare record name"
  type        = string
  default     = ""
}

variable "acm_domain_name" {
  description = "Cloudflare record name"
  type        = string
  default     = ""
}
