resource "aws_instance" "magicinfo_server" {
  ami           = var.magicinfo_server_ami_id
  instance_type = var.magicinfo_server_instance_type

  tags = merge(var.tags, var.default_tags)

  network_interface {
    network_interface_id = aws_network_interface.magicinfo_nic.id
    device_index         = 0
  }

  root_block_device {
    encrypted   = true
    volume_type = "gp3"
    volume_size = 256

    tags = merge(var.tags, var.default_tags)
  }

  lifecycle {
    prevent_destroy = true
    ignore_changes  = [ami]
  }

}

resource "aws_network_interface" "magicinfo_nic" {
  subnet_id = var.magicinfo_public_subnet_id
  private_ips = [
    var.magicinfo_nic_ip
  ]

  security_groups = concat(var.magicinfo_server_additional_nic_security_groups, [resource.aws_security_group.magicinfo_security_group.id])

  tags = merge(var.tags, var.default_tags)
}

resource "aws_eip" "magicinfo_eip" {
  vpc                       = true
  network_interface         = aws_network_interface.magicinfo_nic.id
  associate_with_private_ip = var.magicinfo_nic_ip

  tags = merge(var.tags, var.default_tags)
}

resource "aws_security_group" "magicinfo_security_group" {
  name        = var.magicinfo_server_securitygroup_name
  description = "Allow TLS inbound traffic"
  vpc_id      = var.magicinfo_vpc_id

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description = "RDP from VPC"
    from_port   = 3389
    to_port     = 3389
    protocol    = "tcp"
    cidr_blocks = var.magicinfo_rdp_access_cidr_blocks
  }

  ingress {
    description = "TLS from VPC"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 7
    to_port     = 7
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 9
    to_port     = 9
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }
  ingress {
    description = "Magicinfo software requirement"
    from_port   = 21
    to_port     = 21
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 990
    to_port     = 990
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 7001
    to_port     = 7001
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  ingress {
    description = "Magicinfo software requirement"
    from_port   = 7002
    to_port     = 7002
    protocol    = "tcp"
    cidr_blocks = [
      "0.0.0.0/0"
    ]
  }

  tags = {
    Name = var.magicinfo_server_name
  }
}

resource "aws_acm_certificate" "magicinfo_domain_cert" {
  domain_name       = var.acm_domain_name
  validation_method = "DNS"

  tags = merge(var.tags, var.default_tags)

  lifecycle {
    create_before_destroy = true
  }
}

module "magicinfo_subdomain_validation" {
  source                  = "../cloudflare"
  cloudflare_record_name  = tolist(aws_acm_certificate.magicinfo_domain_cert.domain_validation_options)[0].resource_record_name
  cloudflare_record_value = tolist(aws_acm_certificate.magicinfo_domain_cert.domain_validation_options)[0].resource_record_value
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = tolist(aws_acm_certificate.magicinfo_domain_cert.domain_validation_options)[0].resource_record_type
}

resource "aws_acm_certificate_validation" "magicinfo_acm_certificate_validation" {
  certificate_arn         = aws_acm_certificate.magicinfo_domain_cert.arn
  validation_record_fqdns = [module.magicinfo_subdomain_validation.hostname]
}

resource "aws_lb" "magicinfo_alb" {
  name               = var.magicinfo_server_alb_name
  internal           = false
  load_balancer_type = "application"
  security_groups    = concat(var.magicinfo_server_additional_nic_security_groups, [resource.aws_security_group.magicinfo_security_group.id])
  subnets            = var.magicinfo_alb_subnet_ids

  enable_deletion_protection = true

  access_logs {
    bucket  = aws_s3_bucket.magicinfo_s3.bucket
    prefix  = "access-logs"
    enabled = true
  }

  tags = merge(var.tags, var.default_tags)

  depends_on = [
    resource.aws_acm_certificate_validation.magicinfo_acm_certificate_validation
  ]
}

resource "aws_lb_target_group" "magicinfo_alb_target_group" {
  name        = var.magicinfo_server_name
  port        = 7001
  target_type = "instance"
  protocol    = "HTTP"
  vpc_id      = var.magicinfo_vpc_id
  tags        = merge(var.tags, var.default_tags, { Name = var.magicinfo_server_name })

  health_check {
    healthy_threshold   = 3
    unhealthy_threshold = 5
    timeout             = 5
    interval            = 30
    path                = "/MagicInfo/restapi/v2.0/auth/checkAuth"
    port                = "7001"
  }
}

resource "aws_lb_target_group_attachment" "magicinfo_alb_target_group_attachment" {
  target_group_arn = aws_lb_target_group.magicinfo_alb_target_group.arn
  target_id        = aws_instance.magicinfo_server.id
  port             = "7001"
}

resource "aws_lb_listener" "magicinfo_listener_redirect" {
  load_balancer_arn = aws_lb.magicinfo_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    order = 1
    type  = "redirect"
    redirect {
      host        = "#{host}"
      path        = "/#{path}"
      port        = "7002"
      protocol    = "HTTPS"
      query       = "#{query}"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "magicinfo_listener_https" {
  load_balancer_arn = aws_lb.magicinfo_alb.arn
  port              = "7002"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = aws_acm_certificate.magicinfo_domain_cert.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.magicinfo_alb_target_group.arn
  }

  depends_on = [
    resource.aws_acm_certificate_validation.magicinfo_acm_certificate_validation
  ]
}

resource "aws_lb_listener" "magicinfo_listener_https_1" {
  load_balancer_arn = aws_lb.magicinfo_alb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = aws_acm_certificate.magicinfo_domain_cert.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.magicinfo_alb_target_group.arn
  }

  depends_on = [
    resource.aws_acm_certificate_validation.magicinfo_acm_certificate_validation
  ]
}

resource "aws_lb_listener_rule" "magicinfo_listener_forward_rule" {
  listener_arn = aws_lb_listener.magicinfo_listener_https.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.magicinfo_alb_target_group.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }
}

resource "aws_lb_listener_rule" "magicinfo_listener_forward_basepath_rule" {
  listener_arn = aws_lb_listener.magicinfo_listener_https.arn
  priority     = 99

  action {
    type = "redirect"
    redirect {
      host        = "#{host}"
      path        = "/MagicInfo"
      port        = "7002"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    path_pattern {
      values = ["/"]
    }
  }
}

resource "aws_lb_listener_rule" "magicinfo_listener_forward_basepath_rule_1" {
  listener_arn = aws_lb_listener.magicinfo_listener_https_1.arn
  priority     = 99

  action {
    type = "redirect"
    redirect {
      host        = "#{host}"
      path        = "/MagicInfo"
      port        = "7002"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    path_pattern {
      values = ["/"]
    }
  }
}

resource "aws_lb_listener_rule" "magicinfo_listener_forward_rule_1" {
  listener_arn = aws_lb_listener.magicinfo_listener_https_1.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.magicinfo_alb_target_group.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }
}

resource "aws_s3_bucket" "magicinfo_s3" {
  bucket = var.magicinfo_server_s3_bucket_name

  tags = merge(var.tags, var.default_tags)
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_policy" "magicinfo_s3_bucket_policy" {
  bucket = var.magicinfo_server_s3_bucket_name
  policy = data.aws_iam_policy_document.magicinfo_s3_policy_document.json
}

resource "aws_s3_bucket_acl" "magicinfo_s3_acl" {
  bucket = aws_s3_bucket.magicinfo_s3.id
  acl    = "private"
}

data "aws_iam_policy_document" "magicinfo_s3_policy_document" {
  policy_id = "dm-dmbo-magicinfo"

  statement {
    actions = [
      "s3:PutObject",
    ]
    effect = "Allow"
    resources = [
      "${aws_s3_bucket.magicinfo_s3.arn}/*",
    ]
    principals {
      identifiers = ["${data.aws_elb_service_account.main.arn}"]
      type        = "AWS"
    }
  }

  statement {
    actions = [
      "s3:PutObject"
    ]
    effect    = "Allow"
    resources = ["${aws_s3_bucket.magicinfo_s3.arn}/*"]
    principals {
      identifiers = ["delivery.logs.amazonaws.com"]
      type        = "Service"
    }
  }
  statement {
    actions = [
      "s3:GetBucketAcl"
    ]
    effect    = "Allow"
    resources = ["${aws_s3_bucket.magicinfo_s3.arn}"]
    principals {
      identifiers = ["delivery.logs.amazonaws.com"]
      type        = "Service"
    }
  }
}

module "magicinfo_subdomain" {
  source                  = "../cloudflare"
  cloudflare_record_name  = var.cloudflare_record_name
  cloudflare_record_value = aws_lb.magicinfo_alb.dns_name
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

resource "aws_wafv2_web_acl" "magicinfo_waf_acl" {
  name  = var.magicinfo_server_name
  scope = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "dm-dmbo-magicinfo-rule-1"
    priority = 1

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = var.magicinfo_server_name
      sampled_requests_enabled   = false
    }
  }

  tags = merge(var.tags, var.default_tags)

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = var.magicinfo_server_name
    sampled_requests_enabled   = false
  }
}

resource "aws_wafv2_web_acl_association" "magicinfo_waf_alb_association" {
  resource_arn = aws_lb.magicinfo_alb.arn
  web_acl_arn  = aws_wafv2_web_acl.magicinfo_waf_acl.arn
}
