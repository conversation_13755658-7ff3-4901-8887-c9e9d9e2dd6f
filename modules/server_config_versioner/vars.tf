variable "default_tags" {
  description = "Map of module's default tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "config"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "tags" {
  description = "Map of module's default tags"
  type        = map(any)
  default     = {}
}

variable "server_config_s3_versioning" {
  description = "S3 bucket's versioning, True or False"
  type        = bool
  default     = true
}

variable "server_config_name" {
  description = "Application's name"
  type        = string
  default     = "MyApp"
}

variable "server_config_s3_acl" {
  description = "S3 Bucket ACL type"
  type        = string
  default     = "private"
}

variable "stage" {
  description = "Identifies the stage: prod, dev, qa"
  type        = string
  default     = "dev"
}

variable "additional_policy_arns" {
  description = "Attaching addition policies to Instance Profile"

}