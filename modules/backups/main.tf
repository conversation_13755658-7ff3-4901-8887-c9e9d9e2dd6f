module "data_lifecycle" {
  source                        = "../data_lifecycle"
  data_lifecycle_name           = var.data_lifecycle_name
  tags                          = var.tags
  data_lifecycle_state          = var.data_lifecycle_state
  data_lifecycle_resource_types = var.data_lifecycle_resource_types
  data_lifecycle_target_tags    = var.data_lifecycle_target_tags
  data_lifecycle_interval       = var.data_lifecycle_interval
  data_lifecycle_interval_unit  = var.data_lifecycle_interval_unit
  data_lifecycle_times          = var.data_lifecycle_times
  data_lifecycle_retain_rule    = var.data_lifecycle_retain_rule
  data_lifecycle_copy_tags      = var.data_lifecycle_copy_tags
  data_lifecycle_tags_to_add    = var.data_lifecycle_tags_to_add
}
