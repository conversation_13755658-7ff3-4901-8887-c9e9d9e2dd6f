variable "ecr_name" {
  type        = string
  description = "The Name of the ECR Repository"
  default     = ""
}

variable "ecr_image_tag_mutability" {
  type        = string
  description = "Setting the image mutability"
  default     = "IMMUTABLE"
}

variable "tags" {
  description = "AWS Infrastructure Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}