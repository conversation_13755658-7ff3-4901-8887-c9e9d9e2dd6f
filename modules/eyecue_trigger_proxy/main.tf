#############################
# IAM Role and Policies
#############################

resource "aws_iam_role" "proxy_lambda_role" {
  name = "${var.proxy_function_name}-role-${var.environment}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect    = "Allow",
      Principal = { Service = "lambda.amazonaws.com" },
      Action    = "sts:AssumeRole"
    }]
  })
  tags = var.tags
}

resource "aws_iam_policy" "proxy_invoke_policy" {
  name        = "${var.proxy_function_name}-invoke-policy-${var.environment}"
  description = "Policy to allow proxy lambda to invoke target lambda"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Sid      = "InvokeTargetLambda",
      Effect   = "Allow",
      Action   = "lambda:InvokeFunction",
      Resource = "arn:aws:lambda:${var.target_region}:${var.aws_account_id}:function:${var.target_function}"
    }]
  })
  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "proxy_lambda_policy_attach" {
  role       = aws_iam_role.proxy_lambda_role.name
  policy_arn = aws_iam_policy.proxy_invoke_policy.arn
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.proxy_lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

#############################
# Package Lambda Code
#############################

data "archive_file" "proxy_lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/lambda_function"
  output_path = "${path.module}/lambda_function/${var.proxy_function_name}.zip"
}

#############################
# Create the Proxy Lambda Function
#############################

resource "aws_lambda_function" "proxy_lambda" {
  function_name    = "${var.proxy_function_name}-${var.environment}"
  role             = aws_iam_role.proxy_lambda_role.arn
  handler          = "lambda_function.lambda_handler"
  runtime          = var.lambda_runtime
  timeout          = 30
  filename         = data.archive_file.proxy_lambda_zip.output_path
  source_code_hash = data.archive_file.proxy_lambda_zip.output_base64sha256

  environment {
    variables = {
      TARGET_FUNCTION = var.target_function
      TARGET_REGION   = var.target_region
      AWS_ACCOUNT_ID  = var.aws_account_id
    }
  }
  tags = var.tags
}
