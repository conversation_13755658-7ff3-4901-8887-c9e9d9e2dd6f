variable "environment" {
  type        = string
  description = "Environment (e.g. prod, stg, dev)"
}

variable "aws_account_id" {
  type        = string
  description = "AWS Account ID"
}

variable "target_function" {
  type        = string
  description = "Name of the target lambda function (in ap-southeast-2) that the proxy will invoke"
}

variable "target_region" {
  type        = string
  default     = "ap-southeast-2"
  description = "Region of the target lambda function"
}

variable "proxy_function_name" {
  type        = string
  description = "Name for the proxy lambda function (will be deployed in us-east-1)"
}

variable "lambda_runtime" {
  type        = string
  default     = "python3.11"
  description = "Runtime for the proxy lambda function"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform      = "True"
    Serverless     = "False"
    Environment    = "prod"
    Stack          = "Application"
    Application    = "Event Handler"
    Squad          = "Core"
    CustomerFacing = "True"
    Product        = "Eyecue"
    System         = "Python3.11"
  }
}
