import json
import os
import boto3

def lambda_handler(event, context):
    # Read environment variables for target function details
    target_region = os.environ.get('TARGET_REGION')
    target_function = os.environ.get('TARGET_FUNCTION')
    aws_account_id = os.environ.get('AWS_ACCOUNT_ID')

    # Build the target function ARN
    target_arn = f"arn:aws:lambda:{target_region}:{aws_account_id}:function:{target_function}"

    # Create a boto3 Lambda client in the target region
    lambda_client = boto3.client('lambda', region_name=target_region)

    # Invoke the target function synchronously, passing the same event
    response = lambda_client.invoke(
        FunctionName=target_arn,
        InvocationType='RequestResponse',
        Payload=json.dumps(event)
    )

    # Read and return the target function’s response
    payload = json.loads(response['Payload'].read())
    return payload
