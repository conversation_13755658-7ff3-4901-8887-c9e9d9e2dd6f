module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 5.0"
  name                          = "eyecue-camera-metrics-exporter"
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}


data "aws_iam_policy_document" "eyecue_camera_metrics_exporter_policy_document" {
  statement {
    sid = "CameraMetricsExporterPolicyDocument"
    actions = [
      "dynamodb:Query",
    ]
    resources = [
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/${var.things_shadow_table_name}",
    ]
  }
}

resource "aws_iam_policy" "eyecue_camera_metrics_exporter_policy" {
  name        = "CameraMetricsExporterPolicy"
  depends_on  = [module.iam_user]
  description = "Enable the Camera Health Check to query the DynamoDB table"
  policy      = data.aws_iam_policy_document.eyecue_camera_metrics_exporter_policy_document.json
}

resource "aws_iam_policy_attachment" "eyecue_camera_metrics_exporter_policy_attachment" {
  name       = "CameraMetricsExporterPolicyAttachment"
  users      = [module.iam_user.iam_user_name]
  policy_arn = aws_iam_policy.eyecue_camera_metrics_exporter_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${module.iam_user.iam_user_name}-credentials"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.iam_user_name,
    "aws_access_key_id"     = module.iam_user.iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
