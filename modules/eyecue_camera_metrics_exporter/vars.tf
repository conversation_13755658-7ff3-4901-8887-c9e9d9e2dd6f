variable "things_shadow_table_name" {
  type        = string
  description = "The name of the DynamoDB table for the things shadow"
  default     = "eyecue-things-shadow"
}

variable "aws_region" {
  type        = string
  description = "AWS region"
}

variable "aws_account_id" {
  type        = string
  description = "AWS account ID"
}

variable "keybase" {
  type    = string
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform      = "True"
    Serverless     = "False"
    Environment    = "prod"
    Stack          = "Application"
    Application    = "eyecue-camera-metrics-exporter"
    Squad          = "Platform"
    CustomerFacing = "False"
    Product        = "Eyecue"
    System         = "Python3.12"
  }
}
