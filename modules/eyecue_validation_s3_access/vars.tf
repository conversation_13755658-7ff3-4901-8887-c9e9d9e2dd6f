variable "aws_iam_user" {
  description = "Name of the IAM User who it depends on"
  type = object({
    name = string,
    arn  = string
  })
}

variable "aws_account_id" {}

variable "aws_region" {
  description = "AWS Region"
}

variable "country" {}

variable "client_name" {}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Core"
  }
}

variable "service_name" {
  description = "Name of the service that will use this module"
}

variable "bucket_arn" {
  description = "Bucket ARN for the bucket to be accessed"
}

variable "bucket_name" {
  description = "Bucket name for the bucket to be accessed"
}

variable "bucket_prefix" {
  description = "Bucket name for the bucket to be accessed"
}

variable "prefix_expiry" {
  description = "Bucket name for the bucket to be accessed"
}
