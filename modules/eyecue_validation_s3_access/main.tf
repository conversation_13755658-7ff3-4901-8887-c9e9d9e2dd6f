data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "AWS"
      identifiers = [var.aws_iam_user.arn]
    }
  }
}

resource "aws_iam_role" "upload_role" {
  name               = "Eyecue${var.service_name}S3UploadRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

data "aws_iam_policy_document" "s3_upload_policy" {
  statement {
    sid = "Eyecue${var.service_name}PolicyDocument"
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:ListBucket",
      "s3:GetObject"
    ]
    resources = [
      "${var.bucket_arn}/${var.bucket_prefix}/*"
    ]
  }
}

resource "aws_iam_role_policy" "upload_role_policy" {
  name   = "Eyecue${var.service_name}S3UploadPolicy"
  role   = aws_iam_role.upload_role.id
  policy = data.aws_iam_policy_document.s3_upload_policy.json
}

resource "aws_iam_user_policy" "assume_role_policy_attachment" {
  name = "Eyecue${var.service_name}AssumeS3RolePolicy"
  user = var.aws_iam_user.name
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "sts:AssumeRole",
        Resource = aws_iam_role.upload_role.arn
      }
    ]
  })
}

data "aws_s3_bucket" "images_bucket" {
  bucket = var.bucket_name
}

resource "aws_s3_bucket_lifecycle_configuration" "bucket_lifecycle" {
  bucket = data.aws_s3_bucket.images_bucket.id
  rule {
    id     = "ExpireImages"
    status = "Enabled"
    filter {
      prefix = var.bucket_prefix
    }
    expiration {
      days = var.prefix_expiry
    }
  }
}