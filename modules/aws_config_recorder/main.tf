# Module: AWS Config Recorder

# Notes:
# - When deploying you may encounter a race condition error: 
#     error Config Service: StartConfigurationRecorder, https response error StatusCode: 400 ...
#     NoAvailableDeliveryChannelException: Delivery channel is not available to start configuration 
#    The `aws_config_delivery_channel.config` resource may take some time to become available after creating.
#    A retry of `terraform apply` should most likely fix the issue.

locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# IAM: AWS Config Role
# =====
resource "aws_iam_role" "config_role" {
  name = "${var.recorder_name}-role"

  description = "AWS Config role for ${var.recorder_name}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "config.amazonaws.com"
        }
      }
    ]
  })
  tags = local.tags
}

resource "aws_iam_role_policy_attachment" "config" {
  role = aws_iam_role.config_role.name
  # AWS Managed Policy for AWS Config
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWS_ConfigRole"
}


# =====
# AWS Config: Configuration recorder
# =====
resource "aws_config_configuration_recorder" "config" {
  name     = var.recorder_name
  role_arn = aws_iam_role.config_role.arn

  # For all supported strategy
  dynamic "recording_group" {
    for_each = var.recording_strategy == "ALL_SUPPORTED_RESOURCE_TYPES" ? [1] : []
    content {
      all_supported                 = true
      include_global_resource_types = var.include_global_resource_types
    }
  }

  # For inclusion strategy
  dynamic "recording_group" {
    for_each = var.recording_strategy == "INCLUSION_BY_RESOURCE_TYPES" ? [1] : []
    content {
      all_supported                 = false
      include_global_resource_types = var.include_global_resource_types
      resource_types                = var.include_resource_types
    }
  }

  # For exclusion strategy
  dynamic "recording_group" {
    for_each = var.recording_strategy == "EXCLUSION_BY_RESOURCE_TYPES" ? [1] : []
    content {
      all_supported                 = false
      include_global_resource_types = var.include_global_resource_types
      exclusion_by_resource_types {
        resource_types = var.exclude_resource_types
      }
      recording_strategy {
        use_only = "EXCLUSION_BY_RESOURCE_TYPES"
      }
    }
  }

  recording_mode {
    recording_frequency = var.recording_frequency
  }

}

resource "aws_config_configuration_recorder_status" "config" {
  name       = aws_config_configuration_recorder.config.name
  is_enabled = var.enable_recording
  depends_on = [aws_config_configuration_recorder.config]
}


# =====
# AWS Config: Delivery Channel
# =====
resource "aws_config_delivery_channel" "config" {
  name           = var.recorder_name
  s3_bucket_name = var.s3_bucket_name
  s3_key_prefix  = var.s3_key_prefix
  sns_topic_arn  = var.sns_topic_arn

  depends_on = [aws_config_configuration_recorder.config]
}
