# =====
# Configuration recorder
# =====
variable "recorder_name" {
  description = "[Optional] Name for the AWS Config Configuration recorder"
  default     = "config-recoder"
  type        = string
}

variable "enable_recording" {
  description = "[Optional] Whether to enable AWS Config configuration recording."
  default     = true
  type        = bool
}

variable "recording_strategy" {
  description = <<-DOC
    [Optional] AWS Config recording strategy.
    
    Allowed values:
      - ALL_SUPPORTED_RESOURCE_TYPES
      - INCLUSION_BY_RESOURCE_TYPES
      - EXCLUSION_BY_RESOURCE_TYPES
    
    For more information see: https://docs.aws.amazon.com/config/latest/APIReference/API_RecordingStrategy.html
  DOC
  type        = string
  default     = "ALL_SUPPORTED_RESOURCE_TYPES"
  validation {
    condition = contains([
      "ALL_SUPPORTED_RESOURCE_TYPES",
      "INCLUSION_BY_RESOURCE_TYPES",
      "EXCLUSION_BY_RESOURCE_TYPES"
    ], var.recording_strategy)
    error_message = "Invalid recording strategy. Must be one of: ALL_SUPPORTED_RESOURCE_TYPES, INCLUSION_BY_RESOURCE_TYPES, or EXCLUSION_BY_RESOURCE_TYPES."
  }
}

variable "include_global_resource_types" {
  description = <<-DOC
    [Optional] Specifies whether AWS Config includes all supported types of global resources with the resources that it records.
  DOC
  type        = bool
  default     = false
}

variable "include_resource_types" {
  description = <<-DOC
    [Conditional] List of resource types to include in the configuration recorder.
    Used when `recording_strategy` is set to "INCLUSION_BY_RESOURCE_TYPES".

    For allowed resource types see:
    http://docs.aws.amazon.com/config/latest/APIReference/API_ResourceIdentifier.html#config-Type-ResourceIdentifier-resourceType
  DOC
  type        = list(string)
  default     = []
}

variable "exclude_resource_types" {
  description = <<-DOC
    [Conditional] List of resource types to exclude from the configuration recorder.
    Used when `recording_strategy` is set to "EXCLUSION_BY_RESOURCE_TYPES".

    For allowed resource types see:
    http://docs.aws.amazon.com/config/latest/APIReference/API_ResourceIdentifier.html#config-Type-ResourceIdentifier-resourceType
  DOC
  type        = list(string)
  default     = []
}

variable "recording_frequency" {
  description = <<-DOC
    [Optional] Default recording frequency.
    
    Allowed values:
      - CONTINUOUS
      - DAILY
  DOC
  type        = string
  default     = "CONTINUOUS"
  validation {
    condition = contains([
      "CONTINUOUS",
      "DAILY"
    ], var.recording_frequency)
    error_message = "Invalid recording frequency. Must be one of: CONTINUOUS, or DAILY."
  }
}

# =====
# Configuration delivery channel
# =====
variable "s3_bucket_name" {
  description = "[Required] Name of existing S3 bucket used for AWS Config configuration delivery."
  type        = string
}

variable "s3_key_prefix" {
  description = "[Optional] S3 key prefix used for AWS Config configuration delivery."
  type        = string
  default     = null
}

variable "sns_topic_arn" {
  description = "[Optional] ARN of existing SNS topic used for AWS Config notifications."
  type        = string
  default     = null
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
