variable "aws_iam_user" {
  description = "Name of the IAM User who it depends on"
}
variable "aws_account_id" {}

variable "aws_region" {
  description = "AWS Region"
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "country" {}

variable "client_name" {}

variable "s3_bucket_name" {
  default = "benchmarker"
}


variable "enable_versioning" {
  description = "Defines if the S3 bucket will version the objects"
  default     = false
}

variable "allowed_methods" {
  description = "Which HTTP methods are allowed"
  type        = list(any)
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platforms"
  }
}