resource "aws_dynamodb_table" "table" {
  for_each = var.dynamodb_tables_config

  name                        = each.key
  billing_mode                = "PAY_PER_REQUEST"
  stream_enabled              = each.value.stream_enabled
  stream_view_type            = each.value.stream_view_type
  hash_key                    = each.value.hash_key
  range_key                   = each.value.range_key
  deletion_protection_enabled = each.value.deletion_protection_enabled

  attribute {
    name = each.value.hash_key
    type = "S"
  }

  dynamic "attribute" {
    for_each = each.value.range_key != null ? [each.value.range_key] : []
    content {
      name = attribute.value
      type = "S"
    }
  }

  dynamic "global_secondary_index" {
    for_each = each.value.gsi
    iterator = gsi
    content {
      name            = gsi.key
      hash_key        = gsi.value.hash_key
      range_key       = gsi.value.range_key
      projection_type = "ALL"
    }
  }

  dynamic "ttl" {
    for_each = each.value.ttl != null ? [each.value.ttl] : []
    iterator = ttl
    content {
      enabled        = ttl.value.enabled
      attribute_name = ttl.value.attribute_name
    }
  }

  tags = each.value.tags

  point_in_time_recovery {
    enabled = each.value.point_in_time_recovery
  }

  server_side_encryption {
    enabled = true
  }
}
