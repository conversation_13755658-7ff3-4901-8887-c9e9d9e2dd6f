locals {
  dynamodb_gsi_names = {
    for k, v in aws_dynamodb_table.table : v.name => [
      for gsi in v.global_secondary_index : gsi.name
    ]
  }
  dynamodb_gsi_names_flattened_list = flatten([
    for k, v in local.dynamodb_gsi_names : [
      for gsi in v : {
        table_name = k
        index_name = gsi
      }
    ]
  ])
}

output "dynamodb_tables_ids" {
  value = { for k, v in aws_dynamodb_table.table : k => v.id }
}

output "dynamodb_gsi_names" {
  description = "Map where keys are the table names and values are a list of the table's global secondary index names."
  value       = local.dynamodb_gsi_names
}

output "dynamodb_gsi_names_flattened_list" {
  description = "Flattened list of maps containing the DynamoDB table `table_name` and global secondary index `index_name`."
  value       = local.dynamodb_gsi_names_flattened_list
}
