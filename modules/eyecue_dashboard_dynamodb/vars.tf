variable "dynamodb_tables_config" {
  description = "Configuration for each DynamoDB table"
  type = map(object({
    hash_key : string
    range_key : string
    stream_enabled : bool
    stream_view_type : string
    tags : map(string)
    point_in_time_recovery : bool
    deletion_protection_enabled : bool
    ttl : optional(object({
      attribute_name : string
      enabled : bool
    }), null)
    gsi : optional(map(object({
      hash_key : string
      range_key : optional(string)
    })), {})
  }))
  default = {}
}
