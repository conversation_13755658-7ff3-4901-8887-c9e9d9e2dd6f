
resource "aws_iam_user" "user" {
  name = var.iam_user_name
  tags = var.tags
}

resource "aws_iam_policy" "user" {
  count  = var.create_user_policy ? 1 : 0
  name   = var.iam_user_policyname
  policy = var.iam_user_policy
}

resource "aws_iam_user_policy_attachment" "user" {
  count      = var.create_user_policy ? 1 : 0
  user       = aws_iam_user.user.name
  policy_arn = aws_iam_policy.user[0].arn
}
