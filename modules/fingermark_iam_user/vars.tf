variable "iam_user_name" {
  type        = string
  description = "Name of the IAM User"
}

variable "iam_user_policyname" {
  type        = string
  description = "Name of the IAM User Policy"
}

variable "create_user_policy" {
  description = "Create User Policy Check"
  type        = bool
  default     = true
}

variable "iam_user_policy" {
  type        = string
  description = "IAM User Policy"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}


