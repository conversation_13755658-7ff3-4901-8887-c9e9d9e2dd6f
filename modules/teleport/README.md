# Teleport Module

This Terraform module deploys a secure Teleport instance on AWS with Application Load Balancer (ALB) termination, encrypted storage, and comprehensive automation support.

## Features

- **🔒 Secure by Default**: SSM access, encrypted volumes, IMDSv2 enforcement
- **🌐 Load Balancer Integration**: ALB with SSL termination and health checks
- **📦 Persistent Storage**: Encrypted EBS volume for Teleport data
- **🤖 Ansible Ready**: S3 bucket for automation via AWS Systems Manager
- **🔑 Flexible Access**: Optional SSH access with SSM as primary method
- **📊 Comprehensive Monitoring**: CloudWatch integration and health checks

## Architecture

```
Internet → ALB (HTTPS:443) → EC2 Instance (HTTPS:8443) → Teleport
                                ↓
                           EBS Data Volume
                                ↓
                           S3 Ansible Bucket
```

## Quick Start

```hcl
module "teleport" {
  source = "../../../modules/teleport"

  name                = "teleport-prod"
  vpc_id              = "vpc-********"
  public_subnet_ids   = ["subnet-********", "subnet-********"]
  private_subnet_ids  = ["subnet-abcdef12", "subnet-21fedcba"]
  alb_certificate_arn = "arn:aws:acm:region:account:certificate/cert-id"

  # Instance configuration
  instance_type    = "t3.medium"
  root_volume_size = 20
  data_volume_size = 50

  # Security configuration
  enable_ssm        = true
  enable_ssh_access = false  # SSH disabled by default
  
  # Ansible automation
  create_ansible_bucket = true
  ansible_bucket_name   = "my-teleport-ansible"

  tags = {
    Environment = "production"
    Project     = "teleport"
  }
}
```

## Required Variables

| Variable | Type | Description |
|----------|------|-------------|
| `name` | string | Name prefix for all resources |
| `vpc_id` | string | VPC ID where resources will be created |
| `public_subnet_ids` | list(string) | Public subnet IDs for ALB placement |
| `private_subnet_ids` | list(string) | Private subnet IDs for EC2 placement |
| `alb_certificate_arn` | string | ACM certificate ARN for HTTPS termination |

## Optional Variables

### Instance Configuration
| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `instance_type` | string | `"t3.medium"` | EC2 instance type |
| `root_volume_size` | number | `20` | Size of root EBS volume (GB) |
| `data_volume_size` | number | `50` | Size of data EBS volume (GB) |

### Security Configuration
| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `enable_ssm` | bool | `true` | Enable AWS Systems Manager access |
| `enable_ssh_access` | bool | `false` | Enable SSH access (SSM recommended) |
| `key_name` | string | `null` | EC2 Key Pair name (only used if SSH enabled) |
| `ssh_cidr_blocks` | list(string) | `[]` | CIDR blocks for SSH access |

### Ansible Automation
| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `create_ansible_bucket` | bool | `true` | Create S3 bucket for Ansible automation |
| `ansible_bucket_name` | string | `null` | Custom bucket name (auto-generated if null) |

### Application Configuration
| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `teleport_web_port` | number | `8443` | Teleport web interface port |
| `health_check_path` | string | `"/web/login"` | ALB health check path |
| `health_check_interval` | number | `30` | Health check interval (seconds) |

## Outputs

### ALB Outputs
- `alb_dns_name` - ALB DNS name for CNAME records
- `alb_arn` - ALB ARN for additional configuration
- `alb_zone_id` - ALB hosted zone ID for Route53 alias records

### Instance Outputs
- `instance_id` - EC2 instance ID
- `instance_private_ip` - Private IP address
- `ssm_connect_command` - Command to connect via SSM

### Security Outputs
- `ssh_access_enabled` - Whether SSH access is enabled
- `security_group_ids` - Security group IDs (ALB and EC2)

### S3 Outputs
- `ansible_bucket_name` - Ansible automation bucket name
- `ansible_bucket_arn` - Ansible automation bucket ARN

## Access Methods

### Primary: AWS Systems Manager (Recommended)
```bash
# Connect to instance via SSM
aws ssm start-session --target i-********90abcdef0

# Run Ansible via SSM
aws ssm send-command \
  --instance-ids "i-********90abcdef0" \
  --document-name "AWS-RunAnsiblePlaybook" \
  --parameters '{"playbookurl":"s3://bucket/playbook.yml"}'
```

### Backup: SSH Access (Optional)
```bash
# Only available if enable_ssh_access = true
ssh -i ~/.ssh/key.pem ubuntu@<private-ip>
```

## Security Features

- **🔐 Encrypted Storage**: All EBS volumes encrypted at rest
- **🛡️ IMDSv2 Enforced**: Instance metadata service v2 required
- **🚫 No Public IP**: Instance deployed in private subnet
- **🔒 Minimal Permissions**: Least privilege IAM policies
- **🌐 SSL Termination**: HTTPS only via ALB
- **🔥 Firewall Rules**: UFW configured in userdata

## Ansible Integration

The module creates an S3 bucket specifically for Ansible automation:

1. **Bucket Purpose**: Required by Ansible to store temporary files when connecting via SSM
2. **IAM Permissions**: EC2 instance has read/write access to the bucket
3. **SSM Integration**: Ansible can run via AWS Systems Manager
4. **Security**: Bucket is private with encryption enabled

## Monitoring

- **ALB Health Checks**: Monitors Teleport web interface
- **CloudWatch Integration**: Instance and application metrics
- **Status Logging**: Comprehensive initialization logging

## Example: Production Deployment

```hcl
module "teleport_prod" {
  source = "../../../modules/teleport"

  name                = "teleport-prod"
  vpc_id              = module.vpc.vpc_id
  public_subnet_ids   = module.vpc.public_subnets
  private_subnet_ids  = module.vpc.private_subnets
  alb_certificate_arn = module.acm.certificate_arn

  # Production sizing
  instance_type    = "t3.large"
  root_volume_size = 30
  data_volume_size = 100

  # Security hardened
  enable_ssm        = true
  enable_ssh_access = false
  ssh_cidr_blocks   = []

  # Ansible automation
  create_ansible_bucket = true
  ansible_bucket_name   = "teleport-prod-ansible-automation"

  # Health check tuning
  health_check_interval           = 15
  health_check_timeout           = 10
  health_check_healthy_threshold = 2

  tags = {
    Environment = "production"
    Project     = "teleport"
    Backup      = "true"
  }
}
```

## Troubleshooting

### Instance Not Healthy
1. Check security groups allow ALB → EC2 communication
2. Verify Teleport is running on port 8443
3. Check `/var/log/teleport-init.log` for initialization errors

### SSM Connection Issues
1. Verify `enable_ssm = true`
2. Check IAM instance profile is attached
3. Ensure SSM agent is running: `sudo snap services amazon-ssm-agent`

### Ansible S3 Access Issues
1. Verify `create_ansible_bucket = true`
2. Check IAM permissions for S3 bucket access
3. Test bucket access: `aws s3 ls s3://bucket-name`
