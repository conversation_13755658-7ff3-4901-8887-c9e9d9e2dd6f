#!/bin/bash

# Teleport EC2 Instance Initialization Script
# This script prepares the instance for Teleport installation and Ansible configuration

# Exit on error, but allow some commands to fail gracefully
set -e

# Redirect all output to log file and console
exec > >(tee -a /var/log/teleport-init.log)
exec 2>&1

# Variables
REGION="${region}"
LOG_FILE="/var/log/teleport-init.log"
DATA_DEVICE="/dev/nvme1n1"  # NVMe naming for modern instances
DATA_MOUNT_POINT="/opt/teleport"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting Teleport instance initialization"

# Update system packages
log "Updating system packages"
apt-get update -y
apt-get upgrade -y

# Install essential packages
log "Installing essential packages"
apt-get install -y \
    curl \
    wget \
    unzip \
    jq \
    awscli \
    python3 \
    python3-pip \
    htop \
    vim \
    git \
    ca-certificates \
    gnupg \
    lsb-release

# Disable IPv6 (as per Teleport best practices)
log "Disabling IPv6"
echo 'net.ipv6.conf.all.disable_ipv6 = 1' >> /etc/sysctl.conf
echo 'net.ipv6.conf.default.disable_ipv6 = 1' >> /etc/sysctl.conf
echo 'net.ipv6.conf.lo.disable_ipv6 = 1' >> /etc/sysctl.conf
sysctl -p

# Wait for EBS volume to be available and mount it
log "Waiting for EBS data volume to be available"
while [ ! -e "$DATA_DEVICE" ]; do
    log "Waiting for $DATA_DEVICE to be available..."
    sleep 5
done

# Check if the volume is already formatted
if ! blkid "$DATA_DEVICE"; then
    log "Formatting EBS data volume"
    mkfs.ext4 "$DATA_DEVICE"
else
    log "EBS data volume already formatted"
fi

# Create mount point and mount the volume
log "Creating mount point and mounting EBS data volume"
mkdir -p "$DATA_MOUNT_POINT"
mount "$DATA_DEVICE" "$DATA_MOUNT_POINT"

# Add to fstab for persistent mounting
UUID=$(blkid -s UUID -o value "$DATA_DEVICE")
echo "UUID=$UUID $DATA_MOUNT_POINT ext4 defaults,nofail 0 2" >> /etc/fstab

# Set proper permissions
chown root:root "$DATA_MOUNT_POINT"
chmod 755 "$DATA_MOUNT_POINT"

# Create directories for Teleport
log "Creating Teleport directories"
mkdir -p "$DATA_MOUNT_POINT"/{data,config,certs,logs}
chmod 700 "$DATA_MOUNT_POINT"/data
chmod 755 "$DATA_MOUNT_POINT"/{config,certs,logs}

# Install AWS SSM Agent (should already be installed on Ubuntu 22.04 AMI)
log "Ensuring AWS SSM Agent is installed and running"
# Check if SSM agent is installed as snap (Ubuntu 22.04 default)
if snap list amazon-ssm-agent >/dev/null 2>&1; then
    log "SSM Agent already installed as snap package"
    # SSM agent as snap doesn't use systemctl, it's managed by snapd
    snap services amazon-ssm-agent
elif ! systemctl is-active --quiet amazon-ssm-agent; then
    log "Installing SSM Agent as snap package"
    snap install amazon-ssm-agent --classic
else
    log "SSM Agent already running as systemd service"
fi

# Create teleport user for service
log "Creating teleport user"
useradd --system --home-dir "$DATA_MOUNT_POINT" --shell /bin/false teleport || true
chown -R teleport:teleport "$DATA_MOUNT_POINT"

# Install Docker (for potential containerized Teleport deployment)
log "Installing Docker"
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt-get update -y
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Enable and start Docker
systemctl enable docker
systemctl start docker

# Add teleport user to docker group
usermod -aG docker teleport

# Configure log rotation for Teleport logs
log "Configuring log rotation"
cat > /etc/logrotate.d/teleport << EOF
$DATA_MOUNT_POINT/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 teleport teleport
    postrotate
        systemctl reload teleport || true
    endscript
}
EOF

# Create a marker file to indicate initialization is complete
log "Creating initialization marker file"
touch "$DATA_MOUNT_POINT/.initialized"
echo "$(date)" > "$DATA_MOUNT_POINT/.initialized"

# Set up basic firewall rules (UFW)
log "Configuring basic firewall rules"
ufw --force enable
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 8443/tcp  # Teleport web interface
ufw allow 3022/tcp  # Teleport SSH
ufw allow 3023/tcp  # Teleport proxy
ufw allow 3024/tcp  # Teleport tunnel
ufw allow 3025/tcp  # Teleport auth

# Register with SSM (should happen automatically, but ensure it's working)
log "Ensuring SSM registration"
# SSM agent is running as snap, no need to restart systemd service
if snap services amazon-ssm-agent | grep -q "active"; then
    log "SSM agent is active and running"
else
    log "SSM agent status unknown, but snap is installed"
fi

# Create Ansible readiness marker
log "Creating Ansible readiness marker"
cat > /opt/teleport/.ansible-ready << 'EOF'
# This file indicates the instance is ready for Ansible configuration
# Infrastructure setup completed by Terraform user data script

READY_FOR_ANSIBLE=true
INFRASTRUCTURE_SETUP_COMPLETE=true
DOCKER_AVAILABLE=true
TELEPORT_USER_CREATED=true
DATA_VOLUME_MOUNTED=true
FIREWALL_CONFIGURED=true
EOF

chmod 644 /opt/teleport/.ansible-ready
chown teleport:teleport /opt/teleport/.ansible-ready

# Final system optimization
log "Performing final system optimization"
# Increase file descriptor limits for Teleport
echo "teleport soft nofile 65536" >> /etc/security/limits.conf
echo "teleport hard nofile 65536" >> /etc/security/limits.conf

# Optimize network settings for Teleport
cat >> /etc/sysctl.conf << EOF
# Network optimizations for Teleport
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.core.netdev_max_backlog = 5000
EOF

sysctl -p

log "Teleport instance initialization completed successfully"
log "Instance is ready for Ansible configuration"

# Create a comprehensive status file
cat > "$DATA_MOUNT_POINT/.status" << EOF
INITIALIZATION_COMPLETE=true
COMPLETION_TIME=$(date)
DOCKER_INSTALLED=$(systemctl is-active docker 2>/dev/null || echo "false")
SSM_AGENT_STATUS=$(snap services amazon-ssm-agent 2>/dev/null | grep -q "active" && echo "active" || echo "unknown")
DATA_VOLUME_MOUNTED=$(df $DATA_MOUNT_POINT | grep -q nvme && echo "true" || echo "false")
TELEPORT_USER_CREATED=$(id teleport >/dev/null 2>&1 && echo "true" || echo "false")
USERDATA_SCRIPT_VERSION=v2.0
EOF

# Set proper ownership for status file
chown teleport:teleport "$DATA_MOUNT_POINT/.status"

log "Status file created at $DATA_MOUNT_POINT/.status"

# Signal completion to CloudFormation/Terraform if needed
# This can be extended to send signals to external systems

exit 0
