# ===============================================
# Required Variables
# ===============================================

variable "name" {
  description = "Name prefix for all resources"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where resources will be created"
  type        = string
}

variable "public_subnet_ids" {
  description = "Public subnet IDs for ALB placement"
  type        = list(string)
}

variable "private_subnet_ids" {
  description = "Private subnet IDs for EC2 placement"
  type        = list(string)
}

variable "alb_certificate_arn" {
  description = "ACM certificate ARN for HTTPS termination"
  type        = string
}

# ===============================================
# Optional Variables
# ===============================================

variable "ssh_cidr_blocks" {
  description = "CIDR blocks allowed SSH access to EC2 instance"
  type        = list(string)
  default     = []
}

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.medium"
}

variable "root_volume_size" {
  description = "Size of the root EBS volume in GB"
  type        = number
  default     = 20
}

variable "data_volume_size" {
  description = "Size of the data EBS volume in GB"
  type        = number
  default     = 50
}

variable "key_name" {
  description = "EC2 Key Pair name for SSH access (optional - only used if enable_ssh_access is true)"
  type        = string
  default     = null
}

variable "enable_ssh_access" {
  description = "Enable SSH access to the EC2 instance. When false, only SSM access is available"
  type        = bool
  default     = false
}

variable "enable_ssm" {
  description = "Enable AWS Systems Manager (SSM) access for the instance"
  type        = bool
  default     = true
}

variable "health_check_path" {
  description = "Health check path for ALB target group"
  type        = string
  default     = "/web/login"
}

variable "teleport_web_port" {
  description = "Port for Teleport web interface (non-privileged port)"
  type        = number
  default     = 8443
}

variable "health_check_interval" {
  description = "Health check interval in seconds"
  type        = number
  default     = 30
}

variable "health_check_timeout" {
  description = "Health check timeout in seconds"
  type        = number
  default     = 5
}

variable "health_check_healthy_threshold" {
  description = "Number of consecutive health checks successes required before considering an unhealthy target healthy"
  type        = number
  default     = 2
}

variable "health_check_unhealthy_threshold" {
  description = "Number of consecutive health check failures required before considering a target unhealthy"
  type        = number
  default     = 3
}

# ===============================================
# Ansible Automation Variables
# ===============================================

variable "create_ansible_bucket" {
  description = "Create S3 bucket for Ansible automation via SSM"
  type        = bool
  default     = true
}

variable "ansible_bucket_name" {
  description = "Name for the S3 bucket used by Ansible for automation. If not provided, will be auto-generated"
  type        = string
  default     = null
}

# ===============================================
# Tagging Variables
# ===============================================

variable "tags" {
  description = "A map of tags to assign to the resources"
  type        = map(string)
  default     = {}
}

variable "default_tags" {
  description = "Default tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform = "true"
  }
}
