# ===============================================
# Data Sources
# ===============================================

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Get the latest Ubuntu 22.04 LTS AMI with SSM agent pre-installed
data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# ===============================================
# Local Values
# ===============================================

locals {
  common_tags = merge(var.default_tags, var.tags, {
    Name        = var.name
    Environment = lookup(var.tags, "Environment", "unknown")
    Product     = lookup(var.tags, "Product", "teleport")
    Project     = lookup(var.tags, "Project", "teleport-infrastructure")
    Squad       = lookup(var.tags, "Squad", "platform")
  })

  # User data script for instance initialization
  user_data = base64encode(templatefile("${path.module}/templates/user_data.sh", {
    region      = data.aws_region.current.name
    instance_id = "INSTANCE_ID_PLACEHOLDER" # Will be replaced by cloud-init
  }))
}

# ===============================================
# Security Groups
# ===============================================

# ALB Security Group
resource "aws_security_group" "alb" {
  name        = "${var.name}-alb-sg"
  description = "Security group for Teleport Application Load Balancer"
  vpc_id      = var.vpc_id

  # Allow HTTPS inbound from anywhere
  ingress {
    description = "HTTPS from anywhere"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${var.name}-alb-sg"
  })
}

# EC2 Security Group
resource "aws_security_group" "ec2" {
  name        = "${var.name}-ec2-sg"
  description = "Security group for Teleport EC2 instance"
  vpc_id      = var.vpc_id

  # Allow HTTPS from ALB security group on Teleport port
  ingress {
    description     = "HTTPS from ALB to Teleport"
    from_port       = var.teleport_web_port
    to_port         = var.teleport_web_port
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  # Allow SSH from specified CIDR blocks (if SSH access is enabled and CIDR blocks are provided)
  dynamic "ingress" {
    for_each = var.enable_ssh_access && length(var.ssh_cidr_blocks) > 0 ? [1] : []
    content {
      description = "SSH access"
      from_port   = 22
      to_port     = 22
      protocol    = "tcp"
      cidr_blocks = var.ssh_cidr_blocks
    }
  }

  # Allow all outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${var.name}-ec2-sg"
  })
}

# ===============================================
# IAM Resources
# ===============================================

# IAM role for EC2 instance
resource "aws_iam_role" "teleport" {
  count = var.enable_ssm ? 1 : 0
  name  = "${var.name}-ec2-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

# Attach SSM managed policy
resource "aws_iam_role_policy_attachment" "ssm_managed_instance_core" {
  count      = var.enable_ssm ? 1 : 0
  role       = aws_iam_role.teleport[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# Custom policy for Teleport configuration access
resource "aws_iam_role_policy" "teleport_config" {
  count = var.enable_ssm ? 1 : 0
  name  = "${var.name}-config-policy"
  role  = aws_iam_role.teleport[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath"
        ]
        Resource = [
          "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/teleport/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = [
          "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:teleport/*"
        ]
      }
    ]
  })
}

# S3 policy for Ansible automation bucket access
resource "aws_iam_role_policy" "ansible_s3_access" {
  count = var.enable_ssm && var.create_ansible_bucket ? 1 : 0
  name  = "${var.name}-ansible-s3-policy"
  role  = aws_iam_role.teleport[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket",
          "s3:GetBucketLocation"
        ]
        Resource = [
          aws_s3_bucket.ansible_automation[0].arn,
          "${aws_s3_bucket.ansible_automation[0].arn}/*"
        ]
      }
    ]
  })
}

# Instance profile
resource "aws_iam_instance_profile" "teleport" {
  count = var.enable_ssm ? 1 : 0
  name  = "${var.name}-instance-profile"
  role  = aws_iam_role.teleport[0].name

  tags = local.common_tags
}

# ===============================================
# EBS Volumes
# ===============================================

# Data volume for Teleport data persistence
resource "aws_ebs_volume" "teleport_data" {
  availability_zone = data.aws_availability_zones.available.names[0]
  size              = var.data_volume_size
  type              = "gp3"
  encrypted         = true

  tags = merge(local.common_tags, {
    Name = "${var.name}-data-volume"
  })
}

# ===============================================
# EC2 Instance
# ===============================================

# Get available AZs
data "aws_availability_zones" "available" {
  state = "available"
}

resource "aws_instance" "teleport" {
  ami                    = data.aws_ami.ubuntu.id
  instance_type          = var.instance_type
  subnet_id              = var.private_subnet_ids[0]
  key_name               = var.enable_ssh_access ? var.key_name : null
  vpc_security_group_ids = [aws_security_group.ec2.id]
  iam_instance_profile   = var.enable_ssm ? aws_iam_instance_profile.teleport[0].name : null
  user_data              = local.user_data

  # Enforce IMDSv2
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
  }

  # Encrypted root volume
  root_block_device {
    volume_type           = "gp3"
    volume_size           = var.root_volume_size
    encrypted             = true
    delete_on_termination = true
  }

  # Ensure EBS optimized
  ebs_optimized = true

  tags = merge(local.common_tags, {
    Name = "${var.name}-instance"
  })

  # Ensure instance profile is created before instance
  depends_on = [aws_iam_instance_profile.teleport]
}

# Attach data volume to instance
resource "aws_volume_attachment" "teleport_data" {
  device_name = "/dev/sdf"
  volume_id   = aws_ebs_volume.teleport_data.id
  instance_id = aws_instance.teleport.id
}

# ===============================================
# Application Load Balancer
# ===============================================

resource "aws_lb" "teleport" {
  name               = "${var.name}-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = var.public_subnet_ids

  enable_deletion_protection = false

  tags = merge(local.common_tags, {
    Name = "${var.name}-alb"
  })
}

# Target Group
resource "aws_lb_target_group" "teleport" {
  name     = "${var.name}-tg"
  port     = var.teleport_web_port
  protocol = "HTTPS"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = var.health_check_healthy_threshold
    unhealthy_threshold = var.health_check_unhealthy_threshold
    timeout             = var.health_check_timeout
    interval            = var.health_check_interval
    path                = var.health_check_path
    matcher             = "200"
    port                = "traffic-port"
    protocol            = "HTTPS"
  }

  tags = merge(local.common_tags, {
    Name = "${var.name}-tg"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Target Group Attachment
resource "aws_lb_target_group_attachment" "teleport" {
  target_group_arn = aws_lb_target_group.teleport.arn
  target_id        = aws_instance.teleport.id
  port             = var.teleport_web_port
}

# HTTPS Listener
resource "aws_lb_listener" "teleport_https" {
  load_balancer_arn = aws_lb.teleport.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = var.alb_certificate_arn

  default_action {
    type = "forward"
    forward {
      target_group {
        arn = aws_lb_target_group.teleport.arn
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "${var.name}-https-listener"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# ===============================================
# S3 Bucket for Ansible Automation
# ===============================================

# Generate bucket name if not provided
locals {
  ansible_bucket_name = var.ansible_bucket_name != null ? var.ansible_bucket_name : "${var.name}-ansible-automation"
}

# S3 bucket for Ansible automation (conditional)
resource "aws_s3_bucket" "ansible_automation" {
  count = var.create_ansible_bucket ? 1 : 0

  bucket = local.ansible_bucket_name

  tags = merge(local.common_tags, {
    Name      = "${var.name}-ansible-automation"
    Purpose   = "Ansible automation via SSM"
    Component = "automation"
  })
}

# Block public access to Ansible bucket
resource "aws_s3_bucket_public_access_block" "ansible_automation" {
  count = var.create_ansible_bucket ? 1 : 0

  bucket                  = aws_s3_bucket.ansible_automation[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable encryption for Ansible bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "ansible_automation" {
  count = var.create_ansible_bucket ? 1 : 0

  bucket = aws_s3_bucket.ansible_automation[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Enable versioning for Ansible bucket
resource "aws_s3_bucket_versioning" "ansible_automation" {
  count = var.create_ansible_bucket ? 1 : 0

  bucket = aws_s3_bucket.ansible_automation[0].id
  versioning_configuration {
    status = "Enabled"
  }
}
