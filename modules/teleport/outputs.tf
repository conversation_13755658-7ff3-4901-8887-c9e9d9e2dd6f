# ===============================================
# ALB Outputs
# ===============================================

output "alb_dns_name" {
  description = "ALB DNS name for CNAME record creation"
  value       = aws_lb.teleport.dns_name
}

output "alb_arn" {
  description = "ALB ARN for additional configuration"
  value       = aws_lb.teleport.arn
}

output "alb_zone_id" {
  description = "ALB hosted zone ID for Route53 alias records"
  value       = aws_lb.teleport.zone_id
}

output "target_group_arn" {
  description = "Target group ARN for health monitoring"
  value       = aws_lb_target_group.teleport.arn
}

# ===============================================
# EC2 Outputs
# ===============================================

output "instance_id" {
  description = "EC2 instance ID for management"
  value       = aws_instance.teleport.id
}

output "instance_private_ip" {
  description = "Private IP address of the EC2 instance"
  value       = aws_instance.teleport.private_ip
}

output "instance_public_ip" {
  description = "Public IP address of the EC2 instance (if in public subnet)"
  value       = aws_instance.teleport.public_ip
}

# ===============================================
# Security Group Outputs
# ===============================================

output "security_group_ids" {
  description = "Security group IDs (ALB and EC2)"
  value       = [aws_security_group.alb.id, aws_security_group.ec2.id]
}

output "alb_security_group_id" {
  description = "ALB security group ID"
  value       = aws_security_group.alb.id
}

output "ec2_security_group_id" {
  description = "EC2 security group ID"
  value       = aws_security_group.ec2.id
}

# ===============================================
# IAM Outputs
# ===============================================

output "iam_role_arn" {
  description = "IAM role ARN attached to the EC2 instance"
  value       = var.enable_ssm ? aws_iam_role.teleport[0].arn : null
}

output "iam_role_name" {
  description = "IAM role name attached to the EC2 instance"
  value       = var.enable_ssm ? aws_iam_role.teleport[0].name : null
}

output "iam_instance_profile_name" {
  description = "IAM instance profile name"
  value       = var.enable_ssm ? aws_iam_instance_profile.teleport[0].name : null
}

# ===============================================
# EBS Volume Outputs
# ===============================================

output "data_volume_id" {
  description = "EBS data volume ID"
  value       = aws_ebs_volume.teleport_data.id
}

output "data_volume_device_name" {
  description = "Device name for the EBS data volume"
  value       = "/dev/sdf"
}

# ===============================================
# SSM Outputs
# ===============================================

output "ssm_connect_command" {
  description = "AWS SSM command to connect to the instance"
  value       = var.enable_ssm ? "aws ssm start-session --target ${aws_instance.teleport.id}" : "SSM not enabled"
}

output "ssh_access_enabled" {
  description = "Whether SSH access is enabled for the instance"
  value       = var.enable_ssh_access
}

output "ssh_key_name" {
  description = "SSH key name used for the instance (if SSH access is enabled)"
  value       = var.enable_ssh_access ? var.key_name : "SSH access disabled"
}

output "teleport_web_port" {
  description = "Port where Teleport web interface is running"
  value       = var.teleport_web_port
}

# ===============================================
# S3 Bucket Outputs
# ===============================================

output "ansible_bucket_name" {
  description = "Name of the S3 bucket for Ansible automation"
  value       = var.create_ansible_bucket ? aws_s3_bucket.ansible_automation[0].bucket : null
}

output "ansible_bucket_arn" {
  description = "ARN of the S3 bucket for Ansible automation"
  value       = var.create_ansible_bucket ? aws_s3_bucket.ansible_automation[0].arn : null
}

output "ansible_bucket_region" {
  description = "Region of the S3 bucket for Ansible automation"
  value       = var.create_ansible_bucket ? aws_s3_bucket.ansible_automation[0].region : null
}
