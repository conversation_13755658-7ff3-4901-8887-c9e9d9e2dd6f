variable "release_name" {
  description = "The name of the Helm release."
  type        = string
  default     = "aws-load-balancer-controller"
}

variable "chart_version" {
  description = "The version of the aws-load-balancer-controller chart to deploy."
  type        = string
  default     = "1.11.0"
}

variable "namespace" {
  description = "The Kubernetes namespace in which to deploy VictoriaMetrics."
  type        = string
  default     = "kube-system"
}

variable "create_namespace" {
  description = "Whether to create the namespace if it does not exist."
  type        = bool
  default     = true
}

# ==================================================================

variable "eks_cluster_name" {
  description = "The name of the EKS cluster."
  type        = string
}

variable "lb_controller_irsa_role_arn" {
  description = "The ARN of the IAM role for the AWS Load Balancer Controller."
  type        = string
}

variable "service_account_name" {
  description = "The name of the service account for the AWS Load Balancer Controller."
  type        = string
  default     = "aws-load-balancer-controller"
}

variable "service_account_enabled" {
  description = "Whether to create the service account."
  type        = bool
  default     = true
}
