# =====
# Tags: Consolidated
# =====
locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# Read: Identity and organization
# =====
data "aws_caller_identity" "current" {}

data "aws_organizations_organization" "current" {}

data "aws_region" "current" {}


# =====
# KMS: SNS topic encryption
# =====
resource "aws_kms_key" "sns_encryption_key" {
  description         = "KMS key for CloudWatch Alarms SNS topic encryption, ${var.sns_topic_name}"
  enable_key_rotation = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowAccountIAMAccess"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "AllowSNS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action = [
          "kms:GenerateDataKey*",
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:ReEncrypt*"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" : "arn:aws:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"
          }
        }
      },
      ## The below statement that uses the `aws:PrincipalOrgID` condition doesn't work. It seems 
      ## when CloudWatch uses KMS, the event doesn't have the AWS Organization ID. We can do 
      ## without this because the SNS topic policy is already restricting access by AWS
      ## Organization ID.
      # {
      #   Sid       = "AllowOrganization"
      #   Effect    = "Allow"
      #   Principal = "*"
      #   Action = [
      #     "kms:GenerateDataKey*",
      #     "kms:Encrypt",
      #   ]
      #   Resource = "*"
      #   Condition = {
      #     StringEquals = {
      #       "aws:PrincipalOrgID" : data.aws_organizations_organization.current.id
      #     }
      #   }
      # },
      {
        Sid    = "AllowCloudWatch"
        Effect = "Allow"
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        }
        Action = [
          "kms:GenerateDataKey*",
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:ReEncrypt*"
        ]
        Resource = "*"
      },
    ]
  })

  tags = local.tags
}

resource "aws_kms_alias" "sns_encryption_key_alias" {
  name          = var.kms_alias_name
  target_key_id = aws_kms_key.sns_encryption_key.key_id
}

# =====
# SNS Topic: CloudWatch Alarms
# =====
resource "aws_sns_topic" "cloudwatch_alarms" {
  name              = var.sns_topic_name
  kms_master_key_id = aws_kms_alias.sns_encryption_key_alias.id

  tags = merge(
    local.tags,
    {
      Name = var.sns_topic_name
    }
  )
}

resource "aws_sns_topic_policy" "cloudwatch_alarms_policy" {
  arn = aws_sns_topic.cloudwatch_alarms.arn
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowOrganizationCloudWatchAccess"
        Effect = "Allow"
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        }
        Action = [
          "SNS:Subscribe",
          "SNS:Publish",
          "SNS:GetTopicAttributes",
          "SNS:ListSubscriptionsByTopic"
        ]
        Resource = aws_sns_topic.cloudwatch_alarms.arn
        Condition = {
          StringEquals = {
            "aws:SourceOrgID" : data.aws_organizations_organization.current.id
          }
        }
      }
    ]
  })
}

resource "aws_sns_topic_subscription" "cloudwatch_alarms" {
  for_each  = toset(var.notify_emails)
  topic_arn = aws_sns_topic.cloudwatch_alarms.arn
  protocol  = "email"
  endpoint  = each.value
}
