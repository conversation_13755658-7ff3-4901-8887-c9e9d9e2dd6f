# =====
# KMS: SNS topic encryption
# =====
variable "kms_alias_name" {
  description = "[Optional] Alias name for the KMS key used to encrypt the CloudWatch Alarm notificaitons SNS topic. Must start with 'alias/'."
  default     = "alias/cloudwatch-alarms-sns-encryption-key"
  validation {
    condition     = startswith(var.kms_alias_name, "alias/")
    error_message = "KMS alias name must start with 'alias/'."
  }
}

# =====
# SNS Topic: CloudWatch Alarms
# =====
variable "sns_topic_name" {
  description = "[Optional] Name for SNS topic used to send CloudWatch Alarm notifications from accounts within your AWS Organization."
  type        = string
  default     = "cloudwatch-alarms-org"
}

variable "notify_emails" {
  description = "[Optional] List of email addresses to notify for CloudWatch Alarms."
  type        = list(string)
  default     = []
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
