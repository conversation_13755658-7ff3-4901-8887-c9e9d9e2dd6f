# Module CloudWatch Alarms Notifications SNS Topic

Creates a SNS topic that can be used to send CloudWatch alarm notifications. CloudWatch Alarms in any account within the AWS Organization can use the SNS topic, so long as the CloudWatch Alarms are in the same region as the SNS topic.

It is suggested to create one SNS topic for each region and have all CloudWatch alarms across your accounts use the respective region SNS topic.

## Example usage

```hcl
module "cw_alarms_notifications_sns_topic_ap_southeast_2" {
  providers = {
    aws = aws.ap-southeast-2
  }
  source        = "./modules/cw_alarm_notifications_sns_topic"
  notify_emails = ["<EMAIL>"]
}

module "cw_alarms_notifications_sns_topic_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source        = "./modules/cw_alarm_notifications_sns_topic"
  notify_emails = ["<EMAIL>"]
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  providers = {
    aws = aws.ap-southeast-2
  }
  source = "./modules/ec2_instance_cw_alarms"

  cw_alarm_config_ec2_by_tags_cpu_util_high = { MyEc2 = { instance_tags = { Name = "MyEc2" } } }

  sns_topic_arns = [
    module.cw_alarms_notifications_sns_topic_ap_southeast_2.sns_topic_arn
  ]
}

module "ec2_instance_cw_alarms_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source = "./modules/ec2_instance_cw_alarms"

  cw_alarm_config_ec2_by_tags_cpu_util_high = { YourEc2 = { instance_tags = { Name = "YourEc2" } } }

  sns_topic_arns = [
    module.cw_alarms_notifications_sns_topic_us_east_1.sns_topic_arn
  ]
}
```
