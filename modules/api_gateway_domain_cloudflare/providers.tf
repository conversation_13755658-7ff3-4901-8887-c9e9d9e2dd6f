terraform {
  required_providers {
    aws = {
      source                = "hashicorp/aws"
      version               = ">= 3.0.0"
      configuration_aliases = [aws.us_east_1]
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = ">= 3.0.0"
    }
  }
}

provider "cloudflare" {
  email   = var.cloudflare_api_email
  api_key = data.vault_generic_secret.cloudflare.data["api_key"]
}
