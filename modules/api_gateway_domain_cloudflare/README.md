# API Gateway Domain (Cloudflare)

This module automatically creates a CNAME record in Cloudflare for the verification of a new AWS Certificate and then uses this to register a Custom Domain in API Gateway

> Warning: You MUST provide a US-EAST-1 AWS provider with alias `us_east_1` for EDGE optimised domains!

```tf
module "heartbeat_api_gateway_domain_cloudflare" {
  source             = "../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "stg.api.eyecue-heartbeat-service.eyecue.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "EDGE"
  tags               = var.tags
  providers = {
    aws           = aws
    aws.us_east_1 = aws.us_e1 # Must use us-east-1 for Edge-optimized API Gateway
  }
}
```

For REGIONAL, you must also provider `aws.us_east_1` as you cannot have optional providers, but you can just set this to default, e.g

```tf
    ...
    providers = { aws.us_east_1 = aws, aws = aws }
    ...
```