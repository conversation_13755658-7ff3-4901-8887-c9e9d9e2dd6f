locals {
  is_edge_endpoint = var.endpoint_type == "EDGE"
}

# For EDGE endpoint - only created when using EDGE endpoint type
resource "aws_acm_certificate" "us_east_1" {
  count = local.is_edge_endpoint ? 1 : 0

  provider = aws.us_east_1

  domain_name               = var.domain_name
  validation_method         = "DNS"
  subject_alternative_names = var.subject_alternative_names
  tags                      = var.tags
}

# For REGIONAL endpoint - only created when using REGIONAL endpoint type
resource "aws_acm_certificate" "regional" {
  count = local.is_edge_endpoint ? 0 : 1

  domain_name               = var.domain_name
  validation_method         = "DNS"
  subject_alternative_names = var.subject_alternative_names
  tags                      = var.tags
}

# For each domain validation option provided by ACM for us-east-1 (EDGE)
resource "cloudflare_record" "validation_us_east_1" {
  for_each = local.is_edge_endpoint ? {
    for dvo in aws_acm_certificate.us_east_1[0].domain_validation_options : dvo.domain_name => {
      name  = dvo.resource_record_name
      type  = dvo.resource_record_type
      value = dvo.resource_record_value
    }
  } : {}

  zone_id = var.cloudflare_zone_id
  name    = each.value.name
  type    = each.value.type
  value   = each.value.value
  ttl     = try(var.cloudflare_record_ttl, 60)
}

# For each domain validation option provided by ACM for regional
resource "cloudflare_record" "validation_regional" {
  for_each = local.is_edge_endpoint ? {} : {
    for dvo in aws_acm_certificate.regional[0].domain_validation_options : dvo.domain_name => {
      name  = dvo.resource_record_name
      type  = dvo.resource_record_type
      value = dvo.resource_record_value
    }
  }

  zone_id = var.cloudflare_zone_id
  name    = each.value.name
  type    = each.value.type
  value   = each.value.value
  ttl     = try(var.cloudflare_record_ttl, 60)
}

# Wait for AWS to validate the us-east-1 certificate
resource "aws_acm_certificate_validation" "us_east_1" {
  count = local.is_edge_endpoint ? 1 : 0

  provider        = aws.us_east_1
  certificate_arn = aws_acm_certificate.us_east_1[0].arn
  validation_record_fqdns = [
    for rec in cloudflare_record.validation_us_east_1 : rec.name
  ]
  depends_on = [cloudflare_record.validation_us_east_1]
}

# Wait for AWS to validate the regional certificate
resource "aws_acm_certificate_validation" "regional" {
  count = local.is_edge_endpoint ? 0 : 1

  certificate_arn = aws_acm_certificate.regional[0].arn
  validation_record_fqdns = [
    for rec in cloudflare_record.validation_regional : rec.name
  ]
  depends_on = [cloudflare_record.validation_regional]
}

resource "aws_api_gateway_domain_name" "this" {
  domain_name = var.domain_name

  endpoint_configuration {
    types = [var.endpoint_type]
  }

  # Which certificate attribute matters depends on the endpoint type
  certificate_arn          = local.is_edge_endpoint ? aws_acm_certificate_validation.us_east_1[0].certificate_arn : null
  regional_certificate_arn = !local.is_edge_endpoint ? aws_acm_certificate_validation.regional[0].certificate_arn : null
}
