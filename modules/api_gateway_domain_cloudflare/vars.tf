variable "domain_name" {
  description = "The domain name for the API Gateway custom domain"
  type        = string
}

variable "subject_alternative_names" {
  description = "Additional domain names to include in the certificate"
  type        = list(string)
  default     = []
}

variable "endpoint_type" {
  description = "The endpoint type of the API Gateway domain (EDGE or REGIONAL)"
  type        = string
  validation {
    condition     = contains(["EDGE", "REGIONAL"], var.endpoint_type)
    error_message = "endpoint_type must be either EDGE or REGIONAL"
  }
}

variable "tags" {
  description = "Tags to apply to created resources"
  type        = map(string)
  default     = {}
}

variable "cloudflare_zone_id" {
  description = "The Cloudflare Zone ID to create DNS validation records in"
  type        = string
}

variable "cloudflare_api_email" {
  type    = string
  default = "<EMAIL>"
}

variable "cloudflare_record_ttl" {
  description = "TTL for the Cloudflare DNS records used in certificate validation."
  type        = number
  default     = 1800
}
