output "domain_name" {
  description = "The domain name of the API Gateway"
  value       = aws_api_gateway_domain_name.this.domain_name
}

output "cloudfront_domain_name" {
  description = "The CloudFront domain name associated with the API Gateway (for EDGE endpoints)"
  value       = local.is_edge_endpoint ? aws_api_gateway_domain_name.this.cloudfront_domain_name : null
}

output "cloudfront_zone_id" {
  description = "The CloudFront hosted zone ID associated with the API Gateway (for EDGE endpoints)"
  value       = local.is_edge_endpoint ? aws_api_gateway_domain_name.this.cloudfront_zone_id : null
}

output "regional_domain_name" {
  description = "The regional domain name associated with the API Gateway (for REGIONAL endpoints)"
  value       = !local.is_edge_endpoint ? aws_api_gateway_domain_name.this.regional_domain_name : null
}

output "regional_zone_id" {
  description = "The regional hosted zone ID associated with the API Gateway (for REGIONAL endpoints)"
  value       = !local.is_edge_endpoint ? aws_api_gateway_domain_name.this.regional_zone_id : null
}