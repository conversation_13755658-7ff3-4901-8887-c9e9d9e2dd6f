output "main_sec_group_id" {
  description = "The database security group ip"
  value       = aws_security_group.main_sec_group.id
}

output "default_security_group_id" {
  description = "The ID of the security group created by default on VPC"
  value       = aws_vpc.default.default_security_group_id
}

output "havelock_security_group_id" {
  description = "The ID of the security group created by default on VPC"
  value       = aws_security_group.havelock_north_access.id
}

output "main_vpc_id" {
  description = "The main VPC's ID "
  value       = aws_vpc.default.id
}

output "main_subnet_public_A_id" {
  description = "The database subnet id"
  value       = aws_subnet.main_subnet_public[0].id
}

output "main_subnet_public_B_id" {
  description = "The database subnet id"
  value       = aws_subnet.main_subnet_public[1].id
}

# output "main_subnet_2_id" {
#   description = "The database subnet id"
#   value       = aws_subnet.main_subnet_2.id
# }
