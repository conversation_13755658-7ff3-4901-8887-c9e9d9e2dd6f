variable "aws_region" {
}

variable "vpc_tag_name" {
  default = "Main"
}

variable "vpc_cidr_block" {
  description = "VPC's CIDR block"
  type        = string
  default     = "**********/16"
  validation {
    condition     = can(regex("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(1[6-9]|2[0-8]))$", var.vpc_cidr_block))
    error_message = "CIDR block parameter must be in the form x.x.x.x/16-28."
  }
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "Network"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
