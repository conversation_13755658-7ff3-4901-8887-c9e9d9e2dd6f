resource "aws_iam_user" "pentest_user" {
  name = var.username
  tags = var.tags
}

# ReadOnlyAccess policy attachment
resource "aws_iam_user_policy_attachment" "readonly_access" {
  user       = aws_iam_user.pentest_user.name
  policy_arn = "arn:aws:iam::aws:policy/ReadOnlyAccess"
}

# SecurityAudit policy attachment
resource "aws_iam_user_policy_attachment" "security_audit" {
  user       = aws_iam_user.pentest_user.name
  policy_arn = "arn:aws:iam::aws:policy/SecurityAudit"
}

# AccessAnalyzerServiceRolePolicy attachment
resource "aws_iam_user_policy_attachment" "access_analyzer" {
  user       = aws_iam_user.pentest_user.name
  policy_arn = "arn:aws:iam::aws:policy/IAMAccessAnalyzerFullAccess"
}

# Inline policy for CreateAccessKey permission
resource "aws_iam_user_policy" "create_access_key" {
  name = "CreateAccessKeyPolicy"
  user = aws_iam_user.pentest_user.name
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action   = "iam:CreateAccessKey"
        Effect   = "Allow"
        Resource = "arn:aws:iam::*:user/${var.username}"
      }
    ]
  })
}

# Create access key for the pentest user
resource "aws_iam_access_key" "pentest_user_key" {
  user = aws_iam_user.pentest_user.name
}

// resource "aws_iam_user_login_profile" "pentest_user_console" {
//   user                    = aws_iam_user.pentest_user.name
//   password_reset_required = true
// }

/*
resource "aws_iam_user_policy" "enforce_mfa_policy" {
  name = "EnforceMFAPolicy"
  user = aws_iam_user.pentest_user.name

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowViewAccountInfo",
        Effect = "Allow",
        Action = [
          "iam:GetAccountPasswordPolicy",
          "iam:GetAccountSummary",
          "iam:ListAccountAliases"
        ],
        Resource = "*"
      },
      {
        Sid    = "AllowManageOwnPasswords",
        Effect = "Allow",
        Action = [
          "iam:ChangePassword",
          "iam:GetUser"
        ],
        Resource = "arn:aws:iam::*:user/${var.username}"
      },
      {
        Sid    = "AllowManageOwnAccessKeys",
        Effect = "Allow",
        Action = [
          "iam:CreateAccessKey",
          "iam:DeleteAccessKey",
          "iam:ListAccessKeys",
          "iam:UpdateAccessKey",
          "iam:GetUser"
        ],
        Resource = "arn:aws:iam::*:user/${var.username}"
      },
      {
        Sid    = "AllowManageOwnSigningCertificates",
        Effect = "Allow",
        Action = [
          "iam:DeleteSigningCertificate",
          "iam:ListSigningCertificates",
          "iam:UpdateSigningCertificate",
          "iam:UploadSigningCertificate",
          "iam:GetUser"
        ],
        Resource = "arn:aws:iam::*:user/${var.username}"
      },
      {
        Sid    = "AllowManageOwnMFADevices",
        Effect = "Allow",
        Action = [
          "iam:CreateVirtualMFADevice",
          "iam:DeleteVirtualMFADevice",
          "iam:EnableMFADevice",
          "iam:ListMFADevices",
          "iam:ResyncMFADevice",
          "iam:DeactivateMFADevice"
        ],
        Resource = [
          "arn:aws:iam::*:mfa/${var.username}",
          "arn:aws:iam::*:user/${var.username}"
        ]
      },
      {
        Sid      = "AllowListVirtualMFADevicesNoResourceRestriction",
        Effect   = "Allow",
        Action   = "iam:ListVirtualMFADevices",
        Resource = "*"
      },
      {
        Sid       = "BlockMostAccessUnlessSignedInWithMFA",
        Effect    = "Deny",
        NotAction = [
          "iam:ChangePassword",
          "iam:CreateAccessKey",
          "iam:CreateVirtualMFADevice",
          "iam:DeleteAccessKey",
          "iam:DeleteSigningCertificate",
          "iam:DeleteVirtualMFADevice",
          "iam:EnableMFADevice",
          "iam:GetAccountPasswordPolicy",
          "iam:GetAccountSummary",
          "iam:GetUser",
          "iam:ListAccessKeys",
          "iam:ListAccountAliases",
          "iam:ListMFADevices",
          "iam:ListSigningCertificates",
          "iam:ListVirtualMFADevices",
          "iam:ResyncMFADevice",
          "iam:UpdateAccessKey",
          "iam:UpdateSigningCertificate",
          "iam:UploadSigningCertificate",
          "iam:DeactivateMFADevice"
        ],
        Resource  = "*",
        Condition = {
          "BoolIfExists" = {
            "aws:MultiFactorAuthPresent" = "false"
          }
        }
      }
    ]
  })
}
*/

# Customer-managed policy for temporary MFA setup and access key management
# This policy should be removed once the pentest user has configured their MFA device.
resource "aws_iam_policy" "pentest_mfa_setup" {
  name        = "TemporaryAllowPentestUserMFASetup"
  description = "Temp allow ${var.username} to enrol an MFA device and manage access keys"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid      = "AllowCreateAccessKey",
        Effect   = "Allow",
        Action   = "iam:CreateAccessKey",
        Resource = "arn:aws:iam::*:user/${var.username}"
      },
      {
        Sid    = "AllowManageOwnMFADevicesForSetup",
        Effect = "Allow",
        Action = [
          "iam:CreateVirtualMFADevice",
          "iam:DeleteVirtualMFADevice",
          "iam:EnableMFADevice",
          "iam:ListMFADevices",
          "iam:ResyncMFADevice",
          "iam:DeactivateMFADevice",
          "iam:GetUser"
        ],
        Resource = [
          "arn:aws:iam::*:mfa/${var.username}",
          "arn:aws:iam::*:user/${var.username}"
        ]
      },
      {
        # This statement ensures iam:ListVirtualMFADevices can be called without restriction,
        # which is often necessary before an MFA device ARN is known.
        Sid      = "AllowListVirtualMFADevicesGlobalForSetup",
        Effect   = "Allow",
        Action   = "iam:ListVirtualMFADevices",
        Resource = "*"
      }
    ]
  })

  tags = var.tags
}

# Attach the MFA setup policy to the pentest user
resource "aws_iam_user_policy_attachment" "pentest_mfa_setup" {
  user       = aws_iam_user.pentest_user.name
  policy_arn = aws_iam_policy.pentest_mfa_setup.arn
}

# Create console access for the pentest user
resource "aws_iam_user_login_profile" "pentest_user_console" {
  user                    = aws_iam_user.pentest_user.name
  password_reset_required = true
}

# Create a secret in Secrets Manager to store the credentials
resource "aws_secretsmanager_secret" "pentest_credentials" {
  name        = "pentest/${var.username}/credentials"
  description = "Access credentials for pentesting user ${var.username}"
  tags        = var.tags
}

# Store the access key, secret key, and console password in the secret
resource "aws_secretsmanager_secret_version" "pentest_credentials" {
  secret_id = aws_secretsmanager_secret.pentest_credentials.id
  secret_string = jsonencode({
    username         = aws_iam_user.pentest_user.name
    access_key       = aws_iam_access_key.pentest_user_key.id
    secret_key       = aws_iam_access_key.pentest_user_key.secret
    console_password = aws_iam_user_login_profile.pentest_user_console.password
  })
}
