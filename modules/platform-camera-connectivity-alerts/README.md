# Terraform Module: Grafana SNS Publisher IAM

## Overview
This Terraform module provisions an IAM user for Grafana that is allowed to publish alerts to a specified SNS topic. It creates an IAM user, defines and attaches an IAM policy for SNS publish permissions, and generates access keys for Grafana integration.

## Resources Created
- **IAM User**: A dedicated user for Grafana alerts.
- **IAM Policy**: A policy granting permission to perform SNS:Publish on the specified SNS topic.
- **IAM User Policy Attachment**: Associates the SNS publish policy with the IAM user.
- **IAM Access Key**: Provides credentials for <PERSON><PERSON> to authenticate with AWS.

## Variables
- **environment**: Deployment environment (e.g., dev, staging, prod). Default is `prod`.
- **sns_topic_arn**: ARN of the SNS topic that receives camera connectivity alerts.

## Outputs
- **grafana_access_key_id**: Access Key ID for the Grafana IAM user.
- **grafana_secret_access_key**: Secret Access Key for the Grafana IAM user (sensitive).

## Usage
Include this module in your Terraform configuration and supply the required variables. For example:

```hcl
module "grafana_sns_iam" {
  source        = "./path/to/module"
  environment   = "prod"
  sns_topic_arn = "arn:aws:sns:ap-southeast-2:123456789012:camera-alerts-topic"
}
