# IAM User for Graf<PERSON> to publish to SNS
resource "aws_iam_user" "grafana_alerts" {
  name = "grafana-alerts-${var.environment}"
  path = "/service/"

  tags = {
    Environment = var.environment
    Squad       = "Platform"
    Application = "Camera Connectivity"
    Purpose     = "Grafana Camera Alerts Integration"
  }
}

# IAM policy for SNS publish permissions
resource "aws_iam_policy" "sns_publish" {
  name        = "grafana-alerts-sns-publish-${var.environment}"
  description = "Policy allowing Grafana to publish to the camera alerts SNS topic"
  policy      = data.aws_iam_policy_document.sns_publish.json
}

# Attach the policy to the user
resource "aws_iam_user_policy_attachment" "grafana_alerts_sns" {
  user       = aws_iam_user.grafana_alerts.name
  policy_arn = aws_iam_policy.sns_publish.arn
}

# Access key for the IAM user
resource "aws_iam_access_key" "grafana_alerts" {
  user = aws_iam_user.grafana_alerts.name
}
