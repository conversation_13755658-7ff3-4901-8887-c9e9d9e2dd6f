# Module EC2 Instance CloudWatch Alarms

Deploys CloudWatch alarms for EC2 instance provided CloudWatch metrics. The EC2 instances must exist for the alarms to be created. CloudWatch Alarms notifications can be optionally sent to emails using a SNS topic.

For available EC2 instance CloudWatch metrics see:
https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/viewing_metrics_with_cloudwatch.html

## Example usage - CPU utilisation

Set high and low CPU utilization alarms for EC2 instances with the tag `Name == "MyEc2"`:

```hcl
module "ec2_instance_cw_alarms" {
  source         = "./modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.shared_cw_alarms_topic]

  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    MyEc2 = { instance_tags = { Name = "MyEc2" } }
  }

  cw_alarm_config_ec2_by_tags_cpu_util_low = {
    MyEc2 = { instance_tags = { Name = "MyEc2" } }
  }
}
```

Set high and low CPU utilization alarms for EC2 instances with the tags `Environment == "prod"` and `AlarmingEnabled == "true"`:

```hcl
module "ec2_instance_cw_alarms" {
  source         = "./modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.shared_cw_alarms_topic]

  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    ProdAlarmsEnabled = { instance_tags = {
      Environment     = "prod"
      AlarmingEnabled = "true"
    } }
  }

  cw_alarm_config_ec2_by_tags_cpu_util_low = {
    ProdAlarmsEnabled = { instance_tags = {
      Environment     = "prod"
      AlarmingEnabled = "true"
    } }
  }
}
```

Set high CPU utilization alarms for EC2 instances with the tag `Name == "MyEc2"` with custom thresholds:

```hcl
module "ec2_instance_cw_alarms" {
  source         = "./modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.shared_cw_alarms_topic]

  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    MyEc2 = {
      instance_tags                          = { Name = "MyEc2" }
      evaluation_periods_basic_monitoring    = 1
      evaluation_periods_detailed_monitoring = 3
      statistic                              = "Minimum"
      threshold                              = 90
    }
  }
}
```
