output "ec2_by_tags_cpu_util_high_alarms" {
  description = "CloudWatch Alarms for EC2 instance identified by tags, high CPU utlization."
  value = {
    for k, v in aws_cloudwatch_metric_alarm.ec2_by_tags_cpu_util_high_alarms : k => {
      arn        = v.arn
      id         = v.id
      alarm_name = v.alarm_name
    }
  }
}

output "ec2_by_tags_cpu_util_low_alarms" {
  description = "CloudWatch Alarms for EC2 instance identified by tags, low CPU utlization."
  value = {
    for k, v in aws_cloudwatch_metric_alarm.ec2_by_tags_cpu_util_low_alarms : k => {
      arn        = v.arn
      id         = v.id
      alarm_name = v.alarm_name
    }
  }
}
