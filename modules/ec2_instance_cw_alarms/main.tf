# =====
# Tags: Consolidated
# =====
locals {
  tags = merge(var.default_tags, var.tags)
}

# =====
# CloudWatch Alarms: EC2 instances High CPU
# =====
locals {
  ec2_by_tags_cpu_util_high_alarms = {
    name_prefix = "ec2-cpu-utilization-high"
    alarm_instances_map = merge([
      for group_name, v in data.aws_instances.by_tags_cpu_util_high : {
        for id in v.ids : "${group_name}-${id}" => {
          group_name  = group_name
          instance_id = id
        }
      }
    ]...)
    # Example:
    # alarm_instances_map = {
    #   "groupA-i-12345678900000001" = {
    #     group_name  = "groupA"
    #     instance_id = "i-12345678900000001"
    #   }
    #   "groupA-i-12345678900000002" = {
    #     group_name  = "groupA"
    #     instance_id = "i-12345678900000002"
    #   }
    #   "groupB-i-12345678900000003" = {
    #     group_name  = "groupB"
    #     instance_id = "i-12345678900000003"
    #   }
    # }
  }
}

data "aws_instances" "by_tags_cpu_util_high" {
  for_each = var.cw_alarm_config_ec2_by_tags_cpu_util_high

  instance_tags        = each.value.instance_tags
  instance_state_names = ["running", "shutting-down", "stopped", "stopping"]
}

data "aws_instance" "by_tags_cpu_util_high" {
  for_each = local.ec2_by_tags_cpu_util_high_alarms.alarm_instances_map

  instance_id = each.value.instance_id
}

resource "aws_cloudwatch_metric_alarm" "ec2_by_tags_cpu_util_high_alarms" {
  for_each = local.ec2_by_tags_cpu_util_high_alarms.alarm_instances_map

  alarm_name = "${local.ec2_by_tags_cpu_util_high_alarms.name_prefix}-${each.key}"
  alarm_description = coalesce(
    var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].alarm_description,
    "CPU utilization threshold exceeded for EC2 instance ${each.value.instance_id}"
  )
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods = (
    data.aws_instance.by_tags_cpu_util_high[each.key].monitoring
    ? var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].evaluation_periods_detailed_monitoring
    : var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].evaluation_periods_basic_monitoring
  )
  metric_name = "CPUUtilization"
  namespace   = "AWS/EC2"
  dimensions = {
    InstanceId = each.value.instance_id
  }
  period = (
    data.aws_instance.by_tags_cpu_util_high[each.key].monitoring
    ? 60  # Detailed monitoring, metric time period is 60 seconds
    : 300 # Basic monitoring, metric time period is 300 seconds
  )
  statistic     = var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].statistic
  threshold     = var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].threshold
  alarm_actions = var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].enable_notification ? var.sns_topic_arns : null
  ok_actions    = var.cw_alarm_config_ec2_by_tags_cpu_util_high[each.value.group_name].enable_notification ? var.sns_topic_arns : null

  tags = merge(local.tags, { Name = "${local.ec2_by_tags_cpu_util_high_alarms.name_prefix}-${each.key}" })
}

# =====
# CloudWatch Alarms: EC2 instances Low CPU
# =====
locals {
  ec2_by_tags_cpu_util_low_alarms = {
    name_prefix = "ec2-cpu-utilization-low"
    alarm_instances_map = merge([
      for group_name, v in data.aws_instances.by_tags_cpu_util_low : {
        for id in v.ids : "${group_name}-${id}" => {
          group_name  = group_name
          instance_id = id
        }
      }
    ]...)
    # Example:
    # alarm_instances_map = {
    #   "groupA-i-12345678900000001" = {
    #     group_name  = "groupA"
    #     instance_id = "i-12345678900000001"
    #   }
    #   "groupA-i-12345678900000002" = {
    #     group_name  = "groupA"
    #     instance_id = "i-12345678900000002"
    #   }
    #   "groupB-i-12345678900000003" = {
    #     group_name  = "groupB"
    #     instance_id = "i-12345678900000003"
    #   }
    # }
  }
}

data "aws_instances" "by_tags_cpu_util_low" {
  for_each = var.cw_alarm_config_ec2_by_tags_cpu_util_low

  instance_tags        = each.value.instance_tags
  instance_state_names = ["running", "shutting-down", "stopped", "stopping"]
}

data "aws_instance" "by_tags_cpu_util_low" {
  for_each = local.ec2_by_tags_cpu_util_low_alarms.alarm_instances_map

  instance_id = each.value.instance_id
}
resource "aws_cloudwatch_metric_alarm" "ec2_by_tags_cpu_util_low_alarms" {
  for_each = local.ec2_by_tags_cpu_util_low_alarms.alarm_instances_map

  alarm_name = "${local.ec2_by_tags_cpu_util_low_alarms.name_prefix}-${each.key}"
  alarm_description = coalesce(
    var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].alarm_description,
    "CPU utilization fallen below threshold for EC2 instance ${each.value.instance_id}"
  )
  comparison_operator = "LessThanThreshold"
  evaluation_periods = (
    data.aws_instance.by_tags_cpu_util_low[each.key].monitoring
    ? var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].evaluation_periods_detailed_monitoring
    : var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].evaluation_periods_basic_monitoring
  )
  metric_name = "CPUUtilization"
  namespace   = "AWS/EC2"
  dimensions = {
    InstanceId = each.value.instance_id
  }
  period = (
    data.aws_instance.by_tags_cpu_util_low[each.key].monitoring
    ? 60  # Detailed monitoring, metric time period is 60 seconds
    : 300 # Basic monitoring, metric time period is 300 seconds
  )
  statistic     = var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].statistic
  threshold     = var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].threshold
  alarm_actions = var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].enable_notification ? var.sns_topic_arns : null
  ok_actions    = var.cw_alarm_config_ec2_by_tags_cpu_util_low[each.value.group_name].enable_notification ? var.sns_topic_arns : null

  treat_missing_data = "notBreaching"

  tags = merge(local.tags, { Name = "${local.ec2_by_tags_cpu_util_low_alarms.name_prefix}-${each.key}" })
}
