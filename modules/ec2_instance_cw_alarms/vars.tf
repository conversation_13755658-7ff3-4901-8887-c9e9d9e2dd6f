# =====
# SNS Topic: CloudWatch Alarms for EC2 instance
# =====
variable "sns_topic_arns" {
  description = <<-DOC
    [Optional] List of existing SNS topic ARNs to use for sending CloudWatch Alarm notifications
    for EC2 instances.

    It is recommended to create a centralised SNS topic for multiple CloudWatch Alarm notifications
    using the module `cw_alarm_notifications_sns_topic`:
    https://bitbucket.org/fingermarkltd/fingermark-terraform/src/master/modules/cw_alarm_notifications_sns_topic/
  DOC
  type        = list(string)
  default     = []
}

# =====
# CloudWatch Alarms: EC2 instances CPU
# =====
variable "cw_alarm_config_ec2_by_tags_cpu_util_high" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for EC2 instance identified by tags, high
    CPU utlization.
    CloudWatch Alarm Namespace: AWS/EC2
    CloudWatch Alarm Metric: CPUUtilization
    CloudWatch Alarm Metric Dimensions: InstanceId
    Comparison Operator: GreaterThanThreshold
    
    For each map item, the key is a group name used for forming the name of the CloudWatch Alarm.
    The value is an object with the following attributes.

    * `instance_tags` - [Required] Map of tags to identify EC2 instances.
    * `evaluation_periods_basic_monitoring` - [Optional] Used only if the EC2 instance is using
      basic monitoring. The number of periods to analyze for the alarm's corresponding metric.
      Basic monitoring has metrics availabe in 5 minute (300 seconds) time periods. Must be an
      integer.
    * `evaluation_periods_detailed_monitoring` - [Optional] Used only if the EC2 instance is using
      detailed monitoring. The number of periods to analyze for the alarm's corresponding metric.
      Detailed monitoring has metrics availabe in 1 minute (60 seconds) time periods. Must be an
      integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    instance_tags                          = map(string)
    evaluation_periods_basic_monitoring    = optional(number, 2) # 2 * 5 minutes = Alert after continuous breaching over total 10 minutes
    evaluation_periods_detailed_monitoring = optional(number, 5) # 5 * 1 minute = Alert after continuous breaching over total 5 minutes
    statistic                              = optional(string, "Average")
    threshold                              = optional(number, 85) # 85%
    alarm_description                      = optional(string)
    enable_notification                    = optional(bool, true)
  }))
  default = {}
}


variable "cw_alarm_config_ec2_by_tags_cpu_util_low" {
  description = <<-DOC
    [Optional] Configuration map for CloudWatch Alarms for EC2 instance identified by tags, low
    CPU utlization (for detecting underutilized instances).
    CloudWatch Alarm Namespace: AWS/EC2
    CloudWatch Alarm Metric: CPUUtilization
    CloudWatch Alarm Metric Dimensions: InstanceId
    Comparison Operator: LessThanThreshold
    
    For each map item, the key is a group name used for forming the name of the CloudWatch Alarm.
    The value is an object with the following attributes.

    * `instance_tags` - [Required] Map of tags to identify EC2 instances.
    * `evaluation_periods_basic_monitoring` - [Optional] Used only if the EC2 instance is using
      basic monitoring. The number of periods to analyze for the alarm's corresponding metric.
      Basic monitoring has metrics availabe in 5 minute (300 seconds) time periods. Must be an
      integer.
    * `evaluation_periods_detailed_monitoring` - [Optional] Used only if the EC2 instance is using
      detailed monitoring. The number of periods to analyze for the alarm's corresponding metric.
      Detailed monitoring has metrics availabe in 1 minute (60 seconds) time periods. Must be an
      integer.
    * `statistic` - [Optional] The statistic to apply to the alarm's associated metric. Allowed
      values: `Average`, `Minimum`, `Maximum`, `SampleCount`, `Sum`.
    * `threshold` - [Optional] The value against which the specified statistic is compared.
    * `alarm_description` - [Optional] The description for the alarm.
    * `enable_notification` - [Optional] Whether to enable alarm notification using a SNS topic.
  DOC
  type = map(object({
    instance_tags                          = map(string)
    evaluation_periods_basic_monitoring    = optional(number, 12) # 12 * 5 minutes = Alert after continuous breaching over total 60 minutes
    evaluation_periods_detailed_monitoring = optional(number, 60) # 60 * 1 minute = Alert after continuous breaching over total 60 minutes
    statistic                              = optional(string, "Average")
    threshold                              = optional(number, 2) # 2%
    alarm_description                      = optional(string)
    enable_notification                    = optional(bool, true)
  }))
  default = {}
}

# =====
# Tags
# =====
variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "monitoring"
    Product     = "eyecue"
    Environment = "prod"
  }
}
