module "ec2_instance" {
  source                      = "terraform-aws-modules/ec2-instance/aws"
  version                     = "4.0.0"
  name                        = var.vpn_forwarder_name
  availability_zone           = var.vpn_forwarder_availability_zone
  user_data                   = data.template_file.vpn_forwarder_user_data.rendered
  ami                         = coalesce(var.vpn_forwarder_ami_id, data.aws_ami.default.id)
  instance_type               = var.vpn_forwarder_instance_type
  key_name                    = var.vpn_forwarder_key_name
  monitoring                  = true
  vpc_security_group_ids      = concat(var.vpn_forwarder_security_group_ids, [])
  subnet_id                   = var.vpn_forwarder_subnet_id
  user_data_replace_on_change = var.vpn_forwarder_user_data_replace_on_change
  source_dest_check           = var.vpn_forwarder_source_dest_check
  tags                        = merge(var.default_tags, var.tags)
}

resource "aws_route" "vpn_forwarder_servers" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_servers
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_mcd" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_mcd
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_kfc" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_kfc
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_amr" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_amr
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_czp" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_czp
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_stb" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_stb
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_mnz" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_mnz
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_elj" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_elj
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}

resource "aws_route" "vpn_forwarder_grd" {
  route_table_id         = var.vpn_forwarder_route_table_id
  destination_cidr_block = var.vpn_forwarder_destination_cidr_block_grd
  network_interface_id   = module.ec2_instance.primary_network_interface_id
}
