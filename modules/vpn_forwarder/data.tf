data "aws_region" "current" {}

data "aws_ami" "default" {
  most_recent = "true"

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*"]
  }
  # aws ec2 describe-images --image-ids ami-0194c3e07668a7e36 --region eu-west-2 | jq ".Images[0].OwnerId"  
  owners = ["099720109477"]
}

data "template_file" "vpn_forwarder_user_data" {
  template = file(var.vpn_forwarder_user_data_template)
  vars = {
    authorized_keys_content = file(var.vpn_forwarder_authorized_keys_path)
  }
}

