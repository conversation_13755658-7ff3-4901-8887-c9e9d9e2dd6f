variable "default_tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "tags" {
  type = map(string)
  default = {
    Product = "Eyecue"
    Squad   = "Platform"
  }
}

variable "vpn_forwarder_name" {
  type    = string
  default = "VPN-Forwarder"
}

variable "vpn_forwarder_ami_id" {
  type    = string
  default = ""
}

variable "vpn_forwarder_user_data_template" {
  type        = string
  default     = "user_data/user_data.sh"
  description = "User Data template to use for provisioning EC2"
}

variable "vpn_forwarder_key_name" {
  type    = string
  default = "fm-infra-team"
}

variable "vpn_forwarder_authorized_keys_path" {
  type        = string
  description = "A path to read authorized_keys file and append the one in the server"
  default     = null
}

variable "vpn_forwarder_allowed_security_groups" {
  type = list(string)
}

variable "vpn_forwarder_vpc_id" {
  type = string
}

variable "vpn_forwarder_subnet_id" {
  type = string
}

variable "vpn_forwarder_availability_zone" {
  type = string
}

variable "vpn_forwarder_instance_type" {
  type    = string
  default = "t3.micro"
}

variable "vpn_forwarder_security_group_ids" {
  type = list(string)
}

variable "vpn_forwarder_user_data_replace_on_change" {
  type    = bool
  default = true
}

variable "vpn_forwarder_source_dest_check" {
  type    = bool
  default = false
}

variable "vpn_forwarder_route_table_id" {
  type    = string
  default = ""
}

variable "vpn_forwarder_destination_cidr_block_servers" {
  type    = string
  default = "*************/24"
}

variable "vpn_forwarder_destination_cidr_block_mcd" {
  type    = string
  default = "*********/22"
}

variable "vpn_forwarder_destination_cidr_block_kfc" {
  type    = string
  default = "10.21.0.0/22"
}

variable "vpn_forwarder_destination_cidr_block_amr" {
  type    = string
  default = "10.25.0.0/22"
}

variable "vpn_forwarder_destination_cidr_block_czp" {
  type    = string
  default = "10.27.0.0/22"
}

variable "vpn_forwarder_destination_cidr_block_stb" {
  type    = string
  default = "10.28.0.0/22"
}

variable "vpn_forwarder_destination_cidr_block_mnz" {
  type    = string
  default = "10.29.0.0/22"
}

variable "vpn_forwarder_destination_cidr_block_elj" {
  type    = string
  default = "10.30.0.0/22"
}

variable "vpn_forwarder_destination_cidr_block_grd" {
  type    = string
  default = "10.31.0.0/22"
}
