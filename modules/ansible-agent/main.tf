module "global" {
  source = "./modules/global"
  providers = {
    aws = aws.ap-southeast-2
  }
}

module "ap_southeast_2" {
  source                     = "./modules/regional"
  ansible_agent_s3_bucket_id = module.global.ansible_agent_s3_bucket_id
  ansible_agent_zip_file_dir = "../global/ansible-agent.deb.zip"
  providers = {
    aws = aws.ap-southeast-2
  }
}

module "us_east_1" {
  source                     = "./modules/regional"
  ansible_agent_s3_bucket_id = module.global.ansible_agent_s3_bucket_id
  ansible_agent_zip_file_dir = "../global/ansible-agent.deb.zip"
  providers = {
    aws = aws.us-east-1
  }
}

module "us_west_1" {
  source                     = "./modules/regional"
  ansible_agent_s3_bucket_id = module.global.ansible_agent_s3_bucket_id
  ansible_agent_zip_file_dir = "../global/ansible-agent.deb.zip"
  providers = {
    aws = aws.us-west-1
  }
}

module "us_west_2" {
  source                     = "./modules/regional"
  ansible_agent_s3_bucket_id = module.global.ansible_agent_s3_bucket_id
  ansible_agent_zip_file_dir = "../global/ansible-agent.deb.zip"
  providers = {
    aws = aws.us-west-2
  }
}

