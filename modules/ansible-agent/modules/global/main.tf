resource "aws_s3_object" "ansible_agent_package" {
  depends_on = [
    data.archive_file.ansible_agent_package
  ]
  bucket = aws_s3_bucket.ansible_agent_packages.id
  key    = "packages/${var.ansible_agent_package_name}.deb.zip"
  source = "${path.module}/${var.ansible_agent_package_name}.deb.zip"
  etag   = filemd5("${path.module}/${var.ansible_agent_package_name}.deb.zip")
}

resource "aws_s3_bucket" "ansible_agent_packages" {
  bucket = "ansible-agent-packages-${data.aws_region.current.name}-${data.aws_caller_identity.current.account_id}"
}

resource "aws_s3_bucket_versioning" "ansible_agent_packages" {
  bucket = aws_s3_bucket.ansible_agent_packages.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_public_access_block" "ansible_agent_packages" {
  bucket = aws_s3_bucket.ansible_agent_packages.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
