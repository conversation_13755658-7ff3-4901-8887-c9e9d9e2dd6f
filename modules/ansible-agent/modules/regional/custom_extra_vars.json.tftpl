{"schemaVersion": "2.2", "description": "Set environment variable from Parameter Store", "parameters": {"ParameterStoreKey": {"type": "String", "description": "Name of the Parameter Store Environment Variable"}}, "mainSteps": [{"name": "setEnvironmentVariable", "action": "aws:runShellScript", "inputs": {"runCommand": ["echo 'export ANSIBLE_AGENT_CUSTOM_EXTRA_VARS={{ ssm:{{ ParameterStoreKey }} }}' > custom-extra-vars.conf"], "workingDirectory": "/etc/ansible-agent/config.d", "executionTimeout": "360"}}]}