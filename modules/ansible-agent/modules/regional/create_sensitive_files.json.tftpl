{"schemaVersion": "2.2", "description": "Retrieve multiple secrets from Parameter Store and write them to files", "mainSteps": [{"name": "AnsibleAgentCreateSensitiveFiles", "action": "aws:runShellScript", "inputs": {"runCommand": ["ANSIBLE_VAULT_TOKEN=$(aws ssm get-parameter --name \"${AnsibleVaultTokenParameter}\" --region \"${AwsRegionParameter}\" --with-decryption --query Parameter.Value --output text)", "if [ -z \"$ANSIBLE_VAULT_TOKEN\" ]; then echo 'Failed to retrieve the Ansible Vault token'; exit 1; fi", "echo \"$ANSIBLE_VAULT_TOKEN\" > /etc/ansible-agent/vault.txt", "chmod 600 /etc/ansible-agent/vault.txt", "SSH_KEY=$(aws ssm get-parameter --name \"${SshKeyParameter}\" --region \"${AwsRegionParameter}\" --with-decryption --query Parameter.Value --output text)", "if [ -z \"$SSH_KEY\" ]; then echo 'Failed to retrieve the SSH key'; exit 1; fi", "echo \"$SSH_KEY\" > /etc/ansible-agent/id_rsa", "chmod 600 /etc/ansible-agent/id_rsa", "AWS_ACCESS_KEY_ID=$(aws ssm get-parameter --name \"${AwsAccessKeyIdParameter}\" --region \"${AwsRegionParameter}\" --with-decryption --query Parameter.Value --output text)", "AWS_SECRET_ACCESS_KEY=$(aws ssm get-parameter --name \"${AwsSecretAccessKeyParameter}\" --region \"${AwsRegionParameter}\" --with-decryption --query Parameter.Value --output text)", "SLACK_CHANNEL=#server-provisioning", "AUTO_REGISTER=-y", "if [ -z \"$AWS_ACCESS_KEY_ID\" ] || [ -z \"$AWS_SECRET_ACCESS_KEY\" ] || [ -z \"$SLACK_CHANNEL\" ] || [ -z \"$AUTO_REGISTER\" ]; then echo 'Failed to retrieve one or more config.ini parameters'; exit 1; fi", "cat <<EOF > /etc/kommisjon/config.ini\n[CREDENTIALS]\nAWS_ACCESS_KEY_ID = $AWS_ACCESS_KEY_ID\nAWS_SECRET_ACCESS_KEY = $AWS_SECRET_ACCESS_KEY\n\n[SLACK]\nSLACK_CHANNEL = $SLACK_CHANNEL\n\n[KOMMISJON]\nAUTO_REGISTER = $AUTO_REGISTER\nEOF", "chmod 600 /etc/kommisjon/config.ini"], "executionTimeout": "360"}}]}