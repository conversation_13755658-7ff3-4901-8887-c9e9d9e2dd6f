resource "random_string" "ansible_agent_manifest_version" {
  length  = 20
  special = false
}

resource "aws_ssm_document" "ansible_agent" {
  name          = var.ansible_agent_package_name
  document_type = "Package"
  content = templatefile("${path.module}/${var.ansible_agent_manifest_name}.tftpl", {
    values           = ["s3://${var.ansible_agent_s3_bucket_id}/packages/"]
    SHA256_RESULT    = filesha256("${path.module}/${var.ansible_agent_zip_file_dir}"),
    DOCUMENT_NAME    = "ansible-agent",
    DOCUMENT_VERSION = "${random_string.ansible_agent_manifest_version.result}"
  })
  attachments_source {
    key    = "SourceUrl"
    values = ["s3://${var.ansible_agent_s3_bucket_id}/packages/"]
  }
  tags = var.default_tags
}

resource "aws_ssm_association" "ansible_agent" {
  name             = "AWS-ConfigureAWSPackage"
  association_name = "${var.ansible_agent_package_name}-installation"
  parameters = {
    action              = "Install"
    name                = var.ansible_agent_package_name
    installationType    = "Uninstall and reinstall"
    additionalArguments = jsonencode({})
  }
  compliance_severity = "UNSPECIFIED"

  targets {
    key    = "tag:Provisioned"
    values = ["False"]
  }
}

resource "aws_ssm_document" "set_extra_vars" {
  name          = "AnsibleAgent-Set-Custom-ExtraVars"
  document_type = "Command"
  content       = file("${path.module}/custom_extra_vars.json.tftpl")
  tags          = var.default_tags
}

resource "aws_ssm_document" "create_sensitive_files" {
  name          = "AnsibleAgent-CreateSensitiveFiles"
  document_type = "Command"
  content = templatefile("${path.module}/create_sensitive_files.json.tftpl", {
    AnsibleVaultTokenParameter  = "/ansible-agent/vault/vault-token"
    SshKeyParameter             = "/ansible-agent/vault/id_rsa"
    AwsAccessKeyIdParameter     = "/ansible-agent/vault/aws_access_key_id"
    AwsSecretAccessKeyParameter = "/ansible-agent/vault/aws_secret_access_key"
    AwsRegionParameter          = data.aws_region.current.name
  })
  tags = var.default_tags
}

resource "aws_ssm_association" "create_sensitive_files" {
  name                = aws_ssm_document.create_sensitive_files.name
  association_name    = "AnsibleAgent-CreateSensitiveFilesAssociation"
  compliance_severity = "UNSPECIFIED"

  targets {
    key    = "tag:Provisioned"
    values = ["False"]
  }
}
