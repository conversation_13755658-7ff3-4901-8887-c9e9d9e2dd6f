variable "default_tags" {
  type = map(any)
  default = {
    "Application" = "Ansible Agent"
    "Stack"       = "Provisioning"
    "Terraform"   = "True"
    "Squad"       = "Infra"
  }
}

variable "ansible_agent_package_name" {
  type    = string
  default = "ansible-agent"
}

variable "ansible_agent_manifest_name" {
  type    = string
  default = "manifest.json"
}

variable "ansible_agent_s3_bucket_id" {
  type = string
}

variable "ansible_agent_zip_file_dir" {
  type = string
}
