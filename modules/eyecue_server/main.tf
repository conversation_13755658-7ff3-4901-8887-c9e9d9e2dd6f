module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}


data "aws_iam_policy_document" "eyecue_server_policy_document" {
  statement {
    sid = "EyecueVehiclePolicyDocument"
    actions = [
      "lambda:InvokeFunction",
      "lambda:InvokeAsync"
    ]
    resources = ["arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyeq-vehicle"]
  }
  statement {
    sid = "EyecueThingsShadowGetConfigServerPolicyDocument"
    actions = [
      "lambda:InvokeFunction",
      "lambda:InvokeAsync"
    ]
    resources = ["arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-things-shadow-get-config-server"]
  }
  statement {
    sid = "EyecueIdPoolPolicyDocument"
    actions = [
      "lambda:InvokeFunction",
      "lambda:InvokeAsync"
    ]
    resources = [
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-id-pool-id-pool",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-id-pool-update-vehicle"
    ]
  }
  statement {
    sid = "EyecueServerWeights"
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "arn:aws:s3:::eyecue-weights/detector_files",
      "arn:aws:s3:::eyecue-weights/detector_files/*"
    ]
  }
}

resource "aws_iam_policy" "eyecue_server_policy" {
  name        = "EyecueServerPolicy"
  depends_on  = [module.iam_user]
  description = "Enable EYECue server to invoke the function eyeq-vehicle"
  policy      = data.aws_iam_policy_document.eyecue_server_policy_document.json
}

resource "aws_iam_policy_attachment" "eyecue_server_policy_attachment" {
  name       = "EyecueServerPolicyAttachment"
  users      = [var.aws_iam_user]
  policy_arn = aws_iam_policy.eyecue_server_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
