# ==========================================
# SQS Monitoring Module Outputs
# ==========================================

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = var.enable_lambda_function ? aws_lambda_function.sqs_monitoring[0].arn : null
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = var.enable_lambda_function ? aws_lambda_function.sqs_monitoring[0].function_name : null
}

output "lambda_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = var.enable_lambda_function ? aws_iam_role.lambda_execution_role[0].arn : null
}

output "eventbridge_rule_arn" {
  description = "ARN of the EventBridge rule"
  value       = var.enable_lambda_function ? aws_cloudwatch_event_rule.sqs_monitoring_schedule[0].arn : null
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = var.enable_lambda_function ? aws_cloudwatch_log_group.lambda_logs[0].name : null
}

output "sns_topic_arn" {
  description = "SNS topic ARN used for alarm notifications"
  value       = var.sns_topic_arn
}
