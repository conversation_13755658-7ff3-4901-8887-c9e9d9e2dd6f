"""
SQS Monitoring Lambda Function

This Lambda function automatically discovers all SQS queues in an AWS account
and creates CloudWatch alarms for the ApproximateAgeOfOldestMessage metric
if they don't already exist. This ensures compliance with <PERSON><PERSON>'s 
"Messaging queue message age monitored" test.

Environment Variables:
- ALARM_THRESHOLD: Threshold in seconds (default: 300)
- ALARM_NAME_PREFIX: Prefix for alarm names (default: SQS-OldestMessageAge)
- ALARM_EVALUATION_PERIODS: Number of evaluation periods (default: 1)
- ALARM_PERIOD: Period in seconds (default: 300)
- ALARM_STATISTIC: Statistic to use (default: Maximum)
- ALARM_COMPARISON_OPERATOR: Comparison operator (default: Greater<PERSON>hanThreshold)
- SNS_TOPIC_ARN: SNS topic ARN for notifications (optional)
- TREAT_MISSING_DATA: How to treat missing data (default: missing)
- QUEUE_NAME_FILTER: Regex pattern to filter queue names (optional)
- EXCLUDED_QUEUE_NAMES: Comma-separated list of queue names to exclude (optional)
"""

import json
import logging
import os
import re
import boto3
from botocore.exceptions import Client<PERSON>rror
from typing import List, Dict

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
sqs_client = boto3.client('sqs')
cloudwatch_client = boto3.client('cloudwatch')

def get_env_var(name: str, default: str = None, required: bool = False) -> str:
    """Get environment variable with optional default and required validation."""
    value = os.environ.get(name, default)
    if required and not value:
        raise ValueError(f"Required environment variable {name} is not set")
    return value

def get_env_list(name: str, default: List[str] = None) -> List[str]:
    """Get environment variable as a list (comma-separated)."""
    value = os.environ.get(name, "")
    if not value:
        return default or []
    return [item.strip() for item in value.split(",") if item.strip()]

def list_all_sqs_queues() -> List[str]:
    """List all SQS queue URLs in the account."""
    try:
        queue_urls = []
        paginator = sqs_client.get_paginator('list_queues')

        for page in paginator.paginate():
            if 'QueueUrls' in page:
                queue_urls.extend(page['QueueUrls'])

        logger.info(f"Found {len(queue_urls)} SQS queues")
        return queue_urls

    except ClientError as e:
        logger.exception(f"Error listing SQS queues: {e}")
        raise

def extract_queue_name(queue_url: str) -> str:
    """Extract queue name from queue URL."""
    return queue_url.split('/')[-1]

def should_monitor_queue(queue_name: str, name_filter: str, excluded_names: List[str]) -> bool:
    """Determine if a queue should be monitored based on filters."""
    # Check if queue is in exclusion list
    if queue_name in excluded_names:
        logger.info(f"Skipping excluded queue: {queue_name}")
        return False

    # Check name filter if provided
    if name_filter:
        try:
            if not re.match(name_filter, queue_name):
                logger.info(f"Queue {queue_name} doesn't match filter pattern: {name_filter}")
                return False
        except re.error as e:
            logger.warning(f"Invalid regex pattern '{name_filter}': {e}")
            raise ValueError(f"Invalid QUEUE_NAME_FILTER regex pattern '{name_filter}': {e}")

    return True

def alarm_exists(queue_name: str, alarm_name_prefix: str) -> bool:
    """Check if CloudWatch alarm already exists for the queue."""
    alarm_name = f"{alarm_name_prefix}-{queue_name}"

    try:
        response = cloudwatch_client.describe_alarms_for_metric(
            Namespace='AWS/SQS',
            MetricName='ApproximateAgeOfOldestMessage',
            Dimensions=[
                {
                    'Name': 'QueueName',
                    'Value': queue_name
                }
            ]
        )

        # Check if any alarm matches our naming pattern
        for alarm in response.get('MetricAlarms', []):
            if alarm['AlarmName'] == alarm_name:
                logger.info(f"Alarm already exists for queue {queue_name}: {alarm_name}")
                return True

        return False

    except ClientError as e:
        logger.exception(f"Error checking existing alarms for queue {queue_name}: {e}")
        return False

def create_cloudwatch_alarm(queue_name: str, config: Dict) -> bool:
    """Create CloudWatch alarm for SQS queue."""
    alarm_name = f"{config['alarm_name_prefix']}-{queue_name}"

    alarm_params = {
        'AlarmName': alarm_name,
        'ComparisonOperator': config['comparison_operator'],
        'EvaluationPeriods': config['evaluation_periods'],
        'MetricName': 'ApproximateAgeOfOldestMessage',
        'Namespace': 'AWS/SQS',
        'Period': config['period'],
        'Statistic': config['statistic'],
        'Threshold': config['threshold'],
        'ActionsEnabled': True,
        'AlarmDescription': f'Monitors message age for SQS queue {queue_name} - Vanta compliance',
        'Dimensions': [
            {
                'Name': 'QueueName',
                'Value': queue_name
            }
        ],
        'Unit': 'Seconds',
        'TreatMissingData': config['treat_missing_data'],
        'Tags': [
            {'Key': 'Purpose', 'Value': 'VantaCompliance'},
            {'Key': 'CreatedBy', 'Value': 'SQSMonitoringLambda'},
            {'Key': 'QueueName', 'Value': queue_name}
        ]
    }

    # Add SNS topic if provided
    if config.get('sns_topic_arn'):
        alarm_params['AlarmActions'] = [config['sns_topic_arn']]
        alarm_params['OKActions'] = [config['sns_topic_arn']]

    try:
        cloudwatch_client.put_metric_alarm(**alarm_params)
        logger.info(f"Created CloudWatch alarm: {alarm_name}")
        return True

    except ClientError as e:
        logger.exception(f"Error creating alarm for queue {queue_name}: {e}")
        return False

def lambda_handler(event, context):
    """Main Lambda handler function."""
    logger.info("Starting SQS monitoring remediation")

    try:
        # Load configuration from environment variables
        config = {
            'threshold': float(get_env_var('ALARM_THRESHOLD', '300')),
            'alarm_name_prefix': get_env_var('ALARM_NAME_PREFIX', 'SQS-OldestMessageAge'),
            'evaluation_periods': int(get_env_var('ALARM_EVALUATION_PERIODS', '1')),
            'period': int(get_env_var('ALARM_PERIOD', '300')),
            'statistic': get_env_var('ALARM_STATISTIC', 'Maximum'),
            'comparison_operator': get_env_var('ALARM_COMPARISON_OPERATOR', 'GreaterThanThreshold'),
            'sns_topic_arn': get_env_var('SNS_TOPIC_ARN'),
            'treat_missing_data': get_env_var('TREAT_MISSING_DATA', 'missing'),
            'queue_name_filter': get_env_var('QUEUE_NAME_FILTER', ''),
            'excluded_queue_names': get_env_list('EXCLUDED_QUEUE_NAMES')
        }

        logger.info(f"Configuration: {json.dumps({k: v for k, v in config.items() if k != 'sns_topic_arn'})}")

        # Get all SQS queues
        queue_urls = list_all_sqs_queues()

        if not queue_urls:
            logger.info("No SQS queues found in the account")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'No SQS queues found',
                    'queues_processed': 0,
                    'alarms_created': 0
                })
            }

        # Process each queue
        queues_processed = 0
        alarms_created = 0

        for queue_url in queue_urls:
            queue_name = extract_queue_name(queue_url)

            # Apply filters
            if not should_monitor_queue(queue_name, config['queue_name_filter'], config['excluded_queue_names']):
                continue

            queues_processed += 1

            # Check if alarm already exists
            if alarm_exists(queue_name, config['alarm_name_prefix']):
                continue

            # Create alarm
            if create_cloudwatch_alarm(queue_name, config):
                alarms_created += 1

        result = {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'SQS monitoring remediation completed successfully',
                'total_queues': len(queue_urls),
                'queues_processed': queues_processed,
                'alarms_created': alarms_created
            })
        }

        logger.info(f"Remediation completed: {result['body']}")
        return result

    except Exception as e:
        logger.exception(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'message': 'SQS monitoring remediation failed'
            })
        }
