"""
This test suite provides testing for the SQS monitoring Lambda function.

SETUP:
------
1. Create and activate virtual environment:
   python3 -m venv venv
   source venv/bin/activate

2. Install testing dependencies:
   pip install -r requirements-test.txt

USAGE:
------
# Run all tests with verbose output
pytest test_lambda_pytest.py -v

# Run specific test classes
pytest test_lambda_pytest.py::TestUtilityFunctions -v
pytest test_lambda_pytest.py::TestMockedAWS -v
pytest test_lambda_pytest.py::TestLambdaHandler -v
pytest test_lambda_pytest.py::TestWithMoto -v

# Run specific test methods
pytest test_lambda_pytest.py::TestUtilityFunctions::test_extract_queue_name -v

# Run tests and stop on first failure
pytest test_lambda_pytest.py -x

"""

import os
import json
import boto3
import pytest
from unittest.mock import patch, MagicMock
from moto import mock_aws

from lambda_function import (
    lambda_handler, 
    list_all_sqs_queues, 
    extract_queue_name,
    should_monitor_queue,
    alarm_exists,
    create_cloudwatch_alarm
)

# Set up test environment
os.environ.update({
    'ALARM_THRESHOLD': '300',
    'ALARM_NAME_PREFIX': 'TEST-SQS-OldestMessageAge',
    'ALARM_EVALUATION_PERIODS': '1',
    'ALARM_PERIOD': '300',
    'ALARM_STATISTIC': 'Maximum',
    'ALARM_COMPARISON_OPERATOR': 'GreaterThanThreshold',
    'SNS_TOPIC_ARN': 'arn:aws:sns:us-east-1:123456789012:test-topic',
    'TREAT_MISSING_DATA': 'missing',
    'QUEUE_NAME_FILTER': '',
    'EXCLUDED_QUEUE_NAMES': ''
})

@pytest.fixture
def lambda_context():
    """Mock Lambda context."""
    context = MagicMock()
    context.function_name = "test-function"
    context.aws_request_id = "test-request-id"
    context.get_remaining_time_in_millis.return_value = 30000
    return context

@pytest.fixture
def sample_config():
    """Sample configuration for testing."""
    return {
        'threshold': 300.0,
        'alarm_name_prefix': 'TEST-SQS-OldestMessageAge',
        'evaluation_periods': 1,
        'period': 300,
        'statistic': 'Maximum',
        'comparison_operator': 'GreaterThanThreshold',
        'sns_topic_arn': 'arn:aws:sns:us-east-1:123456789012:test-topic',
        'treat_missing_data': 'missing',
        'queue_name_filter': '',
        'excluded_queue_names': []
    }

class TestUtilityFunctions:
    """Test utility functions."""

    def test_extract_queue_name(self):
        """Test queue name extraction from URL."""
        url = "https://sqs.us-east-1.amazonaws.com/123456789012/my-test-queue"
        assert extract_queue_name(url) == "my-test-queue"

    def test_should_monitor_queue_no_filters(self):
        """Test queue monitoring decision with no filters."""
        assert should_monitor_queue("test-queue", "", []) == True

    def test_should_monitor_queue_with_exclusion(self):
        """Test queue monitoring with exclusion list."""
        assert should_monitor_queue("temp-queue", "", ["temp-queue", "test-queue"]) == False
        assert should_monitor_queue("prod-queue", "", ["temp-queue", "test-queue"]) == True

    def test_should_monitor_queue_with_filter(self):
        """Test queue monitoring with name filter."""
        assert should_monitor_queue("prod-queue", "^prod-.*", []) == True
        assert should_monitor_queue("test-queue", "^prod-.*", []) == False

    def test_should_monitor_queue_invalid_regex(self):
        """Test queue monitoring with invalid regex (should raise ValueError)."""
        # Invalid regex should raise ValueError with clear error message
        with pytest.raises(ValueError, match="Invalid QUEUE_NAME_FILTER regex pattern"):
            should_monitor_queue("any-queue", "[invalid", [])

class TestMockedAWS:
    """Test with mocked AWS services."""

    @patch('lambda_function.sqs_client')
    def test_list_all_sqs_queues(self, mock_sqs):
        """Test SQS queue listing."""
        mock_sqs.get_paginator.return_value.paginate.return_value = [
            {
                'QueueUrls': [
                    'https://sqs.us-east-1.amazonaws.com/123456789012/queue1',
                    'https://sqs.us-east-1.amazonaws.com/123456789012/queue2'
                ]
            }
        ]

        queues = list_all_sqs_queues()
        assert len(queues) == 2
        assert 'queue1' in queues[0]
        assert 'queue2' in queues[1]

    @patch('lambda_function.cloudwatch_client')
    def test_alarm_exists_true(self, mock_cloudwatch):
        """Test alarm existence check when alarm exists."""
        mock_cloudwatch.describe_alarms_for_metric.return_value = {
            'MetricAlarms': [
                {'AlarmName': 'TEST-SQS-OldestMessageAge-test-queue'}
            ]
        }

        assert alarm_exists("test-queue", "TEST-SQS-OldestMessageAge") == True

    @patch('lambda_function.cloudwatch_client')
    def test_alarm_exists_false(self, mock_cloudwatch):
        """Test alarm existence check when alarm doesn't exist."""
        mock_cloudwatch.describe_alarms_for_metric.return_value = {
            'MetricAlarms': []
        }

        assert alarm_exists("test-queue", "TEST-SQS-OldestMessageAge") == False

    @patch('lambda_function.cloudwatch_client')
    def test_create_cloudwatch_alarm(self, mock_cloudwatch, sample_config):
        """Test CloudWatch alarm creation."""
        mock_cloudwatch.put_metric_alarm.return_value = {}

        result = create_cloudwatch_alarm("test-queue", sample_config)
        assert result == True

        # Verify the alarm was created with correct parameters
        mock_cloudwatch.put_metric_alarm.assert_called_once()
        call_args = mock_cloudwatch.put_metric_alarm.call_args[1]

        assert call_args['AlarmName'] == 'TEST-SQS-OldestMessageAge-test-queue'
        assert call_args['MetricName'] == 'ApproximateAgeOfOldestMessage'
        assert call_args['Namespace'] == 'AWS/SQS'
        assert call_args['Threshold'] == 300.0
        assert call_args['Dimensions'][0]['Name'] == 'QueueName'
        assert call_args['Dimensions'][0]['Value'] == 'test-queue'

class TestLambdaHandler:
    """Test the main Lambda handler function."""

    @patch('lambda_function.cloudwatch_client')
    @patch('lambda_function.sqs_client')
    def test_lambda_handler_success(self, mock_sqs, mock_cloudwatch, lambda_context):
        """Test successful Lambda execution."""
        # Mock SQS response
        mock_sqs.get_paginator.return_value.paginate.return_value = [
            {
                'QueueUrls': [
                    'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue-1',
                    'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue-2'
                ]
            }
        ]

        # Mock CloudWatch responses (no existing alarms)
        mock_cloudwatch.describe_alarms_for_metric.return_value = {'MetricAlarms': []}
        mock_cloudwatch.put_metric_alarm.return_value = {}

        # Execute Lambda
        result = lambda_handler({}, lambda_context)

        # Verify response
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['total_queues'] == 2
        assert body['queues_processed'] == 2
        assert body['alarms_created'] == 2
        assert 'successfully' in body['message']

    @patch('lambda_function.cloudwatch_client')
    @patch('lambda_function.sqs_client')
    def test_lambda_handler_no_queues(self, mock_sqs, mock_cloudwatch, lambda_context):
        """Test Lambda execution when no queues exist."""
        # Mock empty SQS response
        mock_sqs.get_paginator.return_value.paginate.return_value = [{}]

        # Execute Lambda
        result = lambda_handler({}, lambda_context)

        # Verify response
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['queues_processed'] == 0
        assert body['alarms_created'] == 0
        assert 'No SQS queues found' in body['message']

    @patch('lambda_function.cloudwatch_client')
    @patch('lambda_function.sqs_client')
    def test_lambda_handler_existing_alarms(self, mock_sqs, mock_cloudwatch, lambda_context):
        """Test Lambda execution when alarms already exist."""
        # Mock SQS response
        mock_sqs.get_paginator.return_value.paginate.return_value = [
            {
                'QueueUrls': ['https://sqs.us-east-1.amazonaws.com/123456789012/existing-queue']
            }
        ]

        # Mock CloudWatch response (alarm already exists)
        mock_cloudwatch.describe_alarms_for_metric.return_value = {
            'MetricAlarms': [
                {'AlarmName': 'TEST-SQS-OldestMessageAge-existing-queue'}
            ]
        }

        # Execute Lambda
        result = lambda_handler({}, lambda_context)

        # Verify response
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['total_queues'] == 1
        assert body['queues_processed'] == 1
        assert body['alarms_created'] == 0  # No new alarms created

    @patch('lambda_function.sqs_client')
    def test_lambda_handler_error(self, mock_sqs, lambda_context):
        """Test Lambda execution with error."""
        # Mock SQS to raise an exception
        mock_sqs.get_paginator.side_effect = Exception("AWS API Error")

        # Execute Lambda
        result = lambda_handler({}, lambda_context)

        # Verify error response
        assert result['statusCode'] == 500
        body = json.loads(result['body'])
        assert 'error' in body
        assert 'failed' in body['message']

class TestWithMoto:
    """Test with moto (AWS service mocking)."""

    @mock_aws
    def test_end_to_end_with_moto(self, lambda_context):
        """End-to-end test using moto for AWS service mocking."""
        # Create mock SQS queues
        sqs = boto3.client('sqs', region_name='us-east-1')
        cloudwatch = boto3.client('cloudwatch', region_name='us-east-1')

        # Create test queues (variables not used but queues are created in mocked SQS)
        sqs.create_queue(QueueName='test-queue-1')
        sqs.create_queue(QueueName='test-queue-2')

        # Execute Lambda (this will use moto's mocked services)
        with patch('lambda_function.sqs_client', sqs), \
             patch('lambda_function.cloudwatch_client', cloudwatch):

            result = lambda_handler({}, lambda_context)

        # Verify response
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['total_queues'] == 2
        assert body['queues_processed'] == 2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
