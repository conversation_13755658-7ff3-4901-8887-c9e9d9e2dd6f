# ==========================================
# SQS Monitoring Module Variables
# ==========================================

variable "alarm_threshold" {
  description = "Threshold in seconds for the ApproximateAgeOfOldestMessage metric"
  type        = number
  default     = 300
}

variable "alarm_name_prefix" {
  description = "Prefix for CloudWatch alarm names"
  type        = string
  default     = "SQS-OldestMessageAge"
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 5
}

variable "alarm_period" {
  description = "Period in seconds for the alarm evaluation"
  type        = number
  default     = 300
}

variable "alarm_statistic" {
  description = "Statistic to use for the alarm"
  type        = string
  default     = "Maximum"
  validation {
    condition     = contains(["Average", "Maximum", "Minimum", "Sum", "SampleCount"], var.alarm_statistic)
    error_message = "Alarm statistic must be one of: Average, Maximum, Minimum, Sum, SampleCount."
  }
}

variable "alarm_comparison_operator" {
  description = "Comparison operator for the alarm"
  type        = string
  default     = "GreaterThanThreshold"
  validation {
    condition = contains([
      "GreaterThanOrEqualToThreshold",
      "GreaterThanThreshold",
      "LessThanThreshold",
      "LessThanOrEqualToThreshold"
    ], var.alarm_comparison_operator)
    error_message = "Comparison operator must be a valid CloudWatch alarm comparison operator."
  }
}

variable "sns_topic_arn" {
  description = "SNS topic ARN for alarm notifications"
  type        = string
  default     = null
}

variable "lambda_function_name" {
  description = "Name of the Lambda function"
  type        = string
  default     = "sqs-monitoring-remediation"
}

variable "lambda_timeout" {
  description = "Timeout for the Lambda function in seconds"
  type        = number
  default     = 300
}

variable "lambda_memory_size" {
  description = "Memory size for the Lambda function in MB"
  type        = number
  default     = 256
}

variable "schedule_expression" {
  description = "EventBridge schedule expression for triggering the Lambda"
  type        = string
  default     = "cron(0 0 * * ? *)" # Daily at midnight UTC
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 365
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "default_tags" {
  description = "Default tags to apply to resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Module      = "sqs_monitoring"
    Purpose     = "VantaCompliance"
    Environment = "unknown"
  }
}

variable "enable_lambda_function" {
  description = "Whether to create the Lambda function and related resources"
  type        = bool
  default     = true
}

variable "treat_missing_data" {
  description = "How to treat missing data for CloudWatch alarms"
  type        = string
  default     = "missing"
  validation {
    condition     = contains(["breaching", "notBreaching", "ignore", "missing"], var.treat_missing_data)
    error_message = "treat_missing_data must be one of: breaching, notBreaching, ignore, missing."
  }
}

variable "queue_name_filter" {
  description = "Optional regex pattern to filter queue names (empty string means no filter)"
  type        = string
  default     = ""
}

variable "excluded_queue_names" {
  description = "List of queue names to exclude from monitoring"
  type        = list(string)
  default     = []
}
