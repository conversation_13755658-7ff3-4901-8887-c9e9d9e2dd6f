# ==========================================
# SQS Monitoring Module - Main Configuration
# ==========================================

# Consolidated tags
locals {
  tags = merge(var.default_tags, var.tags)
}

# Data source for current AWS region and account
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# ==========================================
# Lambda Function Package
# ==========================================

# Create ZIP file for Lambda function
data "archive_file" "lambda_zip" {
  count       = var.enable_lambda_function ? 1 : 0
  type        = "zip"
  output_path = "${path.module}/sqs_monitoring_lambda.zip"

  source {
    content  = file("${path.module}/lambda_function.py")
    filename = "lambda_function.py"
  }
}

# ==========================================
# IAM Role and Policies for Lambda
# ==========================================

# Lambda execution role
resource "aws_iam_role" "lambda_execution_role" {
  count = var.enable_lambda_function ? 1 : 0
  name  = "${var.lambda_function_name}-execution-role"
  tags  = local.tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# Policy for Lambda to access SQS and CloudWatch
resource "aws_iam_policy" "lambda_sqs_cloudwatch_policy" {
  count       = var.enable_lambda_function ? 1 : 0
  name        = "${var.lambda_function_name}-sqs-cloudwatch-policy"
  description = "Policy for SQS monitoring Lambda to access SQS and CloudWatch"
  tags        = local.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sqs:ListQueues",
          "sqs:GetQueueAttributes"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:DescribeAlarmsForMetric",
          "cloudwatch:PutMetricAlarm",
          "cloudwatch:TagResource"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.lambda_function_name}*"
      }
    ]
  })
}

# Optional SNS publish policy if SNS topic is provided
resource "aws_iam_policy" "lambda_sns_policy" {
  count       = var.enable_lambda_function && var.sns_topic_arn != null ? 1 : 0
  name        = "${var.lambda_function_name}-sns-policy"
  description = "Policy for SQS monitoring Lambda to publish to SNS"
  tags        = local.tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = var.sns_topic_arn
      }
    ]
  })
}

# Attach basic execution role policy
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  count      = var.enable_lambda_function ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Attach SQS and CloudWatch policy
resource "aws_iam_role_policy_attachment" "lambda_sqs_cloudwatch" {
  count      = var.enable_lambda_function ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = aws_iam_policy.lambda_sqs_cloudwatch_policy[0].arn
}

# Attach SNS policy if SNS topic is provided
resource "aws_iam_role_policy_attachment" "lambda_sns" {
  count      = var.enable_lambda_function && var.sns_topic_arn != null ? 1 : 0
  role       = aws_iam_role.lambda_execution_role[0].name
  policy_arn = aws_iam_policy.lambda_sns_policy[0].arn
}

# ==========================================
# CloudWatch Log Group
# ==========================================

resource "aws_cloudwatch_log_group" "lambda_logs" {
  count             = var.enable_lambda_function ? 1 : 0
  name              = "/aws/lambda/${var.lambda_function_name}"
  retention_in_days = var.log_retention_days
  tags              = local.tags

  lifecycle {
    prevent_destroy = false
    ignore_changes  = [retention_in_days] # Allow overwrite by cw_log_retention module
  }
}

# ==========================================
# Lambda Function
# ==========================================

resource "aws_lambda_function" "sqs_monitoring" {
  count            = var.enable_lambda_function ? 1 : 0
  filename         = data.archive_file.lambda_zip[0].output_path
  function_name    = var.lambda_function_name
  role             = aws_iam_role.lambda_execution_role[0].arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.12"
  timeout          = var.lambda_timeout
  memory_size      = var.lambda_memory_size
  source_code_hash = data.archive_file.lambda_zip[0].output_base64sha256
  tags             = local.tags

  environment {
    variables = {
      ALARM_THRESHOLD           = var.alarm_threshold
      ALARM_NAME_PREFIX         = var.alarm_name_prefix
      ALARM_EVALUATION_PERIODS  = var.alarm_evaluation_periods
      ALARM_PERIOD              = var.alarm_period
      ALARM_STATISTIC           = var.alarm_statistic
      ALARM_COMPARISON_OPERATOR = var.alarm_comparison_operator
      SNS_TOPIC_ARN             = var.sns_topic_arn
      TREAT_MISSING_DATA        = var.treat_missing_data
      QUEUE_NAME_FILTER         = var.queue_name_filter
      EXCLUDED_QUEUE_NAMES      = join(",", var.excluded_queue_names)
    }
  }

  depends_on = [
    aws_cloudwatch_log_group.lambda_logs,
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_iam_role_policy_attachment.lambda_sqs_cloudwatch
  ]
}

# ==========================================
# EventBridge Rule for Scheduling
# ==========================================

resource "aws_cloudwatch_event_rule" "sqs_monitoring_schedule" {
  count               = var.enable_lambda_function ? 1 : 0
  name                = "${var.lambda_function_name}-schedule"
  description         = "Trigger SQS monitoring Lambda on schedule"
  schedule_expression = var.schedule_expression
  tags                = local.tags
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  count     = var.enable_lambda_function ? 1 : 0
  rule      = aws_cloudwatch_event_rule.sqs_monitoring_schedule[0].name
  target_id = "SQSMonitoringLambdaTarget"
  arn       = aws_lambda_function.sqs_monitoring[0].arn
}

resource "aws_lambda_permission" "allow_eventbridge" {
  count         = var.enable_lambda_function ? 1 : 0
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sqs_monitoring[0].function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.sqs_monitoring_schedule[0].arn
}
