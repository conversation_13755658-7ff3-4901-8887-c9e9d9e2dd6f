# SQS Monitoring Module

This Terraform module implements dynamic remediation for monitoring AWS SQS queues to ensure compliance with <PERSON><PERSON>'s "Messaging queue message age monitored" test. The module automatically discovers all SQS queues in an AWS account and creates CloudWatch alarms for the `ApproximateAgeOfOldestMessage` metric if they don't already exist.

## Features

- **Automatic Discovery**: Discovers all SQS queues in the AWS account
- **Idempotent Operation**: Only creates alarms if they don't already exist
- **Configurable Thresholds**: Customizable alarm parameters
- **Filtering Support**: Optional queue name filtering and exclusions
- **Multi-Region Support**: Can be deployed across multiple AWS regions
- **Compliance Ready**: Designed specifically for Vanta SOC2 compliance
- **Scheduled Execution**: Runs on a configurable schedule via EventBridge

## Architecture

The module creates:

1. **AWS Lambda Function**: Python 3.12 runtime that performs the monitoring logic
2. **IAM Role and Policies**: Least-privilege permissions for SQS and CloudWatch access
3. **CloudWatch Log Group**: For Lambda execution logs with configurable retention
4. **EventBridge Rule**: Scheduled trigger for the Lambda function
5. **CloudWatch Alarms**: Created dynamically for each discovered SQS queue

## Usage

### Basic Usage

```hcl
module "sqs_monitoring" {
  source = "./modules/sqs_monitoring"

  lambda_function_name = "sqs-monitoring-remediation"
  sns_topic_arn       = "arn:aws:sns:us-east-1:123456789012:cloudwatch-alarms"
  
  tags = {
    Environment = "production"
    Compliance  = "Vanta"
  }
}
```

### Advanced Configuration

```hcl
module "sqs_monitoring" {
  source = "./modules/sqs_monitoring"

  # Lambda configuration
  lambda_function_name = "sqs-monitoring-remediation"
  lambda_timeout      = 300
  lambda_memory_size  = 512

  # Alarm configuration
  alarm_threshold            = 600  # 10 minutes
  alarm_name_prefix         = "SQS-MessageAge"
  alarm_evaluation_periods  = 2
  alarm_period              = 300
  alarm_statistic           = "Maximum"
  alarm_comparison_operator = "GreaterThanThreshold"
  treat_missing_data        = "missing"

  # Notification
  sns_topic_arn = "arn:aws:sns:us-east-1:123456789012:cloudwatch-alarms"

  # Scheduling
  schedule_expression = "cron(0 2 * * ? *)"  # Daily at 2 AM UTC

  # Filtering
  queue_name_filter    = "^prod-.*"  # Only monitor queues starting with "prod-"
  excluded_queue_names = ["temp-queue", "test-queue"]

  # Logging
  log_retention_days = 30

  tags = {
    Environment = "production"
    Compliance  = "Vanta"
    Squad       = "Platform"
  }
}
```

## CloudWatch Alarm Configuration

The module creates CloudWatch alarms with the following default configuration:

- **Metric**: `ApproximateAgeOfOldestMessage`
- **Namespace**: `AWS/SQS`
- **Threshold**: 300 seconds (5 minutes)
- **Comparison**: `GreaterThanThreshold`
- **Evaluation Periods**: 1
- **Period**: 300 seconds
- **Statistic**: `Maximum`
- **Missing Data Treatment**: `missing`

## Permissions

The Lambda function requires the following permissions:

### SQS Permissions
- `sqs:ListQueues` - To discover all queues
- `sqs:GetQueueAttributes` - To get queue metadata

### CloudWatch Permissions
- `cloudwatch:DescribeAlarmsForMetric` - To check existing alarms
- `cloudwatch:PutMetricAlarm` - To create new alarms
- `cloudwatch:TagResource` - To tag created alarms

### CloudWatch Logs Permissions
- `logs:CreateLogGroup` - To create log groups
- `logs:CreateLogStream` - To create log streams
- `logs:PutLogEvents` - To write log events

### SNS Permissions (Optional)
- `sns:Publish` - To send notifications (only if SNS topic is configured)

## Monitoring and Troubleshooting

### Lambda Logs

The Lambda function logs detailed information about its execution:

```
INFO: Starting SQS monitoring remediation
INFO: Configuration: {"threshold": 300, "alarm_name_prefix": "SQS-OldestMessageAge", ...}
INFO: Found 5 SQS queues
INFO: Skipping excluded queue: temp-queue
INFO: Alarm already exists for queue my-queue: SQS-OldestMessageAge-my-queue
INFO: Created CloudWatch alarm: SQS-OldestMessageAge-new-queue
INFO: Remediation completed: {"total_queues": 5, "queues_processed": 4, "alarms_created": 1}
```

### Common Issues

1. **Permission Errors**: Ensure the Lambda execution role has all required permissions
2. **Timeout Issues**: Increase `lambda_timeout` for accounts with many queues
3. **Alarm Creation Failures**: Check CloudWatch service limits and alarm naming conflicts

## Compliance Notes

This module is designed to meet Vanta's "Messaging queue message age monitored" compliance test by:

1. **Automatic Discovery**: Ensures all SQS queues are monitored
2. **Consistent Monitoring**: Creates standardized alarms across all queues
3. **Audit Trail**: Provides detailed logging of all actions
4. **Tagging**: Tags all created resources for compliance tracking

## Cost Considerations

- **Lambda Execution**: Minimal cost for scheduled execution
- **CloudWatch Alarms**: $0.10 per alarm per month (standard pricing)
- **CloudWatch Logs**: Based on log retention and volume
- **EventBridge**: Minimal cost for scheduled rules

For an account with 50 SQS queues, expect approximately $5/month in CloudWatch alarm costs.

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| alarm_threshold | Threshold in seconds for the ApproximateAgeOfOldestMessage metric | `number` | `300` | no |
| alarm_name_prefix | Prefix for CloudWatch alarm names | `string` | `"SQS-OldestMessageAge"` | no |
| alarm_evaluation_periods | Number of evaluation periods for the alarm | `number` | `1` | no |
| alarm_period | Period in seconds for the alarm evaluation | `number` | `300` | no |
| alarm_statistic | Statistic to use for the alarm | `string` | `"Maximum"` | no |
| alarm_comparison_operator | Comparison operator for the alarm | `string` | `"GreaterThanThreshold"` | no |
| sns_topic_arn | SNS topic ARN for alarm notifications | `string` | `null` | no |
| lambda_function_name | Name of the Lambda function | `string` | `"sqs-monitoring-remediation"` | no |
| lambda_timeout | Timeout for the Lambda function in seconds | `number` | `300` | no |
| lambda_memory_size | Memory size for the Lambda function in MB | `number` | `256` | no |
| schedule_expression | EventBridge schedule expression for triggering the Lambda | `string` | `"cron(0 0 * * ? *)"` | no |
| log_retention_days | CloudWatch log retention in days | `number` | `365` | no |
| enable_lambda_function | Whether to create the Lambda function and related resources | `bool` | `true` | no |
| treat_missing_data | How to treat missing data for CloudWatch alarms | `string` | `"missing"` | no |
| queue_name_filter | Optional regex pattern to filter queue names | `string` | `""` | no |
| excluded_queue_names | List of queue names to exclude from monitoring | `list(string)` | `[]` | no |
| tags | Tags to apply to resources | `map(string)` | `{}` | no |
| default_tags | Default tags to apply to resources | `map(string)` | See variables.tf | no |

## Outputs

| Name | Description |
|------|-------------|
| lambda_function_arn | ARN of the Lambda function |
| lambda_function_name | Name of the Lambda function |
| lambda_role_arn | ARN of the Lambda execution role |
| eventbridge_rule_arn | ARN of the EventBridge rule |
| cloudwatch_log_group_name | Name of the CloudWatch log group |
| sns_topic_arn | SNS topic ARN used for alarm notifications |

## Testing

### Local Testing

Test the Lambda function code locally before deployment:

```bash
# 1. Setup virtual environment
cd modules/sqs_monitoring
python3 -m venv venv
source venv/bin/activate

# 2. Install testing dependencies
pip install -r requirements-test.txt

# 3. Run comprehensive test suite
pytest test_lambda_pytest.py -v
```
