# CloudWatch Log Retention Module

> Enforces ≥ **365‑day** (or your chosen) retention on *every* CloudWatch Log Group in an AWS account & region.

## How it works
1. **Lambda** scans all log groups on cold start and applies the desired retention.
2. **EventBridge rule** triggers the Lambda instantly whenever `CreateLogGroup` is logged via CloudTrail.
3. A **scheduled rule** (every 6h by default) provides ongoing drift remediation.

## Drift‑free IaC for log groups created elsewhere
If your teams provision *specific* log groups via Terraform and wish to manage the retention inline, add a **lifecycle** stanza to silence churn from this module:

```hcl
resource "aws_cloudwatch_log_group" "example" {
  name              = "/aws/lambda/my‑other‑function"
  retention_in_days = 30  # team‑specific value

  lifecycle {
    ignore_changes = [retention_in_days]
  }
}
```

