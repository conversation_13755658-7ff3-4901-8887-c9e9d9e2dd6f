import os
import boto3
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext

logger = Logger()

retention_days = int(os.environ.get("RETENTION_DAYS", "365"))
logs = boto3.client("logs")

def enforce_retention(log_group_name: str) -> None:
    """Enforce log retention policy for a single CloudWatch log group."""
    try:
        response = logs.describe_log_groups(logGroupNamePrefix=log_group_name)
        group = next((g for g in response.get("logGroups", []) if g["logGroupName"] == log_group_name), None)
        if not group:
            logger.warning(f"Log group {log_group_name} not found.")
            return

        _update_retention(group)
    except Exception as e:
        logger.error(f"Error enforcing retention for {log_group_name}: {e}")


def enforce_retention_all() -> None:
    """Enforce log retention policy for all CloudWatch log groups."""
    paginator = logs.get_paginator("describe_log_groups")
    for page in paginator.paginate():
        for group in page.get("logGroups", []):
            _update_retention(group)


def _update_retention(group: dict) -> None:
    """Update the retention policy for a given log group."""
    current = group.get("retentionInDays")
    if current is None or current < retention_days:
        try:
            logs.put_retention_policy(
                logGroupName=group["logGroupName"],
                retentionInDays=retention_days,
            )
            logger.info(f"Set {group['logGroupName']} to {retention_days} days (prev={current})")
        except Exception as e:
            logger.error(f"Failed to set retention for {group['logGroupName']}: {e}")
    else:
        logger.info(f"Skipping {group['logGroupName']} (current={current}, target={retention_days})")


def handler(event: dict, context: LambdaContext) -> None:
    """Entrypoint for both CreateLogGroup and scheduled EventBridge triggers."""
    logger.info("Event received -> enforcing retention …")
    logger.info(f"Event: {event}")

    if (
        event.get("source") == "aws.logs"
        and event.get("detail-type") == "AWS API Call via CloudTrail"
        and event.get("detail", {}).get("eventName") == "CreateLogGroup"
    ):
        log_group_name = event.get("detail", {}).get("requestParameters", {}).get("logGroupName")
        if log_group_name:
            logger.info(f"Triggered by CreateLogGroup event for {log_group_name}, enforcing retention …")
            enforce_retention(log_group_name)
        else:
            logger.warning("CreateLogGroup event did not contain a logGroupName, falling back to full scan.")
            enforce_retention_all()
    else:
        logger.info("Triggered by scheduled EventBridge event or other, enforcing retention for all groups…")
        enforce_retention_all()

    logger.info("All done!")
