variable "retention_days" {
  type        = number
  description = "Minimum number of days to retain CloudWatch logs."
  default     = 365
}

variable "check_schedule" {
  type        = string
  description = "Schedule for the CloudWatch Events rule to trigger the Lambda function."
  default     = "rate(6 hours)"
}

variable "tags" {
  type        = map(string)
  description = "A map of tags to add to all resources."
  default     = {}
}

variable "default_tags" {
  type        = map(string)
  description = "A map of default tags to add to all resources."
  default     = {}
}
