##############################################################################
# CMK FOR LAMBDA ENV-VAR ENCRYPTION
##############################################################################
resource "aws_kms_key" "lambda_env" {
  description         = "CMK - encrypt Lambda environment variables (cw-log-retention)"
  enable_key_rotation = true
  # Key policy allows only *this* account’s admins + the Lambda service
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      # Account admins
      {
        Sid       = "AllowAccountAdmins",
        Effect    = "Allow",
        Principal = { AWS = "arn:aws:iam::${data.aws_caller_identity.this.account_id}:root" },
        Action    = "kms:*",
        Resource  = "*"
      },
      # Lambda service principal
      {
        Sid       = "AllowLambdaServiceUse",
        Effect    = "Allow",
        Principal = { Service = "lambda.amazonaws.com" },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        Resource = "*",
        Condition = {
          ArnLike = {
            "kms:EncryptionContext:LambdaFunctionArn" = "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.this.account_id}:function:*"
          }
        }
      }
    ]
  })
  tags = local.tags
}

resource "aws_kms_alias" "lambda_env" {
  name          = "alias/cw-log-retention-lambda-env"
  target_key_id = aws_kms_key.lambda_env.key_id
}

##############################################################################
# CMK FOR CLOUDWATCH LOG GROUP ENCRYPTION
##############################################################################
resource "aws_kms_key" "cw_logs" {
  description         = "CMK - encrypt CloudWatch Logs for cw-log-retention Lambda"
  enable_key_rotation = true
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      # Account admins
      {
        Sid       = "AllowAccountAdmins",
        Effect    = "Allow",
        Principal = { AWS = "arn:aws:iam::${data.aws_caller_identity.this.account_id}:root" },
        Action    = "kms:*",
        Resource  = "*"
      },
      # CloudWatch Logs service principal (region-specific!)
      {
        Sid    = "AllowCWLogsUse",
        Effect = "Allow",
        Principal = {
          Service = "logs.${data.aws_region.current.name}.amazonaws.com"
        },
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        Resource = "*",
        Condition = {
          ArnLike = {
            "kms:EncryptionContext:aws:logs:arn" = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.this.account_id}:log-group:*"
          }
        }
      }
    ]
  })
  tags = local.tags
}

resource "aws_kms_alias" "cw_logs" {
  name          = "alias/cw-log-retention-cw-logs"
  target_key_id = aws_kms_key.cw_logs.key_id
}
