variable "instance_id" {
  description = "The ID of the instance to use for the role"
}

variable "aws_region" {
  description = "The AWS region to use for the role"
}

variable "aws_account_id" {
  description = "The AWS account ID to use for the role"
}

variable "db_user_name" {
  description = "The name of the database role"
  default     = "eyecue_readonly_user"
}

variable "roles_allowed_to_assume" {
  description = "The roles allowed to assume this role"
  default     = ["DevAccess"]
}

variable "tags" {
  description = "A map of tags to tag resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}