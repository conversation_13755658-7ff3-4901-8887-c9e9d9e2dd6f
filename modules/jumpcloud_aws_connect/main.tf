# Development Environment Role and Policy
resource "aws_iam_role" "jumpcloud_aws_connect_role_dev" {
  name = "JumpCloudAWSConnectRole-Dev"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = var.dev_saml_provider_arn
        },
        Action = "sts:AssumeRoleWithSAML",
        Condition = {
          StringEquals = {
            "SAML:aud" = "https://signin.aws.amazon.com/saml"
          }
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "aws_connect_policy_dev" {
  name = "AWSConnectFederationPolicy-Dev"
  role = aws_iam_role.jumpcloud_aws_connect_role_dev.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid      = "Statement1",
        Effect   = "Allow",
        Action   = "connect:GetFederationToken",
        Resource = var.dev_connect_instances
      }
    ]
  })
}

# Production Environment Role and Policy
resource "aws_iam_role" "jumpcloud_aws_connect_role_prod" {
  name = "JumpCloudAWSConnectRole-Prod"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = var.prod_saml_provider_arn
        },
        Action = "sts:AssumeRoleWithSAML",
        Condition = {
          StringEquals = {
            "SAML:aud" = "https://signin.aws.amazon.com/saml"
          }
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "aws_connect_policy_prod" {
  name = "AWSConnectFederationPolicy-Prod"
  role = aws_iam_role.jumpcloud_aws_connect_role_prod.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid      = "Statement1",
        Effect   = "Allow",
        Action   = "connect:GetFederationToken",
        Resource = var.prod_connect_instances
      }
    ]
  })
}
