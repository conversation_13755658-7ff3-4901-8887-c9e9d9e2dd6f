variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "region" {
  type    = string
  default = "ap-southeast-2"
}

variable "bucket_name" {
  type = string
}

variable "enable_versioning" {
  default = true
}

variable "acl" {
  default = "private"
}

variable "block_public_policy" {
  default = true
}

variable "block_public_acls" {
  default = true
}

variable "restrict_public_buckets" {
  default = true
}

variable "ignore_public_acls" {
  default = true
}
