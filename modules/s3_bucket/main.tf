module "s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source                  = "terraform-aws-modules/s3-bucket/aws"
  version                 = "3.14.0"
  bucket                  = var.bucket_name
  acl                     = var.acl
  block_public_policy     = var.block_public_policy
  block_public_acls       = var.block_public_acls
  restrict_public_buckets = var.restrict_public_buckets
  ignore_public_acls      = var.ignore_public_acls
  versioning = {
    enabled = var.enable_versioning
  }
  tags = merge(var.tags, { "Name" = var.bucket_name })
}
