variable "mimir_aws_account_id" {
  description = "AWS Account ID for the assumed role principal"
  type        = string
}

variable "lambda_role_arn_suffixes" {
  description = "List of Lambda role ARN suffixes"
  type        = list(string)
}

variable "AWS_ACCOUNT_ID" {
  description = "AWS account ID for the current account"
  type        = string
}

variable "AWS_REGION" {
  description = "AWS region for the current account"
  type        = string
}

variable "policy_description" {
  description = "Description for eyecue-mimir policy"
  type        = string
}

variable "tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}