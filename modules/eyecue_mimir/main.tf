resource "aws_iam_role" "mimir-lambda-invocation-role" {
  name = "mimir-lambda-invocation-role"
  assume_role_policy = jsonencode(
    {
      Statement = [
        {
          Action = "sts:AssumeRole"
          Effect = "Allow"
          Principal = {
            AWS = [for arn_suffix in var.lambda_role_arn_suffixes : "arn:aws:iam::${var.mimir_aws_account_id}:role/${arn_suffix}"]
          }
        },
      ]
      Version = "2012-10-17"
    }
  )
  description = "Role to invoke request-roisuggestor-png lambda from cross accounts."
  tags        = var.tags
}

resource "aws_iam_policy" "mimir-lambda-invocation-policy" {
  name = "mimir-lambda-invocation-policy"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "lambda:TagResource",
            "lambda:InvokeFunction",
            "lambda:GetLayerVersion",
            "lambda:GetEventSourceMapping",
            "lambda:GetFunction",
            "lambda:GetFunctionUrlConfig",
            "lambda:InvokeAsync",
            "lambda:GetFunctionConcurrency",
            "lambda:ListTags",
            "lambda:GetAlias",
            "lambda:ListFunctions",
            "lambda:GetAccountSettings",
            "lambda:GetPolicy",
          ]
          Effect   = "Allow"
          Resource = "arn:aws:lambda:${var.AWS_REGION}:${var.AWS_ACCOUNT_ID}:function:request-roisuggestor-png"
          Sid      = "mimirlambdainvocationpolicy"
        },
      ]
      Version = "2012-10-17"
    }
  )
  description = var.policy_description
  tags        = var.tags
}

resource "aws_iam_role_policy_attachment" "mimir_lambda_invocation_role_policy" {
  role       = aws_iam_role.mimir-lambda-invocation-role.name
  policy_arn = aws_iam_policy.mimir-lambda-invocation-policy.arn
}
