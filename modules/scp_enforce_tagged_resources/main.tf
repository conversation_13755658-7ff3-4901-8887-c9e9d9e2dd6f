resource "aws_organizations_policy" "tagged_resource_scp" {
  name        = "EnforceResourceTagging"
  description = "Deny creation of resources that do not have specific tags"

  content = jsonencode({
    Version = "2012-10-17",
    Statement = [
      for tag in var.required_tags : {
        Effect = "Deny",
        Action = [
          "iot:CreatePolicy",
          "iot:CreateTopicRule",
          "ec2:CreateVpc",
          "redshift:CreateCluster",
          "logs:CreateLogGroup",
          "kinesisanalytics:CreateApplication",
          "quicksight:CreateDashboard",
          "quicksight:CreateDataSource",
          "glue:CreateDatabase",
          "glue:CreateTable",
          "lambda:CreateFunction",
          "cloudtrail:CreateTrail",
          "ssm:CreateDocument",
          "apigateway:CreateRestApi",
          "guardduty:CreateDetector",
          "elasticloadbalancing:CreateLoadBalancer"
        ],
        Resource = "*",
        Condition = {
          Null = {
            "aws:RequestTag/${tag}" = "true"
          }
        }
      }
    ]
  })
}

# Conditionally attach SCP to OU if the OU ID is provided
resource "aws_organizations_policy_attachment" "attach_scp_to_ou" {
  count     = var.target_ou_id != null ? 1 : 0
  policy_id = aws_organizations_policy.tagged_resource_scp.id
  target_id = var.target_ou_id
}

# Conditionally attach SCP to account if the account ID is provided
resource "aws_organizations_policy_attachment" "attach_scp_to_account" {
  count     = var.target_account_id != null ? 1 : 0
  policy_id = aws_organizations_policy.tagged_resource_scp.id
  target_id = var.target_account_id
}
