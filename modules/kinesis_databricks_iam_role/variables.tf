variable "client_granted_stream_resource_arns" {
  description = "List of stream names to grant access to"
  type        = list(string)
  default     = null

}

variable "databricks_role_arn_list" {
  description = "ARN of the Databricks role to attach to the Kinesis role"
  type        = list(string)
  default     = null

}

variable "kms_key_arn_list" {
  description = "ARN of the KMS key to attach to the Kinesis role"
  type        = list(string)
  default     = null

}

variable "client_account_id" {
  description = "Client account ID"
  type        = string
  default     = null

}

variable "default_tags" {
  type = map(string)
  default = {
    "Terraform"       = "True"
    "Stack"           = "Data Sharing"
    "Product"         = "Data"
    "Squad"           = "Data"
    "Customer Facing" = "True"
    "Environment"     = "Prod"
  }
}

variable "tags" {
  type    = map(string)
  default = {}
}