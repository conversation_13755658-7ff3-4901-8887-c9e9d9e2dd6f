# Databricks data sharing policy
resource "aws_iam_role" "databricks_kinesis_role" {
  count = length(var.databricks_role_arn_list) > 0 ? 1 : 0

  name = "databricks_kinesis_${var.client_account_id}"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : var.databricks_role_arn_list
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })

  tags = merge(var.default_tags, var.tags)
}

resource "aws_iam_policy" "kinesis_policy" {
  count = length(var.databricks_role_arn_list) > 0 ? 1 : 0

  name        = "${aws_iam_role.databricks_kinesis_role[0].name}_policy"
  description = "Policy for accessing Kinesis stream and KMS"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "kinesis:ListShards",
          "kinesis:GetRecords",
          "kinesis:GetShardIterator",
          "kinesis:DescribeStream"
        ],
        "Resource" : var.client_granted_stream_resource_arns
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt",
          "kms:DescribeKey"
        ],
        "Resource" : var.kms_key_arn_list
      }
    ]
  })

  tags = merge(var.default_tags, var.tags)
}

resource "aws_iam_role_policy_attachment" "databricks_kinesis_role_attachment" {
  count = length(var.databricks_role_arn_list) > 0 ? 1 : 0

  role       = aws_iam_role.databricks_kinesis_role[0].name
  policy_arn = aws_iam_policy.kinesis_policy[0].arn
}