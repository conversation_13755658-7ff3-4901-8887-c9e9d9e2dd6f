
variable "country" {}

variable "client_name" {}

variable "bucket_name_reference" {
  default = "edw-integration"
}

variable "aws_iam_user" {
  default = "eyecue-edw"
}

variable "enable_versioning" {
  description = "Defines if the S3 bucket will version the objects"
  default     = false
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "customer_aws_account_id" {
  type    = string
  default = ""
}
