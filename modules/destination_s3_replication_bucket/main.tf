
module "s3_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.0"
  bucket  = var.s3_bucket_name
  acl     = "private"
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        kms_master_key_id = aws_kms_key.key.arn
        sse_algorithm     = "aws:kms"
      }
      bucket_key_enabled = true
    }
  }

  versioning = {
    enabled = true
  }
  tags = var.tags
}

resource "aws_kms_key" "key" {
  description             = "Used to encrypt ${var.s3_bucket_name} objects"
  deletion_window_in_days = 30
  tags                    = var.tags
}

resource "aws_kms_alias" "key_alias" {
  name          = "alias/${var.s3_bucket_name}-objects-encryption"
  target_key_id = aws_kms_key.key.arn
}


resource "aws_s3_bucket_policy" "s3_bucket_policy" {
  bucket = module.s3_bucket.s3_bucket_id
  policy = data.aws_iam_policy_document.s3_bucket_policy.json
}

data "aws_iam_policy_document" "s3_bucket_policy" {
  statement {
    actions = [
      "s3:GetBucketVersioning",
      "s3:PutBucketVersioning",
      "s3:ReplicateObject",
      "s3:ReplicateDelete",
      "s3:List*",
      "s3:ObjectOwnerOverrideToBucketOwner"
    ]

    resources = [
      "${module.s3_bucket.s3_bucket_arn}",
      "${module.s3_bucket.s3_bucket_arn}/*"
    ]

    effect = "Allow"

    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${var.source_account_id}:root",
        var.source_s3_replication_role
      ]
    }
  }
}



resource "aws_iam_user" "user" {
  name = var.iam_user_name
  tags = var.tags
}


resource "aws_iam_user_policy_attachment" "user" {
  user       = aws_iam_user.user.name
  policy_arn = aws_iam_policy.user.arn
}

resource "aws_iam_policy" "user" {
  name = "${var.iam_user_name}-iam-user-policy"

  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket",
        "s3:GetObject",
        "s3:ListObject"
      ],
      "Resource": [
        "${module.s3_bucket.s3_bucket_arn}",
        "${module.s3_bucket.s3_bucket_arn}/*"
      ]
    },
    {
        "Action": [
            "kms:Decrypt"
        ],
        "Effect": "Allow",
        "Resource": [
            "${aws_kms_key.key.arn}"
        ]
    }
  ]
}
POLICY
}