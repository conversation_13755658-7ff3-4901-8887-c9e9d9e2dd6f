
variable "iam_user_name" {
  default     = ""
  description = "IAM user name"
  type        = string
}

variable "s3_bucket_name" {
  default     = ""
  description = "S3 Bucket Name"
  type        = string
}

variable "source_account_id" {
  default     = ""
  description = "Source account id"
  type        = string
}

variable "source_s3_replication_role" {
  default     = ""
  description = "Source S3 Replication Role"
  type        = string
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}




