variable "cloudflare_zone_id" {
  type    = string
  default = "8e0e78445380f3394040f1bfaf93b74a"
}

variable "cloudflare_record_name" {
  type    = string
  default = ""
}

variable "cloudflare_record_value" {
  type    = string
  default = ""
}

variable "cloudflare_record_type" {
  type    = string
  default = "A"
}

variable "cloudflare_record_ttl" {
  type    = number
  default = 1 # 1 sets ttl to automatic
}

variable "cloudflare_api_email" {
  type    = string
  default = "<EMAIL>"
}

variable "cloudflare_api_key" {
  type    = string
  default = ""
}