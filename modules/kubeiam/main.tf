resource "aws_iam_role" "kubeiam" {
  name = var.kubeiam_role

  assume_role_policy = var.kubeiam_trustpolicy

  tags = var.tags
}

resource "aws_iam_policy" "kubeiam" {
  name   = "${var.kubeiam_role}-policy"
  policy = var.kubeiam_policy
  tags   = var.tags
}


resource "aws_iam_role_policy_attachment" "kubeiam_role_policy_attachment" {
  policy_arn = aws_iam_policy.kubeiam.arn
  role       = aws_iam_role.kubeiam.name
}

