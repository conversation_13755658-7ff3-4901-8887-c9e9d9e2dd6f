###  Destination AWS Account  ###
variable "aws_account_id" {}

variable "aws_region" {
  default = "ap-southeast-2"
}

variable "client_name" {}

variable "client_acronym" {}

variable "country" {}

variable "country_full" {}

variable "aws_iam_roles" {}

variable "keybase" {}

variable "trusted_aws_account_id" {
  default = "************"
}

variable "cloudcraft_access" {
  description = "It creates an IAM role to grant access from Cloudcraft (https://app.cloudcraft.co/)"
  type        = bool
  default     = false
}

variable "weights_bucket_name" {
  default = "eyecue-weights"
}

variable "env" {
  description = "Account Environment"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}
