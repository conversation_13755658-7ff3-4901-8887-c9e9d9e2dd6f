module "assume_role" {
  source                 = "../fingermark_users_assume_role"
  roles                  = var.aws_iam_roles
  trusted_aws_account_id = var.trusted_aws_account_id
  cloudcraft_access      = var.cloudcraft_access
}

module "eyecue_server" {
  source         = "../eyecue_server"
  aws_iam_user   = "eyecue-server"
  client_name    = var.client_name
  aws_account_id = var.aws_account_id
  aws_region     = var.aws_region
  country        = var.country
}


module "eyecue_roi_configurator" {
  source          = "../eyecue_roi_configurator"
  aws_iam_user    = "eyecue-roi-configuration-tool"
  allowed_methods = ["GET", "POST", "DELETE"]
  client_name     = var.client_name
  client_acronym  = var.client_acronym
  aws_account_id  = var.aws_account_id
  aws_region      = var.aws_region
  country         = var.country
}

module "eyecue_images" {
  source          = "../eyecue_images"
  aws_iam_user    = "eyecue-image-sync"
  allowed_methods = ["GET"]
  client_name     = var.client_name
  aws_account_id  = var.aws_account_id
  aws_region      = var.aws_region
  country         = var.country
  country_full    = var.country_full
  kms_arn         = module.camera_displacement.kms_arn
  tags            = var.tags
}

locals {
  image_sync_user = {
    name = module.eyecue_images.user.name
    arn  = module.eyecue_images.user.arn
  }
}

module "eyecue_validation_s3_access" {
  depends_on     = [module.eyecue_images]
  source         = "../eyecue_validation_s3_access"
  aws_iam_user   = local.image_sync_user
  client_name    = var.client_name
  aws_account_id = var.aws_account_id
  aws_region     = var.aws_region
  country        = var.country
  bucket_arn     = module.eyecue_images.bucket_arn
  bucket_name    = module.eyecue_images.bucket_name
  bucket_prefix  = "validation-tool-images"
  prefix_expiry  = 30
  service_name   = "ValidationToolImages"
}

module "eyecue_iot" {
  source         = "../eyecue_iot"
  aws_iam_user   = "eyecue-iot-creator"
  client_name    = var.client_name
  aws_account_id = var.aws_account_id
  aws_region     = var.aws_region
  country        = var.country
}

module "eyecue_mosaic" {
  source         = "../eyecue_sqs_provider"
  aws_iam_user   = "eyecue-mosaic"
  client_name    = var.client_name
  aws_account_id = var.aws_account_id
  aws_region     = var.aws_region
  country        = var.country
  service_name   = "mosaic"
}

module "eyecue_roi_suggestor" {
  source         = "../eyecue_sqs_provider"
  aws_iam_user   = "roisuggestor"
  client_name    = var.client_name
  aws_account_id = var.aws_account_id
  aws_region     = var.aws_region
  country        = var.country
  service_name   = "roisuggestor"
}

module "eyecue_weights" {
  source         = "../eyecue_weights"
  aws_iam_user   = "eyecue-weights"
  client_name    = var.client_name
  aws_account_id = var.aws_account_id
  aws_region     = var.aws_region
  country        = var.country
  bucket_name    = var.weights_bucket_name
}

module "eyecue_dashboard_iot" {
  source                 = "../eyecue_dashboard_iot"
  aws_iam_user           = "eyecue-dashboard-iot"
  aws_iam_user_read_only = "eyecue-dashboard-iot-read-only"
  aws_account_id         = var.aws_account_id
  aws_region             = var.aws_region
}

module "camera_displacement" {
  source = "../eyecue_camera_displacement"

  customer                 = "${var.client_name}-${var.country_full}"
  region                   = var.aws_region
  account_id               = var.aws_account_id
  images_bucket_arn        = module.eyecue_images.bucket_arn
  camera_images_bucket_arn = module.eyecue_roi_configurator.bucket_arn
  slack_webhook_url        = "https://hooks.slack.com/triggers/T0CMMNY4C/*************/878d614524952ace6c75f0cf9248499e"
  image_sync_user          = "eyecue-image-sync"

  tags = {
    Customer    = "${var.client_name}-${var.country_full}"
    Squad       = "Vision"
    Environment = var.client_name != "cv-qa" ? "production" : "qa"
    Product     = "Eyecue"
    Terraform   = "True"
  }
}

module "eyecue_camera_metrics_exporter" {
  source         = "../eyecue_camera_metrics_exporter"
  aws_region     = var.aws_region
  aws_account_id = var.aws_account_id
  keybase        = var.keybase
  tags           = var.tags
}

module "vpc_flow_logs" {
  source          = "../vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
}
