output "eyecue_dashboard_iot_user" {
  value     = module.eyecue_dashboard_iot.user
  sensitive = true
}

output "eyecue_images_user" {
  value     = module.eyecue_images.user
  sensitive = true
}

output "eyecue_iot_user" {
  value     = module.eyecue_iot.user
  sensitive = true
}

output "eyecue_mosaic_user" {
  value     = module.eyecue_mosaic.user
  sensitive = true
}

output "eyecue_roi_suggestor_user" {
  value     = module.eyecue_roi_suggestor.user
  sensitive = true
}

output "eyecue_roi_configurator_user" {
  value     = module.eyecue_roi_configurator.user
  sensitive = true
}

output "eyecue_server_user" {
  value     = module.eyecue_server.user
  sensitive = true
}

output "eyecue_weights_user" {
  value     = module.eyecue_weights.user
  sensitive = true
}
