import os
import json
import boto3
import base64
from datetime import datetime


s3_client = boto3.client('s3')

def lambda_handler(event, context):

    site_id = None
    event_type = None

    if 'topic' in event:
        topic = event['topic']
        site_id = topic.rsplit('/', 1)[-1]

        if 'vehicle-aggregated' in topic:
            event_type = 'aggregate'

        elif 'hci' in topic:
            event_type = 'hvi'

        elif 'roievent' in topic:
            event_type = 'roi'

        elif 'danger-zones' in topic:
            event_type = 'danger-zone'

        else:
            event_type = topic.split('/')[2]

    if 'base64OriginalPayload' in event:
        # decode
        decoded_payload = base64.b64decode(event['base64OriginalPayload'])
        decoded_payload_str = decoded_payload.decode('utf-8')
        decoded_payload_dict = json.loads(decoded_payload_str)

        # manipulate
        decoded_payload_dict['store_id'] = site_id
        decoded_payload_dict['event_type'] = event_type
        client_name = os.getenv('CLIENT_NAME', None)
        decoded_payload_dict['client_name'] = client_name
        print(f"PAYLOAD: {json.dumps(decoded_payload_dict)}")

        save_to_s3(
            decoded_payload_dict,
            event_type,
            site_id
        )

    return {
        'statusCode': 200,
        'body': json.dumps('Event received and processed successfully')
    }

def save_to_s3(payload, event_type, site_id):
    """
    Saves the payload as a JSON file in an S3 bucket

    Example of filename:
        fm-mcd-aus-0000/aggregate/2024-08-30T02:26:16.861131-payload.json
    """

    bucket_name = os.getenv('BUCKET_NAME', None)
    file_name = f"{site_id}/{event_type}/{datetime.utcnow().isoformat()}-payload.json"

    try:
        payload_json = json.dumps(payload)
        s3_client.put_object(
            Bucket=bucket_name,
            Key=file_name,
            Body=payload_json
        )
        print(f"Successfully saved payload to S3: {file_name}")
    except Exception as e:
        print(f"Failed to save payload to S3: {e}")
