
variable "client_acronym" {
  type    = string
  default = "cvland-dev"
}

variable "client_name" {
  description = "Client name to be appended to iot messages."
  type        = string
  default     = "cvland-dev-aus"
}

variable "kinesis_iot_topic_rules_config" {
  description = "Configuration for each IoT topic rule"
  type = map(object({
    name : string
    sql : string
    stream_name : string
    description : string
    enabled : bool
  }))
  default = {}
}

variable "is_data_sharing_enabled" {
  type    = bool
  default = false
}

variable "fallback_bucket_name" {
  description = "Name of the S3 bucket for fallback storage."
  type        = string
  default     = "kinesis-fallback-events"
}

variable "enable_fallback_to_s3" {
  description = "Enable fallback to S3 by invoking Lambda function on IoT rule error."
  type        = bool
  default     = false
}

variable "tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "prod"
    Squad       = "Platform"
  }
}

variable "iot_kinesis_eventstream_iam_role_name" {
  description = "Name of the role for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-eventstream-role"

}

variable "iot_kinesis_eventstream_iam_policy_name" {
  description = "Name of the policy for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-eventstream-policy"

}

variable "iot_kinesis_cloudwatch_log_group_name" {
  description = "Name of the Cloudwatch log group for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue_iot_kinesis_eventstream"

}

variable "iot_kinesis_cloudwatch_log_group_iam_policy_name" {
  description = "Name of the IAM policy for Cloudwatch log group for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-eventstream-cloudwatch-logs-policy"
}

variable "iot_kinesis_eventstream_iam_policy_attachment_name" {
  description = "Name of the IAM policy attachment for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-eventstream-cloudwatch-logs-attachment"
}

variable "iot_kinesis_fallback_lambda_iam_role_name" {
  description = "Name of the IAM role for Lambda function for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-fallback-lambda-role"

}

variable "iot_kinesis_fallback_lambda_iam_policy_name" {
  description = "Name of the IAM policy for Lambda function for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-fallback-lambda-policy"

}

variable "iot_kinesis_fallback_lambda_function_name" {
  description = "Name of the Lambda function for IoT Kinesis Eventstream"
  type        = string
  default     = "eyecue-iot-kinesis-fallback-lambda"

}
