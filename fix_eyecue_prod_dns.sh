#!/bin/bash

# Script to fix orphaned Cloudflare DNS records in all eyecue production environments
# This removes the deprecated Icinga DNS records that were disabled

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Fixing Orphaned Icinga Cloudflare DNS Records in Eyecue Production Environments ===${NC}"
echo ""

# Get all eyecue prod directories (excluding template)
PROD_DIRS=$(find fingermark/eyecue/prod -maxdepth 1 -type d | sort)

for dir in $PROD_DIRS; do
    env_name=$(basename "$dir")
    echo -e "${YELLOW}Processing environment: $env_name${NC}"
    
    cd "$dir"
    
    # Check if terraform is initialized, if not try to initialize
    if ! terraform state list >/dev/null 2>&1; then
        echo -e "${YELLOW}  🔧 Terraform not initialized, attempting to initialize...${NC}"
        if terraform init >/dev/null 2>&1; then
            echo -e "${GREEN}  ✅ Terraform initialized successfully${NC}"
        else
            echo -e "${RED}  ❌ Failed to initialize terraform${NC}"
            cd - >/dev/null
            continue
        fi
    fi
    
    # Check for orphaned Icinga cloudflare DNS records
    orphaned_records=$(terraform state list | grep "module.icinga2_satellite.module.dns_record.cloudflare_record.this" || true)
    
    if [ -z "$orphaned_records" ]; then
        echo -e "${GREEN}  ✅ No orphaned Icinga DNS records found${NC}"
    else
        echo -e "${YELLOW}  🔧 Found orphaned Icinga DNS records:${NC}"
        echo "$orphaned_records" | sed 's/^/    /'

        # Remove each orphaned record
        echo "$orphaned_records" | while read -r record; do
            if [ -n "$record" ]; then
                echo -e "${YELLOW}    Removing: $record${NC}"
                terraform state rm "$record"
                echo -e "${GREEN}    ✅ Removed successfully${NC}"
            fi
        done

        echo -e "${GREEN}  ✅ All orphaned Icinga DNS records removed from $env_name${NC}"
    fi
    
    cd - >/dev/null
    echo ""
done

echo -e "${GREEN}=== All eyecue production environments processed ===${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Test terraform apply on any environments that had orphaned Icinga DNS records removed"
echo "2. The Icinga DNS records were removed as part of infrastructure cleanup"
echo "3. No manual DNS cleanup is needed as these were monitoring endpoint records"
