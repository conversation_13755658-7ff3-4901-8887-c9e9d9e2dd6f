#!/bin/bash

# Script to run terraform apply -target=module.eyecue_rds on all eyecue production environments
# Excludes cvland and template directories

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Running Terraform Apply on Eyecue Production RDS Modules ===${NC}"
echo ""

# Get all eyecue prod directories (excluding template and cvland)
PROD_DIRS=$(find fingermark/eyecue/prod -maxdepth 1 -type d -name "*-*" | grep -v cvland | sort)

# Track results
SUCCESS_COUNT=0
FAILED_COUNT=0
FAILED_ENVS=()

for dir in $PROD_DIRS; do
    env_name=$(basename "$dir")
    echo -e "${BLUE}=== Processing environment: $env_name ===${NC}"
    
    cd "$dir"
    
    # Check if terraform is initialized
    if ! terraform state list >/dev/null 2>&1; then
        echo -e "${YELLOW}  🔧 Terraform not initialized, attempting to initialize...${NC}"
        if terraform init >/dev/null 2>&1; then
            echo -e "${GREEN}  ✅ Terraform initialized successfully${NC}"
        else
            echo -e "${RED}  ❌ Failed to initialize terraform${NC}"
            FAILED_COUNT=$((FAILED_COUNT + 1))
            FAILED_ENVS+=("$env_name (init failed)")
            cd - >/dev/null
            continue
        fi
    fi
    
    # Check if eyecue_rds module exists in state
    if ! terraform state list | grep -q "module.eyecue_rds"; then
        echo -e "${YELLOW}  ⚠️  No eyecue_rds module found in state, skipping${NC}"
        cd - >/dev/null
        continue
    fi
    
    echo -e "${YELLOW}  🚀 Running terraform apply -target=module.eyecue_rds${NC}"
    echo ""
    
    # Run terraform apply with target
    if terraform apply -target=module.eyecue_rds -auto-approve; then
        echo ""
        echo -e "${GREEN}  ✅ Successfully applied changes to $env_name${NC}"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo ""
        echo -e "${RED}  ❌ Failed to apply changes to $env_name${NC}"
        FAILED_COUNT=$((FAILED_COUNT + 1))
        FAILED_ENVS+=("$env_name")
    fi
    
    cd - >/dev/null
    echo ""
    echo -e "${BLUE}================================================${NC}"
    echo ""
done

# Summary
echo -e "${BLUE}=== SUMMARY ===${NC}"
echo -e "${GREEN}✅ Successful applies: $SUCCESS_COUNT${NC}"
echo -e "${RED}❌ Failed applies: $FAILED_COUNT${NC}"

if [ $FAILED_COUNT -gt 0 ]; then
    echo ""
    echo -e "${RED}Failed environments:${NC}"
    for env in "${FAILED_ENVS[@]}"; do
        echo -e "${RED}  - $env${NC}"
    done
fi

echo ""
if [ $FAILED_COUNT -eq 0 ]; then
    echo -e "${GREEN}🎉 All eyecue production RDS modules applied successfully!${NC}"
else
    echo -e "${YELLOW}⚠️  Some environments failed. Please check the output above for details.${NC}"
fi
