###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

data "vault_generic_secret" "samsung_dmbo_magicinfo_server_pwd" {
  path = "secret/terraform/samsung-dmbo/magicinfo/ec2_server_admin_password"
}

data "vault_generic_secret" "cloudflare" {
  path = "secret/common/cloudflare"
}

data "aws_ami" "magicinfo_ami" {
  most_recent = true

  filter {
    name   = "tag:Name"
    values = ["fm-dmbo-magicinfo"]
  }

  filter {
    name   = "platform"
    values = ["windows"]
  }

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_security_group" "havelock_north_access" {
  name = "HavelockNorthAccess"

  depends_on = [
    module.samsung_dmbo_vpc
  ]
}
