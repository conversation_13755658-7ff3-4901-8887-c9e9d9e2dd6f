module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "DeployerAccess", "StorageAccess", "FinanceAccess"]
}

module "vanta" {
  source = "../../../modules/vanta"
}

module "vpc_flow_logs" {
  source          = "../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "samsung_dmbo_vpc" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags, var.default_vpc_tags)
}

module "samsung_dmbo_magicinfo" {
  source                              = "../../../modules/samsung_dmbo_magicinfo_server"
  magicinfo_vpc_id                    = module.samsung_dmbo_vpc.vpc_id
  magicinfo_rdp_access_cidr_blocks    = var.magicinfo_rdp_access_cidr_blocks
  magicinfo_public_subnet_id          = module.samsung_dmbo_vpc.public_subnet_ids[0]
  magicinfo_server_name               = var.magicinfo_server_name
  magicinfo_server_ami_id             = data.aws_ami.magicinfo_ami.id
  magicinfo_server_instance_type      = var.magicinfo_server_instance_type
  magicinfo_nic_ip                    = var.magicinfo_nic_ip
  magicinfo_server_securitygroup_name = var.magicinfo_server_securitygroup_name
  magicinfo_server_admin_password     = data.vault_generic_secret.samsung_dmbo_magicinfo_server_pwd.data["secret"]
  magicinfo_server_additional_nic_security_groups = [
    data.aws_security_group.havelock_north_access.id
  ]
  magicinfo_server_alb_name       = var.magicinfo_server_alb_name
  magicinfo_server_s3_bucket_name = var.magicinfo_server_s3_bucket_name
  magicinfo_alb_subnet_ids        = module.samsung_dmbo_vpc.public_subnet_ids
  cloudflare_record_name          = var.cloudflare_record_name
  acm_domain_name                 = var.acm_domain_name
  tags                            = var.tags
  default_tags                    = var.default_tags
}

# ==========================================
# CloudWatch Alarms
# ==========================================

module "ec2_instance_cw_alarms_ap_southeast_2" {
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "fm-dmbo-magicinfo" = { instance_tags = { Name = "fm-dmbo-magicinfo" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# ------- ALB Monitoring -------

module "alb_monitoring_ap_southeast_2" {
  source               = "../../../modules/alb_monitoring"
  lambda_function_name = "alb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ALBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
}

# ------ CloudWatch Log Retention Management ------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "CloudWatchLogRetention"
    Environment = "Production"
    Squad       = "platform"
  })
}
