terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 3.0"
    }
  }
}

provider "aws" {
  region = var.AWS_REGION
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "tim-can"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}

provider "cloudflare" {
  email   = "<EMAIL>"
  api_key = data.vault_generic_secret.cloudflare.data["api_key"]
}

