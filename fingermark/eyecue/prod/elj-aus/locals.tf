locals {

  grafana_web_env_vars = [
    {
      "name" : "GF_INSTALL_PLUGINS",
      "value" : "${data.vault_generic_secret.infra.data["GF_INSTALL_PLUGINS"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_USER",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_USER"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SECURITY_ADMIN_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_SECURITY_ADMIN_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_DATABASE_TYPE",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_TYPE"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_HOST",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_HOST"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_NAME",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_NAME"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SECURITY_DISABLE_BRUTE_FORCE_LOGIN_PROTECTION",
      "value" : "${data.vault_generic_secret.infra.data["GF_SECURITY_DISABLE_BRUTE_FORCE_LOGIN_PROTECTION"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_LIVE_ALLOWED_ORIGINS",
      "value" : "${data.vault_generic_secret.infra.data["GF_LIVE_ALLOWED_ORIGINS"]}",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_ENABLED",
      "value" : "${data.vault_generic_secret.infra.data["GF_SMTP_ENABLED"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DEFAULT_INSTANCE_NAME",
      "value" : "${data.vault_generic_secret.infra.data["GF_DEFAULT_INSTANCE_NAME"]}",
      "type" : "String"
    },
    {
      "name" : "GF_SECURITY_ALLOW_EMBEDDING",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_PANELS_DISABLE_SANITIZE_HTML",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_ENABLED",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_HEADER_NAME",
      "value" : "X-JWT-Assertion",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_JWK_SET_URL",
      "value" : "https://dev-fingermark.au.auth0.com/.well-known/jwks.json",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_EMAIL_ATTRIBUTE_PATH",
      "value" : "https://www.fingermark.tech/meta.email",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_EMAIL_CLAIM",
      "value" : "https://www.fingermark.tech/meta.email",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_USERNAME_CLAIM",
      "value" : "https://www.fingermark.tech/meta.email",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_JWT_AUTO_SIGN_UP",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_SERVICE_ACCOUNT_ENABLED",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_PROXY_ENABLED",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_PROXY_HEADER_NAME",
      "value" : "X-WEBAUTH-USER",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_PROXY_AUTO_SIGN_UP",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_AUTH_PROXY_WHITELIST",
      "value" : "127.0.0.1,::1",
      "type" : "String"
    },
    {
      "name" : "GF_SECURITY_COOKIE_SAMESITE",
      "value" : "none",
      "type" : "String"
    },
    {
      "name" : "GF_SECURITY_COOKIE_SECURE",
      "value" : "true",
      "type" : "String"
    },
  ]
}
