#!/usr/bin/env bash
set -Eeuo pipefail

# ---------------------------------------------------------------------
#  Variables
# ---------------------------------------------------------------------
PROJECT_DIR="$(pwd -P)"                # cookiecutter puts us here
PROVIDER_FILE="${PROJECT_DIR}/provider.tf"

# ---------------------------------------------------------------------
#  Template-file renaming
# ---------------------------------------------------------------------
# Rename every file whose name ends with .tmpl (e.g. main.tf.tmpl ⇒ main.tf,
# terraform.tfvars.tmpl ⇒ terraform.tfvars).  We restrict the search to
# Terraform-related extensions to avoid touching other kinds of templates.
find "${PROJECT_DIR}" -type f \
  \( -name '*.tf.tmpl' -o -name '*.tfvars.tmpl' -o -name '*.hcl.tmpl' \) \
  -print0 |
  while IFS= read -r -d '' tmpl; do
    mv -- "${tmpl}" "${tmpl%.tmpl}"
  done

# ---------------------------------------------------------------------
#  Styling helpers
# ---------------------------------------------------------------------
BOLD=$(tput bold 2>/dev/null || true)
RED=$(tput setaf 1 2>/dev/null || true)     # red foreground
RESET=$(tput sgr0 2>/dev/null || true)

# ---------------------------------------------------------------------
#  Output
# ---------------------------------------------------------------------
cat <<EOF

🚀  ${BOLD}Next steps${RESET}
────────────────────────────────────────────────────────
1. Add ${BOLD}Terraform User’s Access & Secret Keys${RESET} to all providers in:
   ${PROVIDER_FILE}
   ${RED}${BOLD}⚠️  Do NOT commit these credentials to Git or any VCS!${RESET}

2. Run \`terraform init\`

3. Run \`terraform apply\` to create assume-role users

4. Remove the keys from ${PROVIDER_FILE} and uncomment the \`assume_role\` block

EOF
