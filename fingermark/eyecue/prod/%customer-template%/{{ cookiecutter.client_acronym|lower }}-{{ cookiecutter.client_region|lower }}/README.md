# {{ cookiecutter.client_acronym|upper }}-{{ cookiecutter.client_region|upper }}

Customer IaC Workspace generated by <PERSON><PERSON><PERSON><PERSON>

| Item           | Value                               |
|----------------|-------------------------------------|
| Client acronym | `{{ cookiecutter.client_acronym }}` |
| Client region  | `{{ cookiecutter.client_region }}`  |
| AWS region     | `{{ cookiecutter.aws_region }}`     |
| AWS account id | `{{ cookiecutter.aws_account_id }}` |

## Setup

### 1. Deploy Environment

1. Go to the [providers.tf](./provider.tf) file and enter the Terraform Access Keys created on the new AWS account for each provider present.

2. Once in the new environment directory you can create the resources:
    ```shell
    terraform init
    terraform apply
    ```

### 2. Setup Assume Role

1. Remove the Access Keys and uncomment the `assume_role` block below as per instructions in comments

