provider "aws" {
  region = var.AWS_REGION

  # ====
  # Temporarily provide access key and secret key for first time setup.
  # This is required to create the roles that we will later use to assume
  # permissions in this account.
  # This is the Terraform user created during the AWS account setup.

  # Step 1: Fill keys below
  
  # ==== DO NOT COMMIT THESE KEYS
  access_key = ""
  secret_key = ""
  # ====

  # Step 2: Terraform Apply

  # Step 3: After the roles are created, remove the access_key and secret_key above and uncomment the assume_role block below.

  # ==== UNCOMMENT ME WHEN YOU HAVE SET UP THE ROLES AND DELETE KEYS ABOVE
  # assume_role {
  #   role_arn     = "arn:aws:iam::{{ cookiecutter.aws_account_id }}:role/AdminAccess"
  #   session_name = "{{ cookiecutter.client_acronym|lower }}-{{ cookiecutter.client_region|lower }}"
  # }
}

{%- if cookiecutter.aws_region != "ap-southeast-2" -%}
# ================================================
# Additional AWS Provider for ap-southeast-2 as this is not the default region.
# Required as we deploy some resources in the ap-southeast-2 region
# ================================================
provider "aws" {
  region = "ap-southeast-2"
  alias  = "ap-southeast-2"
  # ====
  # Temporarily provide access key and secret key for first time setup.
  # This is required to create the roles that we will later use to assume
  # permissions in this account.
  # This is the Terraform user created during the AWS account setup.

  # Step 1: Fill keys below
  
  # ==== DO NOT COMMIT THESE KEYS
  access_key = ""
  secret_key = ""
  # ====

  # Step 2: Terraform Apply

  # Step 3: After the roles are created, remove the access_key and secret_key above and uncomment the assume_role block below.

  # ==== UNCOMMENT ME WHEN YOU HAVE SET UP THE ROLES AND DELETE KEYS ABOVE
  # assume_role {
  #   role_arn     = "arn:aws:iam::{{ cookiecutter.aws_account_id }}:role/AdminAccess"
  #   session_name = "{{ cookiecutter.client_acronym|lower }}-{{ cookiecutter.client_region|lower }}"
  # }
}
{%- endif %}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}

provider "bitbucket" {
  username = data.vault_generic_secret.bitbucket.data["username"]
  password = data.vault_generic_secret.bitbucket.data["password"]
}
