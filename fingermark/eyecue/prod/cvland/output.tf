output "auth0_migration_user_passwd" {
  value = {
    "name" : module.auth0_migration_user.this_iam_user_name,
    "arn" : module.auth0_migration_user.this_iam_user_arn,
    "access_key" : module.auth0_migration_user.this_iam_access_key_id,
    "encrypted_secret_key" : module.auth0_migration_user.this_iam_access_key_encrypted_secret,
    "pgp_key" : module.auth0_migration_user.pgp_key,
    "keybase_command" : module.auth0_migration_user.keybase_secret_key_decrypt_command
  }
  sensitive = true
}

output "helm_deployer_write_user" {
  value     = module.helm_repo_iam.write_user
  sensitive = true
}

output "helm_deployer_read_user" {
  value     = module.helm_repo_iam.read_user
  sensitive = true
}

