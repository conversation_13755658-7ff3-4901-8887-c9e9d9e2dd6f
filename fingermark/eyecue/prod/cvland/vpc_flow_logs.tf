module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws
  }
}

module "vpc_flow_logs_us_e1" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws.us-east-1
  }
}

module "vpc_flow_logs_us_e2" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws.us-east-2
  }
}

module "vpc_flow_logs_us_we1" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws.us-west-1
  }
}

module "vpc_flow_logs_us_we2" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws.us-west-2
  }
}

module "vpc_flow_logs_me_c1" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws.me-central-1
  }
}

module "vpc_flow_logs_me_s1" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
  providers = {
    aws = aws.me-south-1
  }
}
