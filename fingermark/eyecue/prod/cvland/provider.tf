provider "aws" {
  region = "ap-southeast-2"
  # alias  = "ap-southeast-2"
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "cv-prod"
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::************:role/AdminAccess"
  }
}

provider "aws" {
  region = "us-east-2"
  alias  = "us-east-2"
  assume_role {
    role_arn = "arn:aws:iam::************:role/AdminAccess"
  }
}

provider "aws" {
  region = "us-west-1"
  alias  = "us-west-1"
  assume_role {
    role_arn = "arn:aws:iam::************:role/AdminAccess"
  }
}

provider "aws" {
  region = "us-west-2"
  alias  = "us-west-2"
  assume_role {
    role_arn = "arn:aws:iam::************:role/AdminAccess"
  }
}

provider "aws" {
  region = "me-central-1"
  alias  = "me-central-1"
  assume_role {
    role_arn = "arn:aws:iam::************:role/AdminAccess"
  }
}

provider "aws" {
  region = "me-south-1"
  alias  = "me-south-1"
  assume_role {
    role_arn = "arn:aws:iam::************:role/AdminAccess"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
