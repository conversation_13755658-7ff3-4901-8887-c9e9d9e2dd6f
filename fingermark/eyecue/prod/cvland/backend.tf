terraform {
  required_version = ">= 1.12"

  required_providers {
    local = {
      version = "~> 2.1"
    }
  }

  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "ap-southeast-2"
    key            = "cvland-prod/terraform.tfstate"
    dynamodb_table = "terraform-state"
    assume_role = {
      role_arn     = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
      session_name = "infra"
    }
  }
}
