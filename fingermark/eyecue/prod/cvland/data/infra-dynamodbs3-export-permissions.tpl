{"Version": "2012-10-17", "Statement": [{"Sid": "AllowDynamoDBExportAction", "Effect": "Allow", "Action": ["dynamodb:ExportTableToPointInTime", "dynamodb:DescribeExport"], "Resource": "${DYNAMODB_TABLE_ARN}"}, {"Sid": "DescribeDynamoDBExportAction", "Effect": "Allow", "Action": ["dynamodb:DescribeExport"], "Resource": "${DYNAMODB_TABLE_ARN}/export/*"}, {"Sid": "AllowWriteToDestinationBucket", "Effect": "Allow", "Action": ["s3:AbortMultipartUpload", "s3:GetObject", "s3:PutObject", "s3:PutObjectAcl", "s3:List*", "s3:ListBucket", "s3:GetObjectTagging", "s3:PutObjectTagging"], "Resource": ["${SOURCE_S3_BUCKET}/*", "${DESTINATION_S3_BUCKET}/*"]}, {"Sid": "ListingBuckets", "Effect": "Allow", "Action": ["s3:ListBucket"], "Resource": ["${SOURCE_S3_BUCKET}", "${DESTINATION_S3_BUCKET}"]}, {"Sid": "KMSPermissions", "Effect": "Allow", "Action": ["kms:Decrypt", "kms:Encrypt", "kms:DescribeKey", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "Resource": ["arn:aws:kms:ap-southeast-2:047783385012:key/71280cc1-6bda-4a36-836c-cf3fdc2c6dde"]}]}