###  Destination AWS Account  ###
variable "AWS_REGION" {
  default = "ap-southeast-2"
}
variable "ENVIRONMENT" {}

variable "CLIENT_NAME" {}

variable "CLIENT_ACRONYM" {}

variable "COUNTRY" {}

variable "COUNTRY_FULL" {
  type    = string
  default = "aus"
}

variable "KEYBASE" {
  default = "keybase:fingermark"
}

### NETWORK Module ###

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["ap-southeast-2b", "ap-southeast-2c", "ap-southeast-2a"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "True"
    Product     = "Eyecue"
    Environment = "Production"
  }
}

variable "customer" {
  default     = "fingermark"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "general"
  description = "Fingermark Product"
  type        = string
}

variable "sns_topics" {
  type = map(object({
    topic_name : string
    display_name : string
    tags : map(string)
    subscriptions : optional(object({
      protocol : string
      endpoint : string
    }), null)
  }))
  description = "List of SNS topics to create"
  default = {
    cloudwatch-alarm-alert-helper-topic : {
      topic_name   = "cloudwatch-alarm-alert-helper-topic"
      display_name = "Send Alert From SNS to eyecue-app-team-alerts Slack Channel"
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      subscriptions = {
        protocol = "email"
        endpoint = "<EMAIL>"
      }
    },
  }
}

variable "dynamodb_tables_config" {
  description = "Configuration for each DynamoDB table"
  type = map(object({
    hash_key : string
    range_key : optional(string)
    stream_enabled : bool
    stream_view_type : string
    tags : map(string)
    point_in_time_recovery : bool
    deletion_protection_enabled : bool
    ttl : optional(object({
      attribute_name : string
      enabled : bool
    }), null)
    gsi : optional(map(object({
      hash_key : string
      range_key : optional(string)
    })), {})
  }))
  default = {
    "eyecue-dashboard-dashboards" = {
      hash_key                    = "siteId"
      range_key                   = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl = {
        attribute_name = "expireAt"
        enabled        = true
      }
    },
    "eyecue-dashboard-templates" = {
      hash_key                    = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-dashboard-devices" = {
      hash_key                    = "siteId"
      range_key                   = "serialNumber"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-dashboard-stores" = {
      hash_key                    = "siteId"
      range_key                   = "clientId"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
      gsi : {
        "clientId-siteId-index" = {
          hash_key  = "clientId"
          range_key = "siteId"
        }
      }
    },
    "eyecue-report-service-report-definitions" = {
      hash_key                    = "siteId"
      range_key                   = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueReportService"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-report-service-report-schedule" = {
      hash_key                    = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueReportService"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-report-service-report-templates" = {
      hash_key                    = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueReportService"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    }
  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
