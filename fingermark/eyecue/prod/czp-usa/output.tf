output "eyecue_dashboard_iot_user" {
  value     = module.common.eyecue_dashboard_iot_user
  sensitive = true
}

output "eyecue_images_user" {
  value     = module.common.eyecue_images_user
  sensitive = true
}

output "eyecue_iot_user" {
  value     = module.common.eyecue_iot_user
  sensitive = true
}

output "eyecue_mosaic_user" {
  value     = module.common.eyecue_mosaic_user
  sensitive = true
}

output "eyecue_roi_suggestor_user" {
  value     = module.common.eyecue_roi_suggestor_user
  sensitive = true
}

output "eyecue_roi_configurator_user" {
  value     = module.common.eyecue_roi_configurator_user
  sensitive = true
}

output "eyecue_server_user" {
  value     = module.common.eyecue_server_user
  sensitive = true
}

output "eyecue_weights_user" {
  value     = module.common.eyecue_weights_user
  sensitive = true
}

output "master_rds_username" {
  description = "value"
  value       = module.eyecue_rds.master_db_instance_username
  sensitive   = true

}

output "master_rds_password" {
  description = "This is the database password for the root/admin user"
  value       = module.eyecue_rds.master_db_instance_password
  sensitive   = true
}

output "master_instance_address" {
  description = "The MASTER database endpoint url"
  value       = module.eyecue_rds.master_db_instance_address
}

output "replica_instance_address" {
  description = "The REPLICA database endpoint URL"
  value       = module.eyecue_rds.replica_db_instance_address
}

output "eyecue_edw_user" {
  sensitive = true
  value     = module.eyecue_customer_edw_integration.eyecue_edw_user
}
