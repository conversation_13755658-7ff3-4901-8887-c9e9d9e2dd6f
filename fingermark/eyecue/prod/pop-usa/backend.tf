terraform {
  required_version = ">= 1.12"
  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "ap-southeast-2"
    key            = "pop-usa/terraform.tfstate"
    dynamodb_table = "terraform-state"
    assume_role = {
      role_arn     = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
      session_name = "pop-usa"
    }
  }

  required_providers {
    bitbucket = {
      source = "zahiar/bitbucket"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 3.0"
    }
  }
}
