module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DbaAccess"]
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "secret_manager" {
  source = "../../../../modules/secret_manager"

  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"

  eyecue_dashboard_data_secret_name = "rds/ssm/eyecue-dashboard-data"

}


module "eyecue_mimir" {
  source                   = "../../../../modules/eyecue_mimir"
  mimir_aws_account_id     = "************"
  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "Policy to invoke request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}

module "eyecue_roi_suggestor" {
  source         = "../../../../modules/eyecue_sqs_provider"
  aws_iam_user   = "roisuggestor"
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
  service_name   = "roisuggestor"
}

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "kinesis_common_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_FULL_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  stream_name_list               = var.stream_name_list
  firehose_roi_bucket_arn        = "arn:aws:s3:::fm-prod-datalake-1-ap-southeast-2"
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = true
}

module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = "${var.CLIENT_ACRONYM}-${var.COUNTRY_FULL}"
  kinesis_iot_topic_rules_config = var.kinesis_iot_topic_rules_config
  fallback_bucket_name           = "fm-data-eyecue-kinesis-failure-ap-southeast-2"
  enable_fallback_to_s3          = true
}

locals {
  image_sync_user = {
    name = "eyecue-image-sync"
    arn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/eyecue-image-sync"
  }
}

module "eyecue_validation_s3_access" {
  source         = "../../../../modules/eyecue_validation_s3_access"
  aws_iam_user   = local.image_sync_user
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
  bucket_arn     = "arn:aws:s3:::eyecue-kfc-images"
  bucket_name    = "eyecue-kfc-images"
  bucket_prefix  = "validation-tool-images"
  prefix_expiry  = 30
  service_name   = "ValidationToolImages"
}

module "eyecue_mosaic" {
  source         = "../../../../modules/eyecue_sqs_provider"
  aws_iam_user   = "eyecue-mosaic"
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
  service_name   = "mosaic"
}

module "camera_displacement" {
  source = "../../../../modules/eyecue_camera_displacement"

  customer                 = "${var.CLIENT_NAME}-${var.COUNTRY_FULL}"
  region                   = var.AWS_REGION
  account_id               = data.aws_caller_identity.current.account_id
  images_bucket_arn        = "arn:aws:s3:::eyecue-kfc-images"
  camera_images_bucket_arn = "arn:aws:s3:::eyecue-kfc-aus-camera-images"
  slack_webhook_url        = "https://hooks.slack.com/triggers/T0CMMNY4C/*************/878d614524952ace6c75f0cf9248499e"
  image_sync_user          = local.image_sync_user.name

  tags = {
    Customer    = "${var.CLIENT_NAME}-${var.COUNTRY_FULL}"
    Squad       = "Vision"
    Environment = "production"
    Product     = "Eyecue"
    Terraform   = "True"
  }
}

resource "aws_iam_policy" "camera_displacement_policy" {
  name        = "CameraDisplacementIAMPolicy"
  description = "Additional permissions for the camera displacement module"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        "Action" : [
          "s3:PutObjectAcl",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObjectAcl",
          "s3:GetObject"
        ],
        "Effect" : "Allow",
        "Resource" : [
          "arn:aws:s3:::eyecue-kfc-tessera-footage/*",
          "arn:aws:s3:::eyecue-kfc-tessera-footage",
          "arn:aws:s3:::eyecue-kfc-images/*",
          "arn:aws:s3:::eyecue-kfc-images",
          "arn:aws:s3:::eyecue-kfc-aus-camera-images/*",
          "arn:aws:s3:::eyecue-kfc-aus-camera-images"
        ],
        "Sid" : "EyecueImageSyncIAMPolicy"
      },
      {
        "Action" : [
          "sqs:SendMessage",
          "sqs:GetQueueUrl",
          "sqs:GetQueueAttributes"
        ],
        "Effect" : "Allow",
        "Resource" : "arn:aws:sqs:${var.AWS_REGION}:${data.aws_caller_identity.current.account_id}:eyecue-camera-displacement-sqs-${var.CLIENT_ACRONYM}-${var.COUNTRY_FULL}",
        "Sid" : "EyecueCameraDisplacementSendMessagePolicy"
      },
      {
        "Action" : "kms:GenerateDataKey",
        "Effect" : "Allow",
        "Resource" : "${module.camera_displacement.kms_arn}",
        "Sid" : "EyecueCameraDisplacementKMS"
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "image_sync_user_cam_displacement_attachment" {
  user       = local.image_sync_user.name
  policy_arn = aws_iam_policy.camera_displacement_policy.arn
}

module "eyecue_camera_metrics_exporter" {
  source         = "../../../../modules/eyecue_camera_metrics_exporter"
  aws_region     = var.AWS_REGION
  aws_account_id = data.aws_caller_identity.current.account_id
  keybase        = var.KEYBASE
  tags           = var.default_tags
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "eyecue_dashboard_iot" {
  source                 = "../../../../modules/eyecue_dashboard_iot"
  aws_iam_user           = "eyecue-dashboard-iot"
  aws_iam_user_read_only = "eyecue-dashboard-iot-read-only"
  aws_account_id         = data.aws_caller_identity.current.account_id
  aws_region             = var.AWS_REGION
}

# ==========================================
# SOC2 Security
# ==========================================

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source        = "../../../../modules/sqs_monitoring"
  sns_topic_arn = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

# ------- S3 Public Access Block -------

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------ CloudWatch Log Retention Management ------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  rds_cw_alarms = {
    # `module.eyecue_rds` doesn't exist, hence hardcoding RDS DB instance values
    eyecue_rds = {
      master_db_instance_name       = "eyeq"
      master_db_instance_identifier = "eyecue"
    }
  }
}

module "rds_cw_alarms" {
  source                                 = "../../../../modules/rds_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_rds_cpu_util           = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_mem_free           = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_disk_queue_depth   = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_write_iops         = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_read_iops          = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_free_storage_space = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  tags                                   = var.tags
  default_tags                           = var.default_tags
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {}
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
