resource "aws_security_group" "wireguard_proxy" {
  name        = "wireguard-proxy"
  description = "Security group allowing access to UDP port 58017"

  ingress {
    from_port   = 58017
    to_port     = 58017
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

module "wireguard_proxy" {
  # When the instance is running, run the Ansible script to install
  # https://registry.terraform.io/modules/terraform-aws-modules/ec2-instance/aws/latest  
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "~> 2.21.0"

  name           = "wireguard-proxy"
  instance_count = 1

  ami                    = "ami-003d3d03cfe1b0468"
  instance_type          = "t3.small"
  key_name               = aws_key_pair.infra_team.key_name
  monitoring             = true
  vpc_security_group_ids = [module.eyecue_network.havelock_security_group_id, aws_security_group.wireguard_proxy.id]
  subnet_id              = module.eyecue_network.public_subnet_ids[1]
  ebs_optimized          = true
  tags                   = var.default_tags
}



resource "aws_eip" "wireguard_proxy" {
  instance = module.wireguard_proxy.id[0]
  domain   = "vpc"
}

module "wireguard_proxy_dns_record" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_record_name  = "wireguard-server.${var.CLIENT_NAME}.infra"
  cloudflare_record_value = module.wireguard_proxy.public_ip[0]
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
}
