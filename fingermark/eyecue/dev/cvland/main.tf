# ==========================================
# IAM Assume Role
# ==========================================

module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DeployerAccess", "DevAccess"]
}

# ==========================================
# VPC
# ==========================================

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "disabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
}

# =============================================
# INFRA Resources
# =============================================

# TODO: Move these resources to INFRA account

# ------- Kommisjon -------

module "eyecue_kommisjon" {
  source                         = "../../../../modules/eyecue_kommisjon"
  environment                    = var.ENVIRONMENT
  aws_region                     = var.AWS_REGION
  aws_account_id                 = data.aws_caller_identity.current.account_id
  api_id                         = "mjjv00m5tg" # Deployed via Kommisjon Serverless
  provisioning_events_table_name = "dev-platform-provisioning-events"
  s3_inventory_bucket_name       = "kommisjon-global-dev-node-inventory"
}

module "platform_provisioning_events" {
  source     = "../../../../modules/provisioning_events"
  table_name = "dev-platform-provisioning-events"
}

# ==========================================
# Computer Vision Development
# ==========================================

# ======== IAM ========

resource "aws_iam_user" "eyecuetraining" {
  name = "tmp-eyecue-training-pipeline-dev-bucket"
  tags = {
    Terraform   = "true"
    Environment = "dev"
    Reason      = "Temporary creds for eyecue-training-pipeline-dev bucket"
  }
}

resource "aws_iam_policy" "eyecuetraining" {
  name   = "tmp-eyecue-training-pipeline-dev-policy"
  policy = templatefile("data/eyecue-training-s3-user-perms", {})
}

resource "aws_iam_user_policy_attachment" "eyecuetraining" {
  user       = aws_iam_user.eyecuetraining.name
  policy_arn = aws_iam_policy.eyecuetraining.arn
}

# ======== S3 Buckets ========

module "eyecue_weights_triton" {
  source         = "../../../../modules/eyecue_weights_triton"
  aws_iam_user   = "eyecue-weights-triton"
  bucket_name    = "eyecue-weights-dev"
  aws_region     = var.AWS_REGION
  client_name    = var.CLIENT_NAME
  country        = var.COUNTRY
  aws_account_id = data.aws_caller_identity.current.account_id
}

# ==========================================
# EYECUE Dashboard
# ==========================================

# ======== IAM ========

module "ratatoskr_lambda_user" {
  source              = "../../../../modules/fingermark_iam_user"
  iam_user_name       = "eyecue-ratatoskr-lambdas"
  iam_user_policyname = "eyecue-ratatoskr-lambdas-policy"
  iam_user_policy     = templatefile("data/eyecue-ratatoskr-lambdas-policy", { ACCOUNT = data.aws_caller_identity.current.account_id })
  create_user_policy  = true
  tags = {
    Terraform   = "true"
    Environment = "dev"
    Stack       = "CV-DEV"
  }
}

resource "aws_iam_role" "Live-Dashboard-Permissions" {
  name = "Live-Dashboard-Permissions"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "Live-Dashboard-Permissions" {
  name = "Live-Dashboard-Permissions-policy"
  role = aws_iam_role.Live-Dashboard-Permissions.id

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
          "lambda:InvokeFunction",
          "lambda:InvokeAsync"
      ],
      "Resource": [
          "arn:aws:lambda:ap-southeast-2:${data.aws_caller_identity.current.account_id}:function:dev-ratatoskr-configuration-dev-getStoreByChrome"
      ]
    }
  ]
}
EOF
}

# ======== IoT Kinesis ========

module "eyecue_iot_event_kinesis_module" {
  source                        = "../../../../modules/eyecue_iot_event_kinesis"
  client_acronym                = var.CLIENT_ACRONYM
  client_name_kinesis           = "${var.CLIENT_ACRONYM}-${var.COUNTRY_FULL}"
  kinesis_stream_name_roi       = var.kinesis_stream_name_roi
  kinesis_stream_name_hvi       = var.kinesis_stream_name_hvi
  kinesis_stream_name_aggregate = var.kinesis_stream_name_aggregate
  kinesis_stream_name_departure = var.kinesis_stream_name_departure
}

module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = var.CLIENT_ACRONYM
  kinesis_iot_topic_rules_config = var.kinesis_iot_topic_rules_config
}

# ======== Domains ========

# ------- API Gateway -------

module "heartbeat_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "dev.api.eyecue-heartbeat-service.eyecue.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "EDGE"
  tags               = var.tags
  providers = {
    aws           = aws
    aws.us_east_1 = aws.us-east-1 # Must use us-east-1 for Edge-optimized API Gateway
  }
}

module "eyecue_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "dev.api.eyecuedataboard.com"
  cloudflare_zone_id = "aa00a0cdc808160d406c732989eddbca"
  endpoint_type      = "REGIONAL"
  tags               = var.tags

  providers = { aws.us_east_1 = aws, aws = aws } # Hack around non optional provider
}

module "heimdallr_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "dev.eds.invoke-heimdallr.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "REGIONAL"
  tags               = var.tags

  providers = { aws.us_east_1 = aws, aws = aws } # Hack around non optional provider
}

# ------- Cloudflare DNS -------

module "cloudflare_dns_record_dev-api-eyecuedataboard-com" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "aa00a0cdc808160d406c732989eddbca"
  cloudflare_record_name  = "dev.api"
  cloudflare_record_value = "d-8m6z0zye2h.execute-api.ap-southeast-2.amazonaws.com"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

module "cloudflare_dns_record_dev-api-cjs-eyecue-fingermarkai-tech" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "e8428c969a5cc8497a9f8684baba52de"
  cloudflare_record_name  = "dev.api.cjs.eyecue"
  cloudflare_record_value = "d-j2tgzaewi1.execute-api.ap-southeast-2.amazonaws.com"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

# Cloudflare DNS for Heartbeat API custom domain (EDGE -> CloudFront)
module "cloudflare_dns_record_dev_heartbeat_api" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "e8428c969a5cc8497a9f8684baba52de"
  cloudflare_record_name  = "dev.api.eyecue-heartbeat-service.eyecue.fingermarkai.tech"
  cloudflare_record_value = module.heartbeat_api_gateway_domain_cloudflare.cloudfront_domain_name
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}


module "cloudflare_dns_record_dev_dashboard" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "c389162536bc3333bbe4d22e12151e8b"
  cloudflare_record_name  = "dev.dashboard.eyecue"
  cloudflare_record_value = module.dashboard_s3_cloudfront.cloudfront_domainname
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

module "cloudflare_dns_record_dev_admin" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "c389162536bc3333bbe4d22e12151e8b"
  cloudflare_record_name  = "dev.admin.eyecue"
  cloudflare_record_value = module.admin_s3_cloudfront.cloudfront_domainname
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

module "cloudflare_dns_record_dev_monitor" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "c389162536bc3333bbe4d22e12151e8b"
  cloudflare_record_name  = "dev.monitorv2.eyecue"
  cloudflare_record_value = module.monitor_s3_cloudfront.cloudfront_domainname
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

# ------- ACM Certificates -------

resource "aws_acm_certificate" "portal-fingermarkglobal" {
  provider          = aws.us-east-1
  domain_name       = "dev.portal.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "dashboard-fingermarkglobal" {
  provider          = aws.us-east-1
  domain_name       = "dev.dashboard.eyecue.fingermarkglobal.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "admin-fingermarkglobal" {
  provider          = aws.us-east-1
  domain_name       = "dev.admin.eyecue.fingermarkglobal.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "monitor-fingermarkglobal" {
  provider          = aws.us-east-1
  domain_name       = "dev.monitorv2.eyecue.fingermarkglobal.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

# ======== DynamoDB Tables ========

module "eyecue_dashboard_dynamodb_tables" {
  source                 = "../../../../modules/eyecue_dashboard_dynamodb"
  dynamodb_tables_config = var.dynamodb_tables_config
}

# ======== Events ========

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_event_handler" {
  source         = "../../../../modules/eyecue_event_handler"
  environment    = var.ENVIRONMENT
  aws_region     = var.AWS_REGION
  aws_account_id = data.aws_caller_identity.current.account_id
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

# ======== S3 ========

# -------- Portal Site --------

module "portal_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "dev-portal-fingermarkglobal"
  domain_name        = "dev.portal.eyecuedashboard.com"
  cloudfront_aliases = ["dev.portal.eyecuedashboard.com"]
  cloudfront_acm_arn = aws_acm_certificate.portal-fingermarkglobal.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

# -------- Admin Site --------

module "admin_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "dev-admin-fingermarkglobal"
  domain_name        = "dev.admin.eyecue.fingermarkglobal.com"
  cloudfront_aliases = ["dev.admin.eyecue.fingermarkglobal.com"]
  cloudfront_acm_arn = aws_acm_certificate.admin-fingermarkglobal.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "admin_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.admin_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["http://localhost:4321", "https://dev.portal.eyecuedashboard.com"]
}

# -------- Dashboard Site --------

module "dashboard_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "dev-dashboard-fingermarkglobal"
  domain_name        = "dev.dashboard.eyecue.fingermarkglobal.com"
  cloudfront_aliases = ["dev.dashboard.eyecue.fingermarkglobal.com"]
  cloudfront_acm_arn = aws_acm_certificate.dashboard-fingermarkglobal.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "dashboard_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.dashboard_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://dev.portal.eyecuedashboard.com"]
}

# -------- Monitor Site --------

module "monitor_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "dev-monitorv2-fingermarkglobal"
  domain_name        = "dev.monitorv2.eyecue.fingermarkglobal.com"
  cloudfront_aliases = ["dev.monitorv2.eyecue.fingermarkglobal.com"]
  cloudfront_acm_arn = aws_acm_certificate.monitor-fingermarkglobal.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "monitor_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.monitor_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://dev.portal.eyecuedashboard.com", "http://localhost:4321/*"]
}

# ==========================================
# SOC2 Security
# ==========================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source               = "../../../../modules/sqs_monitoring"
  lambda_function_name = "sqs-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "SQSMessageAgeMonitoring"
    Environment = "Development"
    Squad       = "platform"
  })
}

module "sqs_monitoring_us_east_1" {
  source               = "../../../../modules/sqs_monitoring"
  lambda_function_name = "sqs-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]
  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "SQSMessageAgeMonitoring"
    Environment = "Development"
    Squad       = "platform"
  })
  providers = { aws = aws.us-east-1 }
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Development"
    Squad       = "platform"
  })
}

module "dynamodb_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }

  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Development"
    Squad       = "platform"
  })
}

# ------- ALB Monitoring -------

module "alb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/alb_monitoring"
  lambda_function_name = "alb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ALBMonitoring"
    Environment = "Development"
    Squad       = "platform"
  })
}

module "alb_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }

  source               = "../../../../modules/alb_monitoring"
  lambda_function_name = "alb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ALBMonitoring"
    Environment = "Development"
    Squad       = "platform"
  })
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# -------- AWS Config Recorder -------

module "aws_config_recorder" {
  source = "../../../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags = var.tags
}

# ==========================================
# CloudWatch Alarms
# ==========================================

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 }
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "auto-provisioner"     = { instance_tags = { Name = "auto-provisioner" } }
    "DBClone"              = { instance_tags = { Name = "DBClone" } }
    "eap-test"             = { instance_tags = { Name = "eap-test" } }
    "eyecue-InfluxDB-dev"  = { instance_tags = { Name = "eyecue-InfluxDB-dev" } }
    "eyecue-roi-suggester" = { instance_tags = { Name = "eyecue-roi-suggester" } }
    "ps-redis-test"        = { instance_tags = { Name = "ps-redis-test" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

module "ec2_instance_cw_alarms_us_east_1" {
  providers      = { aws = aws.us-east-1 }
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "Signalling-Server" = { instance_tags = { Name = "Signalling-Server" } }
    "victoria-metrics"  = { instance_tags = { Name = "victoria-metrics" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Development"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

# Lambda global error rate monitoring for us-east-1 region
module "lambda_error_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Development"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
