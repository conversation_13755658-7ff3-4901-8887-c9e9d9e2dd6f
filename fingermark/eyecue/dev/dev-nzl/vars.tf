###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

variable "AWS_REGION" {
  default = "ap-southeast-2"
}

variable "ENVIRONMENT" {}

variable "CLIENT_NAME" {
  type    = string
  default = "dev"
}

variable "CLIENT_ACRONYM" {
  type    = string
  default = "dev"
}

variable "COUNTRY" {
  type    = string
  default = "nzl"
}

variable "COUNTRY_FULL" {
  type    = string
  default = "nzl"
}

variable "KEYBASE" {
  type    = string
  default = "keybase:fingermark"
}

variable "APP_STAGE" {}

variable "CLIENT_FULL_NAME" {
  type    = string
  default = "dev-nzl"
}

variable "sns_topics" {
  type = map(object({
    topic_name : string
    display_name : string
    tags : map(string)
    subscriptions : optional(object({
      protocol : string
      endpoint : string
    }), null)
  }))
  description = "List of SNS topics to create"
  default = {
    cloudwatch-alarm-alert-helper-topic : {
      topic_name   = "cloudwatch-alarm-alert-helper-topic"
      display_name = "Send Alert From SNS to eyecue-app-team-alerts Slack Channel"
      tags = {
        "Environment"     = "Production"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      subscriptions = {
        protocol = "email"
        endpoint = "<EMAIL>"
      }
    },
  }
}

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}

variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
}

variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Squad       = "Platform"
    Environment = "prod"
    Product     = "eyecue"
  }
}

variable "customer" {
  default     = "dev-nzl"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "eyecue"
  description = "Fingermark Product"
  type        = string
}

variable "kinesis_data_stream_retention_period" {
  type    = number
  default = 72
}

variable "kinesis_data_stream_stream_mode" {
  type    = string
  default = "ON_DEMAND"
}

variable "redshift_aws_account_ids_roles" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_role_us_east_1" },
    { account_id = "************", role_name = "redshift_stream_role_us_east_1" }
  ]
}

locals {
  #Kinesis Stream Names
  journey_stream   = "ds-${var.CLIENT_ACRONYM}-eyecue-journey"
  drivethru_stream = "ds-${var.CLIENT_ACRONYM}-eyecue-drivethru-events"
  indoor_stream    = "ds-${var.CLIENT_ACRONYM}-eyecue-indoor-events"
}

locals {
  #IOT to Kinesis Routing Rules
  kinesis_iot_topic_rules_config = {
    "roi_eyecue_iot_kinesis_eventstream" = {
      name        = "roi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'roi' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/roievent/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic HVI - use as Interaction in Data Environment
    "interaction_eyecue_iot_kinesis_eventstream" = {
      name        = "interaction_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Interaction(HVI) events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'interaction' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/hci/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic Aggregate - use as Journey in Data Environment
    "journey_eyecue_iot_kinesis_eventstream" = {
      name        = "journey_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Journey (Aggregated) events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'journey' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/vehicle-aggregated-2-0/#'"
      stream_name = local.journey_stream
      enabled     = true
    },
    "departure_eyecue_iot_kinesis_eventstream" = {
      name        = "departure_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'departure' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/departure/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "arrival_eyecue_iot_kinesis_eventstream" = {
      name        = "arrival_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Arrival events to Kinesis Data Stream"
      sql         = "SELECT *, arrival.site_id as store_id, 'arrival' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/arrival/#'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    #IOT Topic Danger Zone use as Queue Zone in Data Environment
    "queue_zone_eyecue_iot_kinesis_eventstream" = {
      name        = "queue_zone_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Queue Zone (Danger Zone) events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'queue-zone' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/danger-zones/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "lead_car_eyecue_iot_kinesis_eventstream" = {
      name        = "lead_car_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Lead Car Timer events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'lead-car' as event_type, '${var.CLIENT_ACRONYM}' as client_name FROM '/eyeq/roicount/+'"
      stream_name = local.drivethru_stream
      enabled     = true
    },
    "indoor_tracking_eyecue_iot_kinesis_eventstream" = {
      name        = "indoor_tracking_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Indoor events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, '${var.CLIENT_ACRONYM}' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/#'"
      stream_name = local.indoor_stream
      enabled     = false
    },
  }
}

variable "lambda_role_arn_suffixes" {
  description = "List of Lambda role ARN suffixes"
  type        = list(string)
  default = [
    "eyecue-mimir-blue-prod-ap-southeast-2-lambdaRole",
    "project-mimir-prod-prod-ap-southeast-2-lambdaRole",
    "eyecue-mimir-green-prod-ap-southeast-2-lambdaRole"
  ]
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
