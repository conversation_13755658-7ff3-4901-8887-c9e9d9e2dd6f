
provider "aws" {
  region      = "ap-southeast-2"
  max_retries = 1
  assume_role {
    role_arn     = "arn:aws:iam::246684370510:role/AdminAccess"
    session_name = "fingermark-terraform-poc"
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::246684370510:role/AdminAccess"
    session_name = "fingermark-terraform-poc"
  }
}

provider "aws" {
  region = "us-east-2"
  alias  = "us-east-2"
  assume_role {
    role_arn     = "arn:aws:iam::246684370510:role/AdminAccess"
    session_name = "fingermark-terraform-poc"
  }
}

provider "aws" {
  region = "us-west-1"
  alias  = "us-west-1"
  assume_role {
    role_arn     = "arn:aws:iam::246684370510:role/AdminAccess"
    session_name = "fingermark-terraform-poc"
  }
}

provider "aws" {
  region = "us-west-2"
  alias  = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::246684370510:role/AdminAccess"
    session_name = "fingermark-terraform-poc"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
