# Root
# ├── Security
# └── Fingermark
#     ├── Supersonic
#     │   └── PROD
#     ├── Support
#     ├── Northvue
#     │   ├── DEV
#     │   ├── STG
#     │   └── PROD
#     ├── Samsung-dmbo
#     │   └── PROD
#     ├── Data
#     │   ├── Prod
#     │   └── Dev
#     ├── EyeCue
#     │   ├── DEV
#     │   └── PROD
#     │       ├── Customer Accounts
#     │       └── Internal Accounts
#     ├── Platform Infrastructure
#     └── Production

# ==========================================
# Security OU
# ==========================================
resource "aws_organizations_organizational_unit" "security" {
  name      = "Security"
  parent_id = data.aws_organizations_organization.org.roots[0].id
}

# ==========================================
# Fingermark OU
# ==========================================
resource "aws_organizations_organizational_unit" "fingermark" {
  name      = "Fingermark"
  parent_id = data.aws_organizations_organization.org.roots[0].id
}

# ==========================================
# Supersonic
# ==========================================
resource "aws_organizations_organizational_unit" "supersonic" {
  name      = "Supersonic"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}
resource "aws_organizations_organizational_unit" "supersonic_prod" {
  name      = "PROD"
  parent_id = aws_organizations_organizational_unit.supersonic.id
}

# ==========================================
# Support
# ==========================================
resource "aws_organizations_organizational_unit" "support" {
  name      = "Support"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}

# ==========================================
# Northvue
# ==========================================
resource "aws_organizations_organizational_unit" "northvue" {
  name      = "Northvue"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}
resource "aws_organizations_organizational_unit" "northvue_dev" {
  name      = "DEV"
  parent_id = aws_organizations_organizational_unit.northvue.id
}
resource "aws_organizations_organizational_unit" "northvue_stg" {
  name      = "STG"
  parent_id = aws_organizations_organizational_unit.northvue.id
}
resource "aws_organizations_organizational_unit" "northvue_prod" {
  name      = "PROD"
  parent_id = aws_organizations_organizational_unit.northvue.id
}

# ==========================================
# Samsung DMBO
# ==========================================
resource "aws_organizations_organizational_unit" "samsung_dmbo" {
  name      = "Samsung-dmbo"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}
resource "aws_organizations_organizational_unit" "samsung_dmbo_prod" {
  name      = "PROD"
  parent_id = aws_organizations_organizational_unit.samsung_dmbo.id
}

# ==========================================
# Data
# ==========================================
resource "aws_organizations_organizational_unit" "data" {
  name      = "Data"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}
resource "aws_organizations_organizational_unit" "data_prod" {
  name      = "Prod"
  parent_id = aws_organizations_organizational_unit.data.id
}
resource "aws_organizations_organizational_unit" "data_dev" {
  name      = "Dev"
  parent_id = aws_organizations_organizational_unit.data.id
}

# ==========================================
# Eyecue
# ==========================================
resource "aws_organizations_organizational_unit" "eyecue" {
  name      = "EyeCue"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}
resource "aws_organizations_organizational_unit" "eyecue_dev" {
  name      = "DEV"
  parent_id = aws_organizations_organizational_unit.eyecue.id
}
resource "aws_organizations_organizational_unit" "eyecue_prod" {
  name      = "PROD"
  parent_id = aws_organizations_organizational_unit.eyecue.id
}
resource "aws_organizations_organizational_unit" "customer_accounts" {
  name      = "Customer Accounts"
  parent_id = aws_organizations_organizational_unit.eyecue_prod.id
}
resource "aws_organizations_organizational_unit" "internal_accounts" {
  name      = "Internal Accounts"
  parent_id = aws_organizations_organizational_unit.eyecue_prod.id
}

# ==========================================
# Platform
# ==========================================
resource "aws_organizations_organizational_unit" "platform" {
  name      = "Platform Infrastructure"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}

# ==========================================
# FM Production
# ==========================================
resource "aws_organizations_organizational_unit" "fm_prod" {
  name      = "Production"
  parent_id = aws_organizations_organizational_unit.fingermark.id
}
