data "aws_organizations_organization" "org" {}

data "aws_organizations_organizational_unit_child_accounts" "data_dev" {
  parent_id = var.org_paths["data_dev"]
}

data "aws_organizations_organizational_unit_child_accounts" "data_prod" {
  parent_id = var.org_paths["data_prod"]
}

data "aws_organizations_organizational_unit_child_accounts" "eyecue_dev" {
  parent_id = var.org_paths["eyecue_dev"]
}

# NOTE(CW): We use descendant accounts here as the eyecue prod OU has two child OUs
#             - Customer Accounts
#             - Internal Accounts
data "aws_organizations_organizational_unit_descendant_accounts" "eyecue_prod" {
  parent_id = var.org_paths["eyecue_prod"]
}

data "aws_organizations_organizational_unit_child_accounts" "northvue_dev" {
  parent_id = var.org_paths["northvue_dev"]
}

data "aws_organizations_organizational_unit_child_accounts" "northvue_stg" {
  parent_id = var.org_paths["northvue_stg"]
}

data "aws_organizations_organizational_unit_child_accounts" "northvue_prod" {
  parent_id = var.org_paths["northvue_prod"]
}

data "aws_organizations_organizational_unit_child_accounts" "platform" {
  parent_id = var.org_paths["platform"]
}

data "aws_organizations_organizational_unit_child_accounts" "fm_prod" {
  parent_id = var.org_paths["fm_prod"]
}

data "aws_organizations_organizational_unit_child_accounts" "dmbo" {
  parent_id = var.org_paths["dmbo"]
}

data "aws_organizations_organizational_unit_child_accounts" "supersonic" {
  parent_id = var.org_paths["supersonic"]
}

data "aws_organizations_organizational_unit_child_accounts" "support" {
  parent_id = var.org_paths["support"]
}
