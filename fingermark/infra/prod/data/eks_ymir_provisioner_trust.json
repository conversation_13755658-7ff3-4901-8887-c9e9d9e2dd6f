{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Federated": "arn:aws:iam::${ACCOUNT}:oidc-provider/oidc.eks.${REGION}.amazonaws.com/id/${OIDC}"}, "Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"oidc.eks.ap-southeast-2.amazonaws.com/id/A5A6746B53BEF6A947ED6E795861F5AD:aud": "sts.amazonaws.com", "oidc.eks.ap-southeast-2.amazonaws.com/id/A5A6746B53BEF6A947ED6E795861F5AD:sub": "system:serviceaccount:ymir-provisioner:ymir-provisioner"}}}]}