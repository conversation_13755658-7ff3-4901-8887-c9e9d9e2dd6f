
###KU<PERSON>_IAM ALB Ingress controller

module "kubeiam_albingress" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-alb-ingress-controller"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/eks_elb_permissions.json")
  kubeiam_trustpolicy = templatefile("data/eks_elb_trust.json", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD" })
}

module "kubeiam_ymirprovisioner" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "YmirProvisioner"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/eks_ymir_provisioner_permissions.json")
  kubeiam_trustpolicy = templatefile("data/eks_ymir_provisioner_trust.json", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD" })
}

module "kubeiam_vault" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-vault"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = templatefile("data/eks_vault_permissions.json", { KMS_VAULT_ARN = aws_kms_key.vault.arn })
  kubeiam_trustpolicy = templatefile("data/eks_vault_trust.json", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD" })
}

module "kubeiam_atlantis" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-atlantis"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/atlantis_permissions.json")
  kubeiam_trustpolicy = templatefile("data/atlantis_trust.json", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD" })
}

module "kubeiam_kommisjon" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-kommisjon"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/kommisjon_permissions.json")
  kubeiam_trustpolicy = templatefile("data/kommisjon_trust.json", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD" })
}

module "kubeiam_dynamodbs3_cvprod2dataprod" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-dynamos3export-cvprod2dataprod"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/dynamodbs3export_cvprod2dataprod_permissions.json")
  kubeiam_trustpolicy = templatefile("data/general_trust.tpl", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD", SERVICEACCOUNT = "system:serviceaccount:cronjobs:dynamos3export" })
}

module "kubeiam_ebscsidriver" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-ebscsidriver"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/ebscsidriver_permissions.json")
  kubeiam_trustpolicy = templatefile("data/general_trust.tpl", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD", SERVICEACCOUNT = "system:serviceaccount:kube-system:ebs-csi-controller-sa" })
}

module "kubeiam_downtime_scheduler" {
  source              = "../../../modules/kubeiam"
  kubeiam_role        = "kubeiam-downtime-scheduler"
  worker_node_arn     = module.eks.worker_node_role_arn
  kubeiam_policy      = file("data/downtime_scheduler_permissions.json")
  kubeiam_trustpolicy = templatefile("data/downtime_scheduler_trust.json", { ACCOUNT = data.aws_caller_identity.current.account_id, REGION = data.aws_region.current.name, OIDC = "A5A6746B53BEF6A947ED6E795861F5AD" })
}
