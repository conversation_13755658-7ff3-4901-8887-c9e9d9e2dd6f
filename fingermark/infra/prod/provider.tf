terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.0.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.17.0"
    }
  }
}

# =====
# Providers: AWS
# =====
# DEFAULT AWS PROVIDER: ap-southeast-2
provider "aws" {
  region = "ap-southeast-2"
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra"
  }
}

provider "aws" {
  region = "ca-central-1"
  alias  = "ca-central-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-ca-central-1"
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-us-east-1"
  }
}

provider "aws" {
  region = "us-west-1"
  alias  = "us-west-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-us-west-1"
  }
}

provider "aws" {
  region = "us-west-2"
  alias  = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-us-west-2"
  }
}

# =====
# Providers: Vault
# =====
provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}

# =====
# Cluster-specific EKS connections for Helm/Kubernetes providers
# =====

# us-east-1 Victoria cluster
data "aws_eks_cluster" "victoria_us_east_1" {
  provider = aws.us-east-1
  name     = "victoria-us-east-1"
}

data "aws_eks_cluster_auth" "victoria_us_east_1" {
  provider = aws.us-east-1
  name     = "victoria-us-east-1"
}

provider "kubernetes" {
  alias                  = "victoria_us_east_1"
  host                   = data.aws_eks_cluster.victoria_us_east_1.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.victoria_us_east_1.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.victoria_us_east_1.token
}

provider "helm" {
  alias = "victoria_us_east_1"
  kubernetes {
    host                   = data.aws_eks_cluster.victoria_us_east_1.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.victoria_us_east_1.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.victoria_us_east_1.token
  }
}

# ap-southeast-2 Victoria cluster
data "aws_eks_cluster" "victoria_ap_southeast_2" {
  provider = aws
  name     = "victoria-ap-southeast-2"
}

data "aws_eks_cluster_auth" "victoria_ap_southeast_2" {
  provider = aws
  name     = "victoria-ap-southeast-2"
}

provider "kubernetes" {
  alias                  = "victoria_ap_southeast_2"
  host                   = data.aws_eks_cluster.victoria_ap_southeast_2.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.victoria_ap_southeast_2.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.victoria_ap_southeast_2.token
}

provider "helm" {
  alias = "victoria_ap_southeast_2"
  kubernetes {
    host                   = data.aws_eks_cluster.victoria_ap_southeast_2.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.victoria_ap_southeast_2.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.victoria_ap_southeast_2.token
  }
}
