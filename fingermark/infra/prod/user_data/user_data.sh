#!/usr/bin/env bash
exec > >(tee /var/log/user-data.log | logger -t user-data -s 2>/dev/console) 2>&1

##
## Setup SSH Config
##
cat <<"__EOF__" > /home/<USER>/.ssh/config
Host *
    StrictHostKeyChecking no
__EOF__

cat<<"__EOF__" >> /home/<USER>/.ssh/authorized_keys
${authorized_keys_content}
__EOF__


## SSH Tunnel
cat<<"__EOF__" >> /etc/systemd/system/ssh-tunnel@.service
${systemd_service_content}
__EOF__

cat<<"__EOF__" >> /etc/default/ssh-tunnel-dev
${systemd_dev_envfile_content}
__EOF__

cat<<"__EOF__" >> /etc/default/ssh-tunnel-prod
${systemd_prod_envfile_content}
__EOF__

chmod 600 /home/<USER>/.ssh/config
chown ubuntu:ubuntu /home/<USER>/.ssh/config

##
## Enable SSM
##
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent

<NAME_EMAIL>
<NAME_EMAIL>
<NAME_EMAIL>

<NAME_EMAIL>
<NAME_EMAIL>
<NAME_EMAIL>