#!/usr/bin/env bash
exec > >(tee /var/log/user-data.log | logger -t user-data -s 2>/dev/console) 2>&1

##
## Setup SSH Config
##
cat <<"__EOF__" > /home/<USER>/.ssh/config
Host *
    StrictHostKeyChecking no
__EOF__

cat<<"__EOF__" >> /home/<USER>/.ssh/authorized_keys
${authorized_keys_content}
__EOF__


##
## Enable SSM
##
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent

<NAME_EMAIL>
<NAME_EMAIL>
<NAME_EMAIL>

<NAME_EMAIL>
<NAME_EMAIL>
<NAME_EMAIL>