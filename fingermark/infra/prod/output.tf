
output "grafana_db_master_password" {
  description = "The master password"
  value       = module.grafana_server.grafana_db_master_password
  sensitive   = true
}

output "aurora_cluster_endpoint" {
  description = "The DB endpoint"
  value       = module.grafana_server.grafana_aurora_cluster_endpoint
  sensitive   = true
}

output "icinga2_master_db_passwd" {
  value     = module.icinga2_master_db.db_instance_password
  sensitive = true
}

output "softether_monitoring_api_ec2_public_ip" {
  value = module.softether_monitoring_api.ec2_public_ip
}

output "config_map_aws_auth" {
  description = "A kubernetes configuration to authenticate to this EKS cluster."
  value       = module.eks.config_map_aws_auth
}

output "kubeconfig" {
  description = "kubectl config file contents for this EKS cluster."
  value       = module.eks.kubeconfig
}

output "eks_worker_node_role_arn" {
  description = "The ARN of the EKS worker node role."
  value       = module.eks.worker_node_role_arn
}

output "infra_workstation_service_directory_pass" {
  description = "AWS Directory Service Password"
  value       = random_string.infra_workstation.result
}

# --- Enhanced Whitelist VPN Gateway Outputs (US East 1) ---
output "vpn_vpc_id" {
  description = "VPC ID of the VPN gateway (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.vpc_id
}

output "vpn_vpc_cidr_block" {
  description = "CIDR block of the VPN VPC (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.vpc_cidr_block
}

output "vpn_public_subnet_ids" {
  description = "List of public subnet IDs in the VPN VPC (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.public_subnet_ids
}

output "vpn_private_subnet_ids" {
  description = "List of private subnet IDs in the VPN VPC (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.private_subnet_ids
}

output "vpn_load_balancer_dns_name" {
  description = "DNS name of the VPN load balancer (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.load_balancer_dns_name
}

output "vpn_endpoint" {
  description = "Recommended VPN endpoint for client configurations (stable DNS name)"
  value       = module.vpn_dns_record.hostname
}

output "vpn_dns_name" {
  description = "Custom DNS name for the VPN endpoint"
  value       = module.vpn_dns_record.hostname
}

output "vpn_load_balancer_zone_id" {
  description = "Zone ID of the VPN load balancer (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.load_balancer_zone_id
}

output "vpn_security_group_id" {
  description = "Security group ID for the VPN instances (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.security_group_id
}

output "vpn_key_s3_bucket" {
  description = "S3 bucket name for VPN keys (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.key_s3_bucket
}

output "vpn_client_config_s3_bucket" {
  description = "S3 bucket name for VPN client configurations (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.client_config_s3_bucket
}

output "vpn_autoscaling_group_name" {
  description = "Name of the VPN Auto Scaling Group (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.autoscaling_group_name
}

output "vpn_client_cidr_block" {
  description = "CIDR block used for VPN client IP addresses (US East 1)"
  value       = module.enhanced_whitelist_vpn_gateway_us_east_1.vpn_client_cidr_block
}
