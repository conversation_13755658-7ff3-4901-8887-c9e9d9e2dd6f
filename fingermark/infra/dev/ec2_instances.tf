module "fm-dev-bitbucket-runner-1" {
  source             = "../../../modules/ec2_instance"
  ami                = "ami-08ea97f528e500b62"
  instance_type      = "t3.medium"
  subnet_id          = module.eyecue_network.public_subnet_ids[2]
  key_name           = "francium-infra-team"
  ebs_volume_size    = 20
  ebs_volume_type    = "gp3"
  security_group_ids = ["sg-0831c8b12aecdc7dc"]
  enable_ssm         = true
  instance_name      = "fm-dev-bitbucket-runner-1"

  tags = {
    Name        = "fm-dev-bitbucket-runner-1"
    Environment = "infra-dev"
    Project     = "ci-cd"
    Owner       = "platform"
  }
}