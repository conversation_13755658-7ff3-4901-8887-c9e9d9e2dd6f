output "grafana_access_key_id" {
  description = "Access Key ID for <PERSON>ana to publish to SNS"
  value       = module.platform_camera_connectivity_alerts.grafana_access_key_id
}

output "grafana_secret_access_key" {
  description = "Secret Access Key for <PERSON><PERSON> to publish to SNS"
  value       = module.platform_camera_connectivity_alerts.grafana_secret_access_key
  sensitive   = true
}

# Enhanced Whitelist VPN Gateway outputs
output "vpn_vpc_id" {
  description = "VPC ID where the VPN gateway is deployed"
  value       = module.enhanced_whitelist_vpn_gateway.vpc_id
}

output "vpn_security_group_id" {
  description = "Security group ID attached to the VPN gateway instance"
  value       = module.enhanced_whitelist_vpn_gateway.security_group_id
}

output "vpn_instance_id" {
  description = "EC2 instance ID of the VPN gateway"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_instance_id
}

output "vpn_load_balancer_arn" {
  description = "ARN of the Network Load Balancer (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.load_balancer_arn
}

output "vpn_load_balancer_dns_name" {
  description = "DNS name of the Network Load Balancer (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.load_balancer_dns_name
}

output "vpn_endpoint" {
  description = "Recommended VPN endpoint for client configurations (stable DNS name)"
  value       = module.vpn_dns_record.hostname
}

output "vpn_dns_name" {
  description = "Custom DNS name for the VPN endpoint"
  value       = module.vpn_dns_record.hostname
}

output "vpn_target_group_arn" {
  description = "ARN of the target group (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.target_group_arn
}

output "vpn_autoscaling_group_name" {
  description = "Name of the Auto Scaling Group (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.autoscaling_group_name
}

output "vpn_instance_role_name" {
  description = "Name of the IAM role attached to the VPN instance"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_instance_role_name
}

# Additional useful outputs
output "vpn_client_cidr_block" {
  description = "CIDR block used for VPN client IP addresses"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_client_cidr_block
}

output "vpn_public_ip" {
  description = "Public IP address of the VPN gateway instance"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_public_ip
}

output "vpn_private_ip" {
  description = "Private IP address of the VPN gateway instance"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_private_ip
}

# Bitbucket Runner outputs
output "bitbucket_runner_instance_id" {
  description = "Instance ID of the Bitbucket runner"
  value       = module.fm-dev-bitbucket-runner-1.instance_id
}

output "bitbucket_runner_ssm_connect_command" {
  description = "Command to connect to the Bitbucket runner via SSM"
  value       = module.fm-dev-bitbucket-runner-1.ssm_connect_command
}

output "bitbucket_runner_private_ip" {
  description = "Private IP address of the Bitbucket runner"
  value       = module.fm-dev-bitbucket-runner-1.private_ip
}

output "bitbucket_runner_public_ip" {
  description = "Public IP address of the Bitbucket runner"
  value       = module.fm-dev-bitbucket-runner-1.public_ip
}

# ===============================================
# Teleport Outputs
# ===============================================

output "teleport_alb_dns_name" {
  description = "Teleport ALB DNS name for CNAME record creation"
  value       = module.teleport.alb_dns_name
}

output "teleport_alb_zone_id" {
  description = "Teleport ALB hosted zone ID for Route53 alias records"
  value       = module.teleport.alb_zone_id
}

output "teleport_instance_id" {
  description = "Teleport EC2 instance ID"
  value       = module.teleport.instance_id
}

output "teleport_ssm_connect_command" {
  description = "AWS SSM command to connect to the Teleport instance"
  value       = module.teleport.ssm_connect_command
}

output "teleport_certificate_arn" {
  description = "ACM certificate ARN for Teleport"
  value       = module.teleport_certificate.certificate_arn
}

output "teleport_security_group_ids" {
  description = "Teleport security group IDs (ALB and EC2)"
  value       = module.teleport.security_group_ids
}

output "teleport_ssh_access_enabled" {
  description = "Whether SSH access is enabled for Teleport instance"
  value       = module.teleport.ssh_access_enabled
}

output "teleport_ansible_bucket_name" {
  description = "Name of the S3 bucket for Teleport Ansible automation"
  value       = module.teleport.ansible_bucket_name
}

output "teleport_ansible_bucket_arn" {
  description = "ARN of the S3 bucket for Teleport Ansible automation"
  value       = module.teleport.ansible_bucket_arn
}
