###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

variable "AWS_REGION" {
  default = "ap-southeast-2"
}

variable "CLIENT_NAME" {}

variable "CLIENT_ACRONYM" {}

variable "COUNTRY" {}

variable "KEYBASE" {
  type    = string
  default = "keybase:fingermark"
}

variable "default_tags" {
  type = map(string)
  default = {
    "Terraform"   = "True"
    "Environment" = "prod"
    "Squad"       = "Platform"
  }
}

variable "tags" {
  type    = map(string)
  default = {}
}

# ===============================================
# CloudWatch Alarms
# ===============================================

variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
