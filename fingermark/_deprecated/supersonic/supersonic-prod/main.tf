module "assume_role" {
  source = "../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DevAccess", "DeployerAccess", "StorageAccess"]
}

module "vanta" {
  source = "../../../modules/vanta"
}

module "vpc_flow_logs_ap_southeast_2" {
  providers = {
    aws = aws
  }
  source          = "../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source          = "../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ==========================================
# CloudWatch Alarms
# ==========================================

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "squid-proxy"  = { instance_tags = { Name = "squid-proxy" } }
    "NodWebServer" = { instance_tags = { Name = "NodWebServer" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}
