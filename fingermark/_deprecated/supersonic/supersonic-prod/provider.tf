
provider "aws" {
  region = var.AWS_REGION
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "supersonic-prod"
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "supersonic-prod"
  }
}
