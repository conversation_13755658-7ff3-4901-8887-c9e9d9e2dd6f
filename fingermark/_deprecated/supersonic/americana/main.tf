module "assume_role" {
  source            = "../../../modules/fingermark_users_assume_role"
  roles             = ["AdminAccess", "PowerAccess", "DevAccess"]
  cloudcraft_access = true
}

module "vanta" {
  source = "../../../modules/vanta"
}

module "vpc_flow_logs_ap_southeast_2" {
  providers = {
    aws = aws
  }
  source          = "../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs_me_south_1" {
  providers = {
    aws = aws.me-south-1
  }
  source          = "../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

module "ec2_instance_cw_alarms_ap_southeast_2" {
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "gocd-with-snapshot-test-1" = { instance_tags = { Name = "gocd-with-snapshot-test-1" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}
