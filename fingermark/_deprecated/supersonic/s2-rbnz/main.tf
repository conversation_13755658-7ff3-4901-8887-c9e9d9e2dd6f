module "assume_role" {
  source = "../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DevAccess", "DeployerAccess", "StorageAccess"]
}

module "vanta" {
  source = "../../../modules/vanta"
}

module "vpc_flow_logs" {
  source          = "../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}
