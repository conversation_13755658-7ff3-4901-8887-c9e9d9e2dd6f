module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}

module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "vanta" {
  source = "../../../../modules/vanta"
}

module "northvue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.product}_${var.env}_${data.aws_region.current.name}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "northvue_cloudfront_cert" {
  source = "../../../../modules/northvue_cloudfront_cert"
}

resource "aws_iam_user" "northvue_deployer" {
  name = "northvue_deployer"
  tags = var.default_tags
}

resource "aws_iam_user_policy_attachment" "northvue_deployer" {
  user       = aws_iam_user.northvue_deployer.name
  policy_arn = aws_iam_policy.northvue_deployer.arn
}

resource "aws_iam_policy" "northvue_deployer" {
  name = "northvue_deployer-iam-user-policy"

  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
		{
			"Effect": "Allow",
			"Action": [
				"ecr:BatchGet*",
				"ecr:BatchCheck*",
				"ecr:Get*",
				"ecr:Describe*",
				"ecr:List*"
			],
			"Resource": [
			    "*"
			]
		},
    {
      "Effect" : "Allow",
      "Action" : [
        "s3:List*",
        "s3:Get*",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource" : [
          "*"
      ]
    }
  ]
}
POLICY
}

module "eyecue_iot" {
  source         = "../../../../modules/eyecue_iot"
  aws_iam_user   = "eyecue-iot-creator"
  client_name    = var.product
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = data.aws_region.current.name
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

# ------- CloudWatch Log Retention Management ------- 

module "cw_log_retention_us_east_1" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
