variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Account Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Account Default Tags"
  type        = map(any)
  default = {
    Terraform   = "True"
    Product     = "Northvue"
    Environment = "Staging"
  }
}

variable "env" {
  default     = "stg"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "northvue"
  description = "Fingermark Product"
  type        = string
}