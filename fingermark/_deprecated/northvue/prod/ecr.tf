module "moonfire_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-moonfire-nvr"
  tags     = var.default_tags
}

module "moonfire_exporter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-moonfire-exporter"
  tags     = var.default_tags
}

module "triton_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-triton-client"
  tags     = var.default_tags
}

module "tracker_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-tracker-features"
  tags     = var.default_tags
}

module "frame_grabber_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-frame-grabber"
  tags     = var.default_tags
}

module "event_processor_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-event-processor"
  tags     = var.default_tags
}

module "interval_trigger_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-interval-trigger"
  tags     = var.default_tags
}

module "config_reloader_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-config-reloader"
  tags     = var.default_tags
}

module "event_toolkit_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-event-toolkit"
  tags     = var.default_tags
}

module "event_generator" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-event-generator"
  tags     = var.default_tags
}

module "event_handler_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nv-event-handler"
  tags     = var.default_tags
}

module "eyeq-credentials" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-credentials"
  tags     = var.default_tags
}
