terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "< 6.0.0" # pinning to avoid breaking changes in AWS v6+
      # and since this environment is deprecated
      # will likely not be updated to v6+.
    }
  }
}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::952449457503:role/AdminAccess"
    session_name = "NorthVue-PRD"
  }
}

provider "aws" {
  region = "ap-southeast-2"
  alias  = "ap-southeast-2"
  assume_role {
    role_arn     = "arn:aws:iam::952449457503:role/AdminAccess"
    session_name = "NorthVue-PRD"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
