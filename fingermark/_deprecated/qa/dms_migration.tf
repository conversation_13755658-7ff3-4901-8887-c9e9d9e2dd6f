##############################
# DMS Migration Resources (Temporary)
# This file contains ALL resources needed for migration
# After migration, delete this file and run terraform apply to remove all resources
##############################

##############################
# DMS IAM Roles (Required by AWS DMS)
##############################

# Create the DMS VPC role (required by AWS DMS)
resource "aws_iam_role" "dms_vpc_role" {
  name = "dms-vpc-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "dms.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Name        = "dms-vpc-role"
    Purpose     = "DMS VPC Management"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# Attach the AWS managed policy for DMS VPC management
resource "aws_iam_role_policy_attachment" "dms_vpc_role_policy" {
  role       = aws_iam_role.dms_vpc_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonDMSVPCManagementRole"
}

# IAM role for DMS task assessment (required for validation)
resource "aws_iam_role" "dms_access_for_tasks" {
  name = "dms-access-for-tasks"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "dms.amazonaws.com"
      }
    }]
  })

  tags = {
    Name        = "dms-access-for-tasks"
    Purpose     = "DMS Task Assessment"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# Attach S3 access policy for DMS assessment results
resource "aws_iam_role_policy_attachment" "dms_access_for_tasks_s3" {
  role       = aws_iam_role.dms_access_for_tasks.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

# DMS CloudWatch Logs Role
resource "aws_iam_role" "dms_cloudwatch_logs_role" {
  name = "dms-cloudwatch-logs-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "dms.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Name        = "dms-cloudwatch-logs-role"
    Purpose     = "DMS CloudWatch Logging"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# Attach the AWS managed policy for DMS CloudWatch logs
resource "aws_iam_role_policy_attachment" "dms_cloudwatch_logs_role_policy" {
  role       = aws_iam_role.dms_cloudwatch_logs_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonDMSCloudWatchLogsRole"
}

##############################
# Temporary Access for PostgreSQL Provider
##############################

# Get current public IP for temporary RDS access
data "http" "myip" {
  url = "https://ipv4.icanhazip.com"
}

# Add temporary security group rule for Terraform PostgreSQL provider
resource "aws_security_group_rule" "terraform_postgres_access" {
  type              = "ingress"
  from_port         = 5432
  to_port           = 5432
  protocol          = "tcp"
  cidr_blocks       = ["${chomp(data.http.myip.response_body)}/32"]
  security_group_id = module.eyecue_rds.security_group_id
  description       = "Temporary access for Terraform PostgreSQL provider (DELETE AFTER MIGRATION)"

  lifecycle {
    create_before_destroy = true
  }
}

##############################
# DMS User Credentials for Source Database
##############################

# Generate a random password for DMS user (without special characters)
resource "random_password" "dms_user_password" {
  length  = 32
  special = false # DMS doesn't support special characters
  upper   = true
  lower   = true
  numeric = true
}

# Store DMS user credentials in Secrets Manager
resource "aws_secretsmanager_secret" "dms_user_credentials" {
  name_prefix = "dms-migration-user-"
  description = "Credentials for DMS migration user (temporary)"

  tags = {
    Name        = "dms-migration-user-credentials"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

resource "aws_secretsmanager_secret_version" "dms_user_credentials" {
  secret_id = aws_secretsmanager_secret.dms_user_credentials.id
  secret_string = jsonencode({
    username = "dms_migration_user"
    password = random_password.dms_user_password.result
  })
}

# Build Lambda package
resource "null_resource" "lambda_build" {
  triggers = {
    source_code_hash = filebase64sha256("${path.module}/lambdas/dms_user_creation/src/lambda_function.py")
  }

  provisioner "local-exec" {
    command = "cd ${path.module}/lambdas/dms_user_creation && bash build.sh"
  }
}

# IAM role for Lambda
resource "aws_iam_role" "lambda_role" {
  name = "${local.dms_prefix}-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "dms-user-creation-lambda-role"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# Attach basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

# Lambda function
resource "aws_lambda_function" "create_dms_user" {
  filename      = "${path.module}/lambdas/dms_user_creation/function.zip"
  function_name = "${local.dms_prefix}-create-user"
  role          = aws_iam_role.lambda_role.arn
  handler       = "lambda_function.lambda_handler"
  runtime       = "python3.9"
  timeout       = 60

  vpc_config {
    subnet_ids         = module.eyecue_network.private_subnet_ids
    security_group_ids = [aws_security_group.dms_lambda_sg.id]
  }

  environment {
    variables = {
      DB_HOST         = data.aws_db_instance.existing_rds.address
      DB_PORT         = data.aws_db_instance.existing_rds.port
      DB_NAME         = "eyeq"
      MASTER_USER     = data.aws_db_instance.existing_rds.master_username
      MASTER_PASSWORD = module.eyecue_rds.master_db_instance_password
      DMS_PASSWORD    = random_password.dms_user_password.result
    }
  }

  # No layers - we'll package psycopg2 with the function

  tags = {
    Name        = "create-dms-user-lambda"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_logs,
    random_password.dms_user_password,
    null_resource.lambda_build
  ]
}

# Security group for Lambda
resource "aws_security_group" "dms_lambda_sg" {
  name        = "${local.dms_prefix}-lambda-sg"
  description = "Security group for Lambda function creating DMS user"
  vpc_id      = module.eyecue_network.vpc_id

  egress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [module.eyecue_rds.security_group_id]
  }

  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "lambda-dms-user-creation-sg"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# Allow Lambda to connect to RDS
resource "aws_security_group_rule" "lambda_to_rds" {
  type                     = "ingress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.dms_lambda_sg.id
  security_group_id        = module.eyecue_rds.security_group_id
  description              = "Allow Lambda to connect to RDS for user creation"
}

# Execute Lambda to create user
resource "null_resource" "invoke_lambda" {
  triggers = {
    lambda_function = aws_lambda_function.create_dms_user.function_name
    password_change = random_password.dms_user_password.result
  }

  provisioner "local-exec" {
    command = <<-EOT
      aws lambda invoke \
        --function-name ${aws_lambda_function.create_dms_user.function_name} \
        --profile qa \
        --region ap-southeast-2 \
        /tmp/lambda_response.json && \
      cat /tmp/lambda_response.json
    EOT
  }

  depends_on = [
    aws_lambda_function.create_dms_user,
    aws_security_group_rule.lambda_to_rds
  ]
}

##############################
# DMS Migration Resources
##############################

# Data source to reference the existing RDS instance
data "aws_db_instance" "existing_rds" {
  db_instance_identifier = module.eyecue_rds.master_db_instance_identifier
}

# Local value for DMS naming
locals {
  dms_prefix = "eyecue-${var.env}-${local.migration_suffix}"
}

# DMS Subnet Group
resource "aws_dms_replication_subnet_group" "main" {
  replication_subnet_group_description = "Subnet group for DMS replication instance"
  replication_subnet_group_id          = "${local.dms_prefix}-subnet-group"

  subnet_ids = module.eyecue_network.public_subnet_ids

  tags = {
    Name        = "${local.dms_prefix}-subnet-group"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }

  depends_on = [
    aws_iam_role.dms_vpc_role
  ]
}

# Security Group for DMS
resource "aws_security_group" "dms" {
  name        = "${local.dms_prefix}-sg"
  description = "Security group for DMS replication instance"
  vpc_id      = module.eyecue_network.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${local.dms_prefix}-sg"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# Allow DMS to connect to source RDS (unencrypted)
resource "aws_security_group_rule" "dms_to_rds" {
  type                     = "ingress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.dms.id
  security_group_id        = module.eyecue_rds.security_group_id
  description              = "Allow DMS to connect to source RDS"
}

# Allow DMS to connect to target RDS (encrypted)
resource "aws_security_group_rule" "dms_to_encrypted_rds" {
  type                     = "ingress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.dms.id
  security_group_id        = module.eyecue_rds_encrypted.security_group_id
  description              = "Allow DMS to connect to encrypted RDS"
}

# DMS Replication Instance
resource "aws_dms_replication_instance" "main" {
  replication_instance_class = "dms.t3.medium"
  replication_instance_id    = "${local.dms_prefix}-instance"
  allocated_storage          = 100
  publicly_accessible        = false
  multi_az                   = false

  vpc_security_group_ids      = [aws_security_group.dms.id]
  replication_subnet_group_id = aws_dms_replication_subnet_group.main.id

  tags = {
    Name        = "${local.dms_prefix}-instance"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

# DMS Source Endpoint (Existing Unencrypted RDS)
resource "aws_dms_endpoint" "source" {
  endpoint_id   = "${local.dms_prefix}-source"
  endpoint_type = "source"
  engine_name   = "postgres"

  server_name   = data.aws_db_instance.existing_rds.address
  port          = data.aws_db_instance.existing_rds.port
  database_name = "eyeq" # Correct database name
  username      = "dms_migration_user"
  password      = random_password.dms_user_password.result

  ssl_mode = "none"

  # Use extra_connection_attributes to configure PostgreSQL plugin
  # This ensures DMS uses test_decoding instead of pglogical
  extra_connection_attributes = "PluginName=TEST_DECODING;captureDDLs=N"

  tags = {
    Name        = "${local.dms_prefix}-source-endpoint"
    Environment = var.env
    Type        = "source"
    Terraform   = "true"
    Temporary   = "true"
  }

  depends_on = [
    null_resource.invoke_lambda
  ]
}

# DMS Target Endpoint (New Encrypted RDS)
resource "aws_dms_endpoint" "target" {
  endpoint_id   = "${local.dms_prefix}-target"
  endpoint_type = "target"
  engine_name   = "postgres"

  server_name   = module.eyecue_rds_encrypted.master_db_instance_address
  port          = module.eyecue_rds_encrypted.master_db_instance_port
  database_name = module.eyecue_rds_encrypted.master_db_instance_name
  username      = module.eyecue_rds_encrypted.master_db_instance_username
  password      = module.eyecue_rds_encrypted.master_db_instance_password

  ssl_mode = "none"

  tags = {
    Name        = "${local.dms_prefix}-target-endpoint"
    Environment = var.env
    Type        = "target"
    Terraform   = "true"
    Temporary   = "true"
  }
}

# DMS Replication Task
resource "aws_dms_replication_task" "main" {
  migration_type           = "full-load-and-cdc"
  replication_instance_arn = aws_dms_replication_instance.main.replication_instance_arn
  replication_task_id      = "${local.dms_prefix}-task"

  source_endpoint_arn = aws_dms_endpoint.source.endpoint_arn
  target_endpoint_arn = aws_dms_endpoint.target.endpoint_arn

  table_mappings = jsonencode({
    rules = [
      {
        rule-type = "selection"
        rule-id   = "1"
        rule-name = "1"
        object-locator = {
          schema-name = "%"
          table-name  = "%"
        }
        rule-action = "include"
      }
    ]
  })

  replication_task_settings = jsonencode({
    TargetMetadata = {
      TargetSchema       = ""
      SupportLobs        = true
      FullLobMode        = true
      LobChunkSize       = 64
      LimitedSizeLobMode = false
      LobMaxSize         = 32
      InlineLobMaxSize   = 8
      LoadMaxFileSize    = 102400
      BatchApplyEnabled  = false
    }
    FullLoadSettings = {
      TargetTablePrepMode           = "DROP_AND_CREATE"
      MaxFullLoadSubTasks           = 8
      TransactionConsistencyTimeout = 600
      CommitCheckpointDelay         = 10
    }
    Logging = {
      EnableLogging = true
      LogComponents = [
        {
          Id       = "DATA_STRUCTURE"
          Severity = "LOGGER_SEVERITY_DEFAULT"
        },
        {
          Id       = "TARGET_LOAD"
          Severity = "LOGGER_SEVERITY_DEFAULT"
        },
        {
          Id       = "TARGET_APPLY"
          Severity = "LOGGER_SEVERITY_DEFAULT"
        },
        {
          Id       = "SOURCE_UNLOAD"
          Severity = "LOGGER_SEVERITY_DEFAULT"
        },
        {
          Id       = "SOURCE_CAPTURE"
          Severity = "LOGGER_SEVERITY_DEFAULT"
        },
        {
          Id       = "TASK_MANAGER"
          Severity = "LOGGER_SEVERITY_DEFAULT"
        }
      ]
    }
    ControlTablesSettings = {
      ControlSchema               = ""
      HistoryTimeslotInMinutes    = 5
      HistoryTableEnabled         = false
      SuspendedTablesTableEnabled = false
      StatusTableEnabled          = false
    }
    StreamBufferSettings = {
      StreamBufferCount        = 3
      StreamBufferSizeInMB     = 8
      CtrlStreamBufferSizeInMB = 5
    }
    ChangeProcessingDdlHandlingPolicy = {
      HandleSourceTableDropped   = false # Disable DDL handling
      HandleSourceTableTruncated = false # Disable DDL handling
      HandleSourceTableAltered   = false # Disable DDL handling
    }
    ErrorBehavior = {
      DataErrorPolicy                      = "LOG_ERROR"
      DataTruncationErrorPolicy            = "LOG_ERROR"
      DataErrorEscalationPolicy            = "SUSPEND_TABLE"
      DataErrorEscalationCount             = 0
      TableErrorPolicy                     = "SUSPEND_TABLE"
      TableErrorEscalationPolicy           = "STOP_TASK"
      TableErrorEscalationCount            = 0
      RecoverableErrorCount                = -1
      RecoverableErrorInterval             = 5
      RecoverableErrorThrottling           = true
      RecoverableErrorThrottlingMax        = 1800
      ApplyErrorDeletePolicy               = "IGNORE_RECORD"
      ApplyErrorInsertPolicy               = "LOG_ERROR"
      ApplyErrorUpdatePolicy               = "LOG_ERROR"
      ApplyErrorEscalationPolicy           = "LOG_ERROR"
      ApplyErrorEscalationCount            = 0
      ApplyErrorFailOnTruncationDdl        = false
      FullLoadIgnoreConflicts              = true
      FailOnTransactionConsistencyBreached = false
      FailOnNoTablesCaptured               = false
    }
    ChangeProcessingTuning = {
      BatchApplyPreserveTransaction = true
      BatchApplyTimeoutMin          = 1
      BatchApplyTimeoutMax          = 30
      BatchApplyMemoryLimit         = 500
      BatchSplitSize                = 0
      MinTransactionSize            = 1000
      CommitTimeout                 = 1
      MemoryLimitTotal              = 1024
      MemoryKeepTime                = 60
      StatementCacheSize            = 50
    }
  })

  tags = {
    Name        = "${local.dms_prefix}-migration-task"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }

  depends_on = [
    aws_dms_endpoint.source,
    aws_dms_endpoint.target,
    aws_dms_replication_instance.main
  ]
}

##############################
# CloudWatch Alarms for DMS Monitoring
##############################

resource "aws_cloudwatch_metric_alarm" "dms_cpu" {
  alarm_name          = "${local.dms_prefix}-cpu-high"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/DMS"
  period              = "300"
  statistic           = "Average"
  threshold           = "75"
  alarm_description   = "This metric monitors DMS CPU utilization"
  alarm_actions       = [module.eyecue_sns_topic.topic_arns["cloudwatch-alarm-alert-helper-topic"]]

  dimensions = {
    ReplicationInstanceIdentifier = aws_dms_replication_instance.main.replication_instance_id
  }

  tags = {
    Name        = "${local.dms_prefix}-cpu-alarm"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_freeable_memory" {
  alarm_name          = "${local.dms_prefix}-memory-low"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "FreeableMemory"
  namespace           = "AWS/DMS"
  period              = "300"
  statistic           = "Average"
  threshold           = "500000000"
  alarm_description   = "This metric monitors DMS freeable memory"
  alarm_actions       = [module.eyecue_sns_topic.topic_arns["cloudwatch-alarm-alert-helper-topic"]]

  dimensions = {
    ReplicationInstanceIdentifier = aws_dms_replication_instance.main.replication_instance_id
  }

  tags = {
    Name        = "${local.dms_prefix}-memory-alarm"
    Environment = var.env
    Terraform   = "true"
    Temporary   = "true"
  }
}

##############################
# Outputs for Migration
##############################

# Encrypted RDS Instance Outputs
output "encrypted_rds_instance_id" {
  description = "The ID of the encrypted RDS instance"
  value       = module.eyecue_rds_encrypted.master_db_instance_identifier
}

output "encrypted_rds_instance_endpoint" {
  description = "The connection endpoint of the encrypted RDS instance"
  value       = module.eyecue_rds_encrypted.master_db_instance_endpoint
}

output "encrypted_rds_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing RDS credentials"
  value       = module.eyecue_rds_encrypted.secret_arn
}

# DMS Outputs
output "dms_replication_instance_id" {
  description = "The ID of the DMS replication instance"
  value       = aws_dms_replication_instance.main.replication_instance_id
}

output "dms_replication_task_id" {
  description = "The ID of the DMS replication task"
  value       = aws_dms_replication_task.main.replication_task_id
}

output "dms_replication_task_arn" {
  description = "The ARN of the DMS replication task"
  value       = aws_dms_replication_task.main.replication_task_arn
}

# Migration Status Information
output "migration_instructions" {
  description = "Instructions for completing the migration"
  value       = <<-EOT
    ======================================
    DMS Migration Setup Complete!
    ======================================
    
    STEP 1: Start the DMS replication task
    ----------------------------------------
    aws dms start-replication-task \
      --replication-task-arn ${aws_dms_replication_task.main.replication_task_arn} \
      --start-replication-task-type start-replication \
      --profile qa \
      --region ap-southeast-2
    
    STEP 2: Monitor the migration
    ----------------------------------------
    - AWS DMS Console: Check task progress
    - CloudWatch Alarms: Monitor CPU and memory
    
    STEP 3: After migration is complete
    ----------------------------------------
    1. Test the encrypted RDS instance
    2. Update application connection strings
    3. Stop the DMS task
    
    STEP 4: Cleanup (AFTER validation)
    ----------------------------------------
    1. Delete this file: dms_migration.tf
    2. Run: terraform apply
    3. All DMS resources will be removed
    
    Connection Details:
    - Encrypted RDS: ${module.eyecue_rds_encrypted.master_db_instance_endpoint}
    - Secret ARN: ${module.eyecue_rds_encrypted.secret_arn}
    
    DMS User Credentials (stored in Secrets Manager):
    - Secret: ${aws_secretsmanager_secret.dms_user_credentials.name}
  EOT
}