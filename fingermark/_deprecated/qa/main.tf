module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

module "common" {
  source         = "../../../modules/common"
  aws_account_id = data.aws_caller_identity.current.account_id
  # trusted_aws_account_id = "************"
  aws_region     = var.AWS_REGION
  client_name    = var.CLIENT_NAME
  client_acronym = var.CLIENT_ACRONYM
  country        = var.COUNTRY
  country_full   = var.COUNTRY_FULL
  aws_iam_roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
  keybase        = var.KEYBASE
  env            = var.ENVIRONMENT
}

module "eyecue_notification_service" {
  source         = "../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

module "eyecue_sns_topic" {
  source     = "../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_benchmarker" {
  source          = "../../../modules/eyecue_benchmarker"
  aws_iam_user    = "eyecue-benchmarker"
  keybase         = var.KEYBASE
  allowed_methods = ["GET", "POST", "DELETE"]
  client_name     = var.CLIENT_NAME
  aws_account_id  = data.aws_caller_identity.current.account_id
  aws_region      = var.AWS_REGION
  country         = var.COUNTRY
}

module "eyecue_network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "eyecue_rds" {
  source                        = "../../../modules/eyecue_rds"
  eyecue_rds_stage_name         = var.env
  create_replica                = false
  rds_apply_changes_immediately = true
  rds_master_instance_class     = var.RDS_MASTER_INSTANCE_CLASS
  rds_engine_version            = "16.8"
  rds_ca_cert_identifier        = "rds-ca-rsa2048-g1"
  special_password              = true
  aws_region                    = var.AWS_REGION
  aws_account_id                = data.aws_caller_identity.current.account_id
  product                       = "Eyecue"
  vpc_id                        = module.eyecue_network.vpc_id
  subnet_ids                    = module.eyecue_network.public_subnet_ids
  eyecue_rds_cloudflare_api_key = data.vault_generic_secret.cloudflare.data["api_key"]
  create_db_parameter_group     = true
  parameter_group_family        = "postgres16"
  parameter_group_parameters = [ # Explicitly disable forced SSL
    {
      name  = "rds.force_ssl"
      value = "0"
    },
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "wal_sender_timeout"
      value        = "30000"
      apply_method = "immediate"
    }
  ]
}

module "eyecue_mimir" {
  source                   = "../../../modules/eyecue_mimir"
  mimir_aws_account_id     = "************"
  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "This policy is used to invoke the request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}

module "helm_repo" {
  enable_versioning = true
  source            = "../../../modules/s3_bucket"
  bucket_name       = "eyecue-helm-${var.CLIENT_NAME}-package"
  tags = {
    Terraform   = "true"
    Environment = "qa"
    Stack       = "cicd"
    Product     = "Eyecue"
  }
}

module "helm_repo_iam" {
  source                 = "../../../modules/s3_bucket_iam"
  aws_iam_user           = "helm-deployer"
  keybase                = var.KEYBASE
  iam_policy_prefix_name = "EyecueHelmPackageSyncIAM"
  bucket_resources_arn_list = [
    "${module.helm_repo.bucket_arn}",
    "${module.helm_repo.bucket_arn}/*"
  ]
  tags = {
    Terraform   = "true"
    Environment = "qa"
    Stack       = "cicd"
    Product     = "Eyecue"
  }
}

resource "aws_security_group" "lambda_sg" {
  name        = "${var.CLIENT_NAME}-lambda-sg"
  description = "${var.CLIENT_NAME}-lambda sgs for lambdas"
  vpc_id      = module.eyecue_network.vpc_id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.default_tags, var.vpc_tags)
}

# ==========================================
# SOC2 Security
# ==========================================

module "vanta" {
  source = "../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source        = "../../../modules/sqs_monitoring"
  sns_topic_arn = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_east_1" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
  providers = {
    aws = aws.us-east-1
  }
}

# ------- S3 Public Access Block -------

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

module "dynamodb_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }

  source               = "../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

# ------- RDS Monitoring -------

module "rds_monitoring_ap_southeast_2" {
  source               = "../../../modules/rds_monitoring"
  lambda_function_name = "rds-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "RDSMonitoring"
    Environment = "QA"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

##############################
# Encrypted RDS Instance (Permanent)
# This replaces the unencrypted RDS after migration
##############################

# Generate a random suffix for unique resource names
resource "random_id" "rds_suffix" {
  byte_length = 4
}

# Local values for consistent naming
locals {
  migration_suffix = random_id.rds_suffix.hex
  rds_identifier   = "eyecue-${var.env}-encrypted-${local.migration_suffix}"
}

# Create new encrypted RDS instance using the new module
module "eyecue_rds_encrypted" {
  source = "../../../modules/eyecue_rds_encrypted"

  # Basic Configuration
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION

  # Instance Identification
  rds_instance_identifier = local.rds_identifier
  name_prefix             = "eyecue"
  name_suffix             = "${var.env}-encrypted-${local.migration_suffix}"
  environment             = var.env

  # Database Configuration
  rds_name           = "eyecue"
  rds_username       = "eyecue_admin"
  rds_engine         = "postgres"
  rds_engine_version = "16.8"
  rds_port           = 5432

  # Instance Specifications (matching existing)
  rds_master_instance_class = var.RDS_MASTER_INSTANCE_CLASS
  rds_allocated_storage     = 100
  rds_max_allocated_storage = 1000
  rds_storage_type          = "gp3"
  # rds_master_iops not specified - will use gp3 baseline performance

  # Encryption Settings
  rds_storage_encrypted = true

  # Network Configuration
  vpc_id                 = module.eyecue_network.vpc_id
  subnet_ids             = module.eyecue_network.public_subnet_ids
  vpc_security_group_ids = []
  db_publicly_accessible = true

  # Backup and Maintenance
  rds_backup_retention_period = 7
  rds_backup_window           = "03:00-04:00"
  rds_maintenance_window      = "Mon:04:00-Mon:05:00"
  rds_skip_final_snapshot     = true
  deletion_protection         = false
  multi_az                    = false

  # Apply changes immediately for testing
  rds_apply_changes_immediately = true

  # Security and Authentication
  iam_database_authentication_enabled = true
  create_random_password              = true
  create_secrets_manager_secret       = true
  special_password                    = false # DMS doesn't support special characters

  # Parameter Group Settings
  create_db_parameter_group = true
  parameter_group_name      = local.rds_identifier
  parameter_group_family    = "postgres16"
  # DMS requires logical_replication enabled
  # When rds.logical_replication is set to 1, AWS automatically configures:
  # - wal_level, max_wal_senders, max_replication_slots, max_connections
  parameter_group_parameters = [
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot" # Static parameter - requires reboot
    },
    {
      name         = "rds.force_ssl"
      value        = "0"              # Disabled for DMS compatibility
      apply_method = "pending-reboot" # Static parameter - requires reboot
    },
    {
      name         = "wal_sender_timeout"
      value        = "30000"     # 30 seconds - recommended for DMS CDC
      apply_method = "immediate" # Dynamic parameter
    }
  ]

  # Don't create replica for now
  create_replica = false

  # IAM Configuration
  create_iam_role   = true
  create_iam_policy = true

  # Tags
  tags = {
    Terraform   = "true"
    Environment = var.env
    Product     = "Eyecue"
    Purpose     = "Production-Encrypted"
    Encrypted   = "true"
  }
}
