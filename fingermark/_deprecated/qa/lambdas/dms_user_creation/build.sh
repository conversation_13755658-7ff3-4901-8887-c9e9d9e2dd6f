#!/bin/bash
set -e

# Clean up previous builds
rm -rf package
rm -f function.zip

# Create package directory
mkdir package

# Install dependencies
pip3 install --target ./package -r requirements.txt --platform manylinux2014_x86_64 --only-binary=:all: --python-version 3.9

# Copy function code
cp src/lambda_function.py package/

# Create deployment package
cd package
zip -r ../function.zip . -q
cd ..

echo "Lambda package created: function.zip"