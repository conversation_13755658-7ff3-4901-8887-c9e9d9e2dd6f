import json
import os
import subprocess
import sys

# Install psycopg2-binary at runtime if not present
try:
    import psycopg2
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "psycopg2-binary", "-t", "/tmp/"])
    sys.path.insert(0, '/tmp/')
    import psycopg2

def lambda_handler(event, context):
    """
    Creates or updates DMS migration user in RDS PostgreSQL
    """
    # Get connection parameters from environment
    host = os.environ['DB_HOST']
    port = os.environ['DB_PORT']
    database = os.environ['DB_NAME']
    master_user = os.environ['MASTER_USER']
    master_password = os.environ['MASTER_PASSWORD']
    dms_password = os.environ['DMS_PASSWORD']
    
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=master_user,
            password=master_password,
            sslmode='require'
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Try to create user, if exists, alter password
        try:
            cursor.execute(f"CREATE USER dms_migration_user WITH LOGIN PASSWORD '{dms_password}';")
            print("Created new DMS user")
        except Exception as e:
            if 'already exists' in str(e) or 'duplicate' in str(e).lower():
                cursor.execute(f"ALTER USER dms_migration_user WITH PASSWORD '{dms_password}';")
                print("Updated existing DMS user password")
            else:
                raise e
        
        # Grant necessary permissions
        cursor.execute("GRANT CONNECT ON DATABASE eyeq TO dms_migration_user;")
        cursor.execute("GRANT USAGE ON SCHEMA public TO dms_migration_user;")
        cursor.execute("GRANT SELECT ON ALL TABLES IN SCHEMA public TO dms_migration_user;")
        cursor.execute("ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO dms_migration_user;")
        
        # Grant replication role through rds_replication
        try:
            cursor.execute("GRANT rds_replication TO dms_migration_user;")
            print("Granted rds_replication role")
        except Exception as e:
            print(f"Note: Grant rds_replication: {str(e)}")
        
        # Try to ensure user has replication capability
        try:
            cursor.execute("ALTER USER dms_migration_user WITH REPLICATION;")
            print("Ensured replication capability")
        except Exception as e:
            print(f"Note: Could not alter replication attribute: {str(e)}")
            # This is expected if the user already has replication or we don't have permission
        
        cursor.close()
        conn.close()
        
        return {
            'statusCode': 200,
            'body': json.dumps('DMS user created/updated successfully')
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error: {str(e)}')
        }