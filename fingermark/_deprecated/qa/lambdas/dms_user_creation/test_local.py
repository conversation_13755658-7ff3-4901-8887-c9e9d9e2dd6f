#!/usr/bin/env python3
"""
Local test script for DMS user creation Lambda
This helps test the logic without deploying to AWS
"""

import json
import os
import sys

# Add src to path
sys.path.insert(0, 'src')

def test_lambda():
    """Test the Lambda function locally"""
    
    # Set test environment variables
    # Note: Replace these with actual values for testing
    test_env = {
        'DB_HOST': 'master-postgres.cde8zcvlxax0.ap-southeast-2.rds.amazonaws.com',
        'DB_PORT': '5432',
        'DB_NAME': 'eyeq',
        'MASTER_USER': 'eyecue',
        'MASTER_PASSWORD': 'YOUR_MASTER_PASSWORD',  # Get from Terraform or Secrets Manager
        'DMS_PASSWORD': 'TestPassword123'
    }
    
    # Set environment variables
    for key, value in test_env.items():
        os.environ[key] = value
    
    # Import after setting env vars
    from lambda_function import lambda_handler
    
    # Test event
    event = {}
    context = {}
    
    # Run the handler
    result = lambda_handler(event, context)
    
    print("Lambda execution result:")
    print(json.dumps(result, indent=2))
    
    return result

if __name__ == "__main__":
    print("Testing DMS User Creation Lambda locally...")
    print("=" * 50)
    
    # Check if we have the password
    if 'YOUR_MASTER_PASSWORD' in os.environ.get('MASTER_PASSWORD', 'YOUR_MASTER_PASSWORD'):
        print("\nWARNING: You need to set the actual master password in the test_env dict")
        print("You can get it from: terraform output -raw master_rds_password")
        sys.exit(1)
    
    test_lambda()