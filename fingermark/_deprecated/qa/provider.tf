
provider "aws" {
  region = "ap-southeast-2"
  assume_role {
    # The role AR<PERSON> within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "qa"
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "qa"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
