terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "< 6.0.0" # pinning to avoid breaking changes in AWS v6+
      # and since this environment is deprecated 
      # will likely not be updated to v6+.
    }
  }
}

provider "aws" {
  region = var.AWS_REGION
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fm-dev"
  }
}
